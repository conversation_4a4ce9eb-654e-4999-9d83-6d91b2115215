<template>
  <view class="container">
    <!-- 使用统一的返回按钮组件 -->
    <common-back-button></common-back-button>
    
    <!-- 页面标题 -->
    <view class="header">
      <text class="title">提交维修单</text>
    </view>

    <!-- 表单区域 -->
    <view class="form-container">
      <!-- 联系人和联系电话放在一行 -->
      <view class="form-item">
        <view style="display: flex; justify-content: space-between;">
          <view style="width: 48%;">
            <text class="label">联系人<text class="required">*</text></text>
            <view class="input-wrapper" :style="{'border-color': formErrors.contactName ? '#ff4d4f' : '#e0e0e0'}">
              <input
                v-model="formData.contactName"
                class="input"
                placeholder="请输入联系人姓名"
                placeholder-class="placeholder"
                @input="validateField('contactName')"
              />
            </view>
          </view>
          <view style="width: 48%;">
            <text class="label">联系电话<text class="required">*</text></text>
            <view class="input-wrapper" :style="{'border-color': formErrors.contactPhone ? '#ff4d4f' : '#e0e0e0'}">
              <input
                v-model="formData.contactPhone"
                type="number"
                class="input"
                placeholder="请输入联系电话"
                placeholder-class="placeholder"
                maxlength="11"
                @input="validateField('contactPhone')"
              />
            </view>
          </view>
        </view>
      </view>

      <!-- 维修地址 -->
      <view class="form-item">
        <text class="label">维修地址<text class="required">*</text></text>
        <address-input
          v-model="formData.addressInfo"
          @addressChange="handleAddressChange"
          :config="{
            required: true,
            placeholder: '请输入维修地址',
            showCurrentLocation: true,
            showMapPicker: false,
            maxDetailLength: 100
          }"
        />
      </view>

      <!-- 预约时间和是否紧急放在一行 -->
      <view class="form-item">
        <view style="display: flex; justify-content: space-between;">
          <view style="width: 65%;">
            <text class="label">预约时间</text>
            <view class="input-wrapper date-wrapper" @click="showDatePicker">
              <text class="date-text" :style="{color: formData.appointmentTime ? '#333333' : '#999999'}">
                {{ formData.appointmentTime || '请选择预约时间' }}
              </text>
              <text class="date-arrow">›</text>
            </view>
          </view>
          <view style="width: 30%;">
            <text class="label">是否紧急</text>
            <view class="switch-wrapper">
              <switch
                :checked="formData.urgent"
                @change="handleSwitchChange"
                color="#07c160"
              />
              <text class="switch-text">{{ formData.urgent ? '紧急' : '普通' }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 维修描述 -->
      <view class="form-item">
        <text class="label">维修内容描述<text class="required">*</text></text>
        <view class="input-wrapper textarea-wrapper" :style="{'border-color': formErrors.description ? '#ff4d4f' : '#e0e0e0'}">
          <textarea
            v-model="formData.description"
            class="textarea"
            placeholder="请详细描述您的维修需求"
            placeholder-class="placeholder"
            maxlength="500"
            @input="validateField('description')"
          />
          <view class="word-count">{{ formData.description.length }}/500</view>
        </view>
      </view>

      <!-- 提交按钮 -->
      <button
        class="submit-btn"
        :class="{'btn-loading': loading}"
        :disabled="loading"
        @click="submitOrder"
      >
        <text v-if="loading">提交中...</text>
        <text v-else>提交订单</text>
      </button>
    </view>

    <!-- 重新实现日期选择器 -->
    <view class="date-picker-mask" v-if="showPicker" @click.stop="closeDatePicker"></view>
    <view class="date-picker-container" v-if="showPicker">
      <view class="date-picker-header">
        <view class="date-picker-action" @click.stop="closeDatePicker">取消</view>
        <view class="date-picker-title">选择预约时间</view>
        <view class="date-picker-action confirm" @click.stop="confirmDatePicker">确定</view>
      </view>
      <picker-view
        class="date-picker-view"
        :value="tempDateTimeArray"
        @change="handlePickerViewChange"
        indicator-style="height: 88rpx; border-top: 1px solid #eee; border-bottom: 1px solid #eee;"
        :mask-style="maskStyle"
      >
        <picker-view-column>
          <view class="date-picker-item" v-for="(item, index) in dateTimeSelector[0]" :key="index">{{ item }}</view>
        </picker-view-column>
        <picker-view-column>
          <view class="date-picker-item" v-for="(item, index) in dateTimeSelector[1]" :key="index">{{ item }}</view>
        </picker-view-column>
        <picker-view-column>
          <view class="date-picker-item" v-for="(item, index) in dateTimeSelector[2]" :key="index">{{ item }}</view>
        </picker-view-column>
        <picker-view-column>
          <view class="date-picker-item" v-for="(item, index) in dateTimeSelector[3]" :key="index">{{ item }}</view>
        </picker-view-column>
        <picker-view-column>
          <view class="date-picker-item" v-for="(item, index) in dateTimeSelector[4]" :key="index">{{ item }}</view>
        </picker-view-column>
      </picker-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useUserStore } from '@/store/user';
import { type CreateOrderParams, orderApi, type AddressInfo } from '@/api/order';
import CommonBackButton from '@/components/common/BackButton.vue';
import AddressInput from '@/components/address/AddressInput.vue';
import type { AddressData } from '@/types/location';

const userStore = useUserStore();
const loading = ref(false);

// 表单数据
const formData = reactive({
  description: '',
  contactName: '',
  contactPhone: '',
  urgent: false,
  appointmentTime: '',
  // 结构化地址字段
  addressInfo: null as AddressData | null
});

// 表单错误状态
const formErrors = reactive({
  contactName: false,
  contactPhone: false,
  address: false,
  description: false
});

// 日期时间选择器相关
const showPicker = ref(false);
const dateTimeSelector = ref<string[][]>([]);
const dateTimeArray = ref<number[]>([0, 0, 0, 0, 0]);

// 临时存储当前选择的日期时间
const tempDateTimeArray = ref<number[]>([0, 0, 0, 0, 0]);
const maskStyle = ref<string>('background-size: 100% 88px; background-image: linear-gradient(180deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.4)), linear-gradient(0deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.4));');

// 处理地址变化
function handleAddressChange(addressData: AddressData) {
  formData.addressInfo = addressData;
  // 重新验证地址字段
  validateField('address');
}

// 实时验证单个字段
function validateField(field: string) {
  switch (field) {
    case 'contactName':
      formErrors.contactName = !formData.contactName.trim();
      break;
    case 'contactPhone':
      formErrors.contactPhone = !formData.contactPhone || !/^1\d{10}$/.test(formData.contactPhone);
      break;
    case 'address':
      formErrors.address = !formData.addressInfo ||
                          !formData.addressInfo.detailAddress ||
                          !formData.addressInfo.detailAddress.trim();
      break;
    case 'description':
      formErrors.description = !formData.description.trim() || formData.description.trim().length < 5;
      break;
  }
}

// 初始化表单数据，设置默认值和日期选择器数据
onMounted(async () => {
  try {
    // 如果用户信息不存在，则获取用户信息
    if (!userStore.userInfo) {
      await userStore.getUserInfo();
    }
    
    // 设置默认值
    if (userStore.userInfo) {
      formData.contactName = userStore.userInfo.username || '';
      formData.contactPhone = userStore.userInfo.phone || '';
      // 如果用户有地址信息，设置为默认值
      if (userStore.userInfo.addressInfo) {
        formData.addressInfo = userStore.userInfo.addressInfo;
      }
    }

    // 初始化日期时间选择器
    initDateTimeSelector();
  } catch (error) {
    console.error('获取用户信息失败', error);
  }
});

// 初始化日期时间选择器
function initDateTimeSelector() {
  const now = new Date();
  // 创建一个表示默认时间的变量（当前时间加1小时）
  const defaultTime = new Date(now.getTime() + 60 * 60 * 1000);
  
  const yearStart = now.getFullYear();
  const yearEnd = yearStart + 1;
  
  // 年份选项（当前年和下一年）
  const years = [];
  for (let i = yearStart; i <= yearEnd; i++) {
    years.push(i + '年');
  }
  
  // 月份选项（1-12月）
  const months = [];
  for (let i = 1; i <= 12; i++) {
    months.push(i + '月');
  }
  
  // 日期选项（1-31日，根据月份动态调整）
  const days = [];
  const daysInMonth = new Date(defaultTime.getFullYear(), defaultTime.getMonth() + 1, 0).getDate();
  for (let i = 1; i <= daysInMonth; i++) {
    days.push(i + '日');
  }
  
  // 小时选项（0-23点，全天可选）
  const hours = [];
  for (let i = 0; i <= 23; i++) {
    hours.push(i + '点');
  }
  
  // 分钟选项（0, 15, 30, 45分）
  const minutes = ['00分', '15分', '30分', '45分'];
  
  dateTimeSelector.value = [years, months, days, hours, minutes];
  
  // 设置默认选中的日期时间（当前时间加1小时，向上取整到最近的15分钟间隔）
  const defaultYear = defaultTime.getFullYear();
  const defaultMonth = defaultTime.getMonth(); // 0-11
  const defaultDay = defaultTime.getDate() - 1; // 1-31
  const defaultHour = defaultTime.getHours();
  
  // 计算年份索引
  const yearIndex = defaultYear - yearStart;
  
  // 计算小时索引（0-23小时）
  const hourIndex = defaultHour;
  
  // 计算分钟索引（向上取整到15分钟间隔）
  const defaultMinute = defaultTime.getMinutes();
  let minuteIndex = 0;
  if (defaultMinute < 15) minuteIndex = 0;
  else if (defaultMinute < 30) minuteIndex = 1;
  else if (defaultMinute < 45) minuteIndex = 2;
  else minuteIndex = 3;
  
  // 设置默认选中值
  dateTimeArray.value = [yearIndex, defaultMonth, defaultDay, hourIndex, minuteIndex];
  
  // 设置初始表单值
  const selectedDate = new Date(defaultYear, defaultMonth, defaultDay + 1, defaultHour, minuteIndex * 15);
  formData.appointmentTime = formatDate(selectedDate);
}

// 处理picker-view变化
function handlePickerViewChange(e: any) {
  const values = e.detail.value;
  tempDateTimeArray.value = values;
  
  // 根据年月动态调整日期选项
  updateDaysForSelectedMonth(values[0], values[1]);
}

// 根据选择的年月更新日期选项
function updateDaysForSelectedMonth(yearIndex: number, monthIndex: number) {
  const year = parseInt(dateTimeSelector.value[0][yearIndex]);
  const month = parseInt(dateTimeSelector.value[1][monthIndex]);
  
  // 计算所选月份的天数
  const daysInMonth = new Date(year, month, 0).getDate();
  
  // 更新日期选项
  const newDays = [];
  for (let i = 1; i <= daysInMonth; i++) {
    newDays.push(i + '日');
  }
  
  // 更新日期选择器
  const newDateTimeSelector = [...dateTimeSelector.value];
  newDateTimeSelector[2] = newDays;
  dateTimeSelector.value = newDateTimeSelector;
  
  // 如果当前选中的日期超出了所选月份的天数，则调整为最后一天
  if (tempDateTimeArray.value[2] >= daysInMonth) {
    tempDateTimeArray.value[2] = daysInMonth - 1;
  }
}

// 确认选择
function confirmDatePicker() {
  dateTimeArray.value = [...tempDateTimeArray.value];
  
  // 根据选择更新日期
  const year = parseInt(dateTimeSelector.value[0][dateTimeArray.value[0]]);
  const month = parseInt(dateTimeSelector.value[1][dateTimeArray.value[1]]);
  const day = parseInt(dateTimeSelector.value[2][dateTimeArray.value[2]]);
  const hour = parseInt(dateTimeSelector.value[3][dateTimeArray.value[3]]);
  const minute = parseInt(dateTimeSelector.value[4][dateTimeArray.value[4]]) || 0;
  
  const selectedDate = new Date(year, month - 1, day, hour, minute);
  formData.appointmentTime = formatDate(selectedDate);
  
  // 关闭选择器
  closeDatePicker();
}

// 显示日期选择器
function showDatePicker() {
  // 将当前选中值复制到临时数组
  tempDateTimeArray.value = [...dateTimeArray.value];
  showPicker.value = true;
}

// 关闭日期选择器
function closeDatePicker() {
  showPicker.value = false;
}

// 日期格式化
function formatDate(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 表单验证
function validateForm(): boolean {
  if (!formData.contactName.trim()) {
    uni.showToast({
      title: '请填写联系人',
      icon: 'none'
    });
    return false;
  }
  
  if (!formData.contactPhone || !/^1\d{10}$/.test(formData.contactPhone)) {
    uni.showToast({
      title: '请填写正确的联系电话',
      icon: 'none'
    });
    return false;
  }
  
  if (!formData.addressInfo) {
    uni.showToast({
      title: '请填写维修地址',
      icon: 'none'
    });
    return false;
  }

  // 检查详细地址是否填写（因为配置了required: true）
  if (!formData.addressInfo.detailAddress || !formData.addressInfo.detailAddress.trim()) {
    uni.showToast({
      title: '请填写详细地址信息',
      icon: 'none'
    });
    return false;
  }
  
  if (!formData.description.trim()) {
    uni.showToast({
      title: '请填写维修内容描述',
      icon: 'none'
    });
    return false;
  }
  
  if (formData.description.trim().length < 5 || formData.description.trim().length > 500) {
    uni.showToast({
      title: '描述长度应在5-500字符之间',
      icon: 'none'
    });
    return false;
  }
  
  return true;
}

// 提交订单
async function submitOrder() {
  if (!validateForm()) {
    return;
  }

  loading.value = true;
  try {
    const orderData: CreateOrderParams = {
      description: formData.description,
      contactName: formData.contactName,
      contactPhone: formData.contactPhone,
      appointmentTime: formData.appointmentTime,
      urgent: formData.urgent ? 1 : 0,  // 将boolean转换为number: true->1, false->0
      // 结构化地址信息
      addressInfo: {
        province: formData.addressInfo!.province,
        city: formData.addressInfo!.city,
        district: formData.addressInfo!.district,
        street: formData.addressInfo!.street,
        streetNumber: formData.addressInfo!.streetNumber,
        detailAddress: formData.addressInfo!.detailAddress,
        formattedAddress: formData.addressInfo!.formattedAddress,
        latitude: formData.addressInfo!.latitude,
        longitude: formData.addressInfo!.longitude,
        accuracy: formData.addressInfo!.accuracy
      }
    };

    await orderApi.createOrder(orderData);

    uni.showToast({
      title: '提交成功',
      icon: 'success',
      duration: 1500
    });

    // 改为1.5秒后跳转到订单列表页（tabBar页面需要使用switchTab）
    setTimeout(() => {
      uni.switchTab({
        url: '/pages/user/order-list'
      });
    }, 1500);
  } catch (error: any) {
    console.error('提交订单失败:', error);
    // 显示服务器返回的具体错误信息
    uni.showToast({
      title: error.message || '提交失败，请重试',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
}

// 处理switch切换
function handleSwitchChange(e: any) {
  formData.urgent = e.detail.value;
}
</script>

<style>
.container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 20rpx;
}

.header {
  position: relative;
  height: 80rpx;
  margin-top: 180rpx; /* 为返回按钮留出空间 */
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #ebeef5;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 适配各种机型屏幕尺寸 */
/* #ifdef MP-WEIXIN */
.header {
  margin-top: 200rpx; /* 微信小程序中额外增加顶部边距 */
}
/* #endif */

.title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.form-container {
  margin: 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.form-item {
  margin-bottom: 20rpx;
}

.label {
  font-size: 26rpx;
  color: #333333;
  margin-bottom: 10rpx;
  display: block;
  font-weight: 500;
}

.required {
  color: #ff4d4f;
  margin-left: 4rpx;
}

.input-wrapper {
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  overflow: hidden;
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #07c160;
  box-shadow: 0 0 0 2rpx rgba(7, 193, 96, 0.1);
}

.input {
  height: 70rpx;
  font-size: 26rpx;
  color: #333333;
  padding: 0 24rpx;
  width: 100%;
}

.textarea-wrapper {
  position: relative;
  padding-bottom: 40rpx;
}

.textarea {
  width: 100%;
  height: 120rpx;
  font-size: 26rpx;
  color: #333333;
  padding: 16rpx;
  line-height: 1.4;
}

.address-area {
  height: 80rpx;
}

.word-count {
  position: absolute;
  right: 16rpx;
  bottom: 12rpx;
  font-size: 20rpx;
  color: #999999;
}

.switch-wrapper {
  display: flex;
  align-items: center;
  height: 60rpx;
}

.switch-text {
  margin-left: 16rpx;
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
}

.date-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
  height: 70rpx;
}

.date-text {
  font-size: 26rpx;
  color: #333333;
  font-weight: normal;
}

.date-arrow {
  font-size: 32rpx;
  color: #07c160;
  transform: rotate(90deg);
}

.submit-btn {
  margin-top: 30rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 30rpx;
  color: #ffffff;
  background: linear-gradient(to right, #07c160, #10ad7a);
  border-radius: 40rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.2);
  transition: all 0.3s ease;
}

.submit-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.btn-loading {
  opacity: 0.7;
}

.placeholder {
  color: #999999;
}

/* 日期选择器样式 */
.date-picker-mask {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9998;
}

.date-picker-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  z-index: 9999;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.date-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.date-picker-action {
  font-size: 28rpx;
  color: #666666;
  padding: 10rpx;
}

.date-picker-action.confirm {
  color: #07c160;
  font-weight: 500;
}

.date-picker-title {
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
}

.date-picker-view {
  width: 100%;
  height: 400rpx;
}

.date-picker-item {
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333333;
}

/* 添加深色模式支持 */
@media (prefers-color-scheme: dark) {
  .container {
    background-color: #1a1a1a;
  }
  
  .header, .form-container {
    background-color: #2a2a2a;
    border-color: #3a3a3a;
  }
  
  .title, .label, .input, .textarea, .date-text, .switch-text {
    color: #e0e0e0;
  }
  
  .input-wrapper, .date-picker-container, .date-picker-header {
    border-color: #3a3a3a;
    background-color: #2a2a2a;
  }
  
  .date-picker-title, .date-picker-item {
    color: #e0e0e0;
  }
  
  .date-picker-action {
    color: #a0a0a0;
  }
  
  .submit-btn {
    background: linear-gradient(to right, #07c160, #0d9c6d);
  }
}
</style> 