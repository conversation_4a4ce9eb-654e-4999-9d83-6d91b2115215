package com.repair.exception;

/**
 * 业务异常类
 */
public class BusinessException extends RuntimeException {
    
    /**
     * 错误码
     */
    private Integer code;
    
    /**
     * 默认构造函数
     */
    public BusinessException() {
        super();
        this.code = 400;
    }
    
    /**
     * 使用错误信息构造异常
     * @param message 错误信息
     */
    public BusinessException(String message) {
        super(message);
        this.code = 400;
    }
    
    /**
     * 使用错误信息和错误码构造异常
     * @param message 错误信息
     * @param code 错误码
     */
    public BusinessException(String message, Integer code) {
        super(message);
        this.code = code;
    }
    
    /**
     * 使用错误信息、原因和错误码构造异常
     * @param message 错误信息
     * @param cause 异常原因
     * @param code 错误码
     */
    public BusinessException(String message, Throwable cause, Integer code) {
        super(message, cause);
        this.code = code;
    }
    
    /**
     * 获取错误码
     * @return 错误码
     */
    public Integer getCode() {
        return this.code;
    }
} 