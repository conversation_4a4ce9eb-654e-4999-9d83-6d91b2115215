package com.repair.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 维修订单实体类
 */
@Data
@Accessors(chain = true)
@TableName("repair_order")
public class RepairOrder {
    /**
     * 订单ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 订单号
     */
    private String orderId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 维修师ID
     */
    private Long repairerId;
    
    /**
     * 维修问题描述
     */
    private String description;
    
    /**
     * 联系人姓名
     */
    private String contactName;
    
    /**
     * 联系电话
     */
    private String contactPhone;
    
    /**
     * 维修地址纬度
     */
    private BigDecimal latitude;

    /**
     * 维修地址经度
     */
    private BigDecimal longitude;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区县
     */
    private String district;

    /**
     * 街道
     */
    private String street;

    /**
     * 详细地址
     */
    private String detailAddress;

    /**
     * 格式化地址
     */
    private String formattedAddress;
    
    /**
     * 订单状态：
     * 0-待处理 {@link com.repair.enums.OrderStatusEnum#PENDING}
     * 1-已接单 {@link com.repair.enums.OrderStatusEnum#ACCEPTED}
     * 2-处理中 {@link com.repair.enums.OrderStatusEnum#PROCESSING}
     * 3-已完成 {@link com.repair.enums.OrderStatusEnum#COMPLETED}
     * 4-已取消 {@link com.repair.enums.OrderStatusEnum#CANCELED}
     */
    private Integer status;
    
    /**
     * 是否紧急：0-普通，1-紧急
     */
    private Boolean urgent;
    
    /**
     * 评分：1-5星
     */
    private Integer rating;
    
    /**
     * 评价内容
     */
    private String comment;
    
    /**
     * 预约维修时间
     */
    private LocalDateTime appointmentTime;
    
    /**
     * 订单创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 订单更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    /**
     * 订单完成时间
     */
    private LocalDateTime completeTime;
} 