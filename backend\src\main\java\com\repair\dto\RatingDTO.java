package com.repair.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 订单评价数据传输对象
 */
@Data
public class RatingDTO {
    /**
     * 评分，1-5星
     */
    @NotNull(message = "评分不能为空")
    @Min(value = 1, message = "评分最低为1星")
    @Max(value = 5, message = "评分最高为5星")
    private Integer rating;
    
    /**
     * 评价内容
     */
    private String comment;
} 