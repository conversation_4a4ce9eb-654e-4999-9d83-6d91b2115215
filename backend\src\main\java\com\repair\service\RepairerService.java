package com.repair.service;

import com.repair.dto.RepairerLoginDTO;
import com.repair.dto.PasswordChangeDTO;
import com.repair.dto.ResetPasswordDTO;
import com.repair.entity.Repairer;
import com.repair.vo.RepairerLoginVO;
import com.repair.vo.RepairerInfoVO;
import com.repair.vo.PageVO;
import com.repair.vo.OrderVO;
import com.repair.dto.RepairerInfoUpdateDTO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 维修师傅服务接口
 */
public interface RepairerService extends IService<Repairer> {
    /**
     * 维修师傅登录
     */
    RepairerLoginVO login(RepairerLoginDTO loginDTO);
    
    /**
     * 获取维修师信息
     */
    RepairerInfoVO getRepairerInfo();
    
    /**
     * 获取待接单列表
     * @param page 页码
     * @param size 每页大小
     * @return 分页数据
     */
    PageVO<OrderVO> getPendingOrders(Integer page, Integer size);
    
    /**
     * 获取已接订单列表
     * @param page 页码
     * @param size 每页大小
     * @return 分页数据
     */
    PageVO<OrderVO> getAcceptedOrders(Integer page, Integer size);
    
    /**
     * 接单
     * @param orderId 订单ID
     */
    void acceptOrder(Long orderId);
    
    /**
     * 取消接单
     * @param orderId 订单ID
     */
    void cancelOrder(Long orderId);
    
    /**
     * 完成订单
     * @param orderId 订单ID
     */
    void completeOrder(Long orderId);
    
    /**
     * 开始处理订单
     * @param orderId 订单ID
     */
    void processOrder(Long orderId);
    
    /**
     * 更新维修师信息
     * @param updateDTO 更新信息DTO
     * @return 更新后的维修师信息
     */
    RepairerInfoVO updateRepairerInfo(RepairerInfoUpdateDTO updateDTO);
    
    /**
     * 修改密码
     * @param passwordChangeDTO 密码修改DTO
     */
    void changePassword(PasswordChangeDTO passwordChangeDTO);

    /**
     * 发送验证码
     */
    void sendVerifyCode(String phone);

    /**
     * 重置密码
     */
    void resetPassword(ResetPasswordDTO resetPasswordDTO);
    
    /**
     * 获取待处理订单数量（已接单和处理中状态）
     * @return 订单数量
     */
    Long getProcessingOrderCount();
    
    /**
     * 获取待接单订单数量
     * @return 订单数量
     */
    Long getPendingOrderCount();
} 