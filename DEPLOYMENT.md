# uni-app维修管理系统部署指南

本文档提供了将uni-app维修管理系统部署到生产环境的详细步骤，包括前端微信小程序部署和后端服务器部署。

## 目录

- [前端部署 - 微信小程序](#前端部署---微信小程序)
  - [准备工作](#准备工作)
  - [构建微信小程序](#构建微信小程序)
  - [上传与审核](#上传与审核)
  - [发布上线](#发布上线)
- [后端部署 - Docker](#后端部署---docker)
  - [前提条件](#前提条件)
  - [部署架构](#部署架构)
  - [环境准备](#环境准备)
  - [Docker镜像构建](#docker镜像构建)
  - [代码部署](#代码部署)
  - [配置与启动](#配置与启动)
  - [验证部署](#验证部署)
- [域名与HTTPS配置](#域名与https配置)
- [监控与维护](#监控与维护)
- [常见问题](#常见问题)

## 前端部署 - 微信小程序

### 准备工作

1. **确认微信小程序已完成备案**
   - 确保小程序AppID已配置在项目的`manifest.json`文件中
   - 确认已在微信公众平台完成域名备案

2. **准备开发者工具**
   - 下载并安装[微信开发者工具](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
   - 使用已获得管理员授权的微信账号登录开发者工具

3. **配置服务器域名**
   - 登录[微信公众平台](https://mp.weixin.qq.com/)
   - 进入"开发"->"开发设置"->"服务器域名"
   - 添加后端API的域名到request合法域名列表中
   - 如有使用WebSocket，添加相应域名到WebSocket合法域名
   - 如有上传功能，添加相应域名到uploadFile合法域名

### 构建微信小程序

1. **修改环境配置**
   - 打开项目根目录下的`.env.production`文件
   - 确保API地址指向生产环境服务器
   ```
   VUE_APP_BASE_API=https://your-production-api.com/api
   ```

2. **执行构建命令**
   ```powershell
   # 安装依赖
   pnpm install

   # 构建微信小程序
   pnpm build:mp-weixin
   ```

3. **优化包体积**
   - 检查并删除未使用的组件和静态资源
   - 确保图片已压缩，考虑使用CDN存储大型资源
   - 检查最终包大小是否符合微信小程序限制(主包不超过2MB，分包不超过8MB)

### 上传与审核

1. **导入项目**
   - 打开微信开发者工具
   - 选择"导入项目"，选择项目目录下的`dist/build/mp-weixin`文件夹
   - 填入AppID(已在manifest.json中配置)

2. **预览测试**
   - 在开发者工具中点击"预览"按钮
   - 使用微信扫描二维码进行真机测试
   - 确认所有功能正常运行，包括API调用、页面跳转等

3. **上传代码**
   - 在开发者工具中点击"上传"按钮
   - 填写版本号和项目备注
   - 等待上传完成

4. **提交审核**
   - 登录[微信公众平台](https://mp.weixin.qq.com/)
   - 进入"管理"->"版本管理"
   - 选择刚上传的版本，点击"提交审核"
   - 填写必要的审核信息，包括功能介绍、测试账号等
   - 提交审核并等待微信团队审核(通常1-7天)

### 发布上线

1. **审核通过后发布**
   - 收到审核通过通知后，登录微信公众平台
   - 进入"管理"->"版本管理"
   - 点击"发布"按钮将小程序发布到线上环境

2. **配置小程序自动化**
   - 考虑使用CI/CD工具自动化构建和上传过程
   - 可以使用miniprogram-ci工具实现命令行上传
   ```powershell
   # 安装miniprogram-ci
   npm install -g miniprogram-ci
   
   # 配置上传脚本(需要创建project.config.json)
   miniprogram-ci upload --pp ./dist/build/mp-weixin --pkp ./private.key --appid YOUR_APPID --uv 1.0.0 --desc "版本描述"
   ```

## 后端部署 - Docker

### 前提条件

在开始部署之前，请确保您的服务器已安装以下软件：

- Docker (20.10.x 或更高版本)
- Docker Compose (2.x 或更高版本)
- Git (用于克隆代码仓库)

可以使用以下命令检查安装情况：

```bash
docker --version
docker-compose --version
git --version
```

如果尚未安装，请按照官方文档进行安装：

- [Docker安装指南](https://docs.docker.com/engine/install/)
- [Docker Compose安装指南](https://docs.docker.com/compose/install/)

### 部署架构

Docker部署架构包含以下容器：

- **backend**: Spring Boot应用容器
- **mysql**: MySQL 8.0数据库容器
- **redis**: Redis 6.2缓存容器
- **nginx**: Nginx 1.21反向代理容器(可选，也可使用宝塔面板等外部Nginx)

所有容器都在同一个Docker网络中通信，数据通过Docker卷持久化。

### 环境准备

1. **服务器准备**
   - 推荐配置: 2核4G内存以上，存储空间根据数据量确定
   - 操作系统: CentOS 7+, Ubuntu 18.04+ 或 Windows Server 2016+
   - 确保服务器已开放必要端口(通常为80, 443, 18080)

2. **域名解析**
   - 将已备案的域名解析到服务器IP
   - 添加www和api子域名记录(如需要)

3. **防火墙配置**
   ```bash
   # Ubuntu/Debian
   sudo ufw allow 80/tcp
   sudo ufw allow 443/tcp
   sudo ufw allow 18080/tcp
   
   # CentOS
   sudo firewall-cmd --permanent --add-port=80/tcp
   sudo firewall-cmd --permanent --add-port=443/tcp
   sudo firewall-cmd --permanent --add-port=18080/tcp
   sudo firewall-cmd --reload
   ```

### Docker镜像构建

1. **注意 shell 脚本格式**

   如果你自定义了启动脚本（如 `docker-entrypoint.sh`），请务必确保：
   - 文件编码为 UTF-8 无 BOM（在 VSCode 右下角选择"UTF-8"而非"UTF-8 with BOM"保存）
   - 行尾为 LF（在 VSCode 右下角选择"LF"）
   - 否则容器启动时会报 `no such file or directory` 或 `Illegal option -` 等错误。

2. **了解Dockerfile**

   项目中的`backend/Dockerfile`文件定义了如何构建Spring Boot应用的Docker镜像。它使用了多阶段构建：

   ```dockerfile
   # 第一阶段：使用Maven构建应用
   FROM maven:3.8-openjdk-17 AS build
   WORKDIR /app
   COPY pom.xml .
   # 下载依赖，利用Docker缓存机制
   RUN mvn dependency:go-offline
   
   COPY src ./src
   RUN mvn clean package -DskipTests
   
   # 第二阶段：使用JRE运行应用
   FROM eclipse-temurin:17-jre-alpine
   WORKDIR /app
   COPY --from=build /app/target/*.jar app.jar
   
   # 创建日志目录
   RUN mkdir -p /app/logs
   
   # 设置时区
   RUN apk add --no-cache tzdata
   ENV TZ=Asia/Shanghai
   
   # 设置JVM参数
   ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+HeapDumpOnOutOfMemoryError -XX:+UseStringDeduplication"
   
   EXPOSE 18080
   
   # 使用ENTRYPOINT而不是CMD，这样可以接受额外的参数
   ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar ${0} ${@}"]
   ```

   这个Dockerfile的优点：
   - 多阶段构建减小了最终镜像大小
   - 利用Docker缓存机制加速构建
   - 配置了合适的JVM参数
   - 设置了正确的时区
   - 创建了日志目录
   - 允许通过环境变量传递额外的启动参数

3. **手动构建镜像**

   如果需要单独构建后端镜像（通常不需要，因为`docker-compose up`会自动构建）：

   ```bash
   # 进入项目根目录
   cd /path/to/repair-system
   
   # 构建后端镜像
   docker build -t repair-system-backend:latest ./backend
   ```

4. **自定义构建参数**

   如果需要传递构建参数：

   ```bash
   # 使用构建参数
   docker build --build-arg JDK_VERSION=17 -t repair-system-backend:latest ./backend
   ```

5. **镜像标记与推送**

   如果需要将镜像推送到私有仓库：

   ```bash
   # 标记镜像
   docker tag repair-system-backend:latest your-registry.com/repair-system-backend:v1.0.0
   
   # 推送镜像
   docker push your-registry.com/repair-system-backend:v1.0.0
   ```

### 代码部署

1. **克隆代码仓库**
   ```bash
   # 使用Git克隆项目代码
   git clone <仓库地址> /path/to/repair-system
   cd /path/to/repair-system
   ```

   对于Windows服务器:
   ```powershell
   git clone <仓库地址> C:\path\to\repair-system
   cd C:\path\to\repair-system
   ```

2. **创建必要的目录结构**
   ```bash
   # 创建nginx配置目录
   mkdir -p backend/nginx/conf.d
   mkdir -p backend/nginx/ssl
   mkdir -p backend/nginx/logs
   
   # 确保日志目录存在
   mkdir -p backend/logs
   ```

3. **准备配置文件**
   - 复制环境变量示例文件
   ```bash
   cp .env.example .env
   ```
   
   - 修改.env文件中的敏感信息，如数据库密码、JWT密钥等
   ```bash
   # 编辑.env文件
   vi .env
   ```

   `.env`文件的作用：
   - 在Docker Compose启动时被自动读取
   - 为容器提供环境变量配置
   - 允许在不修改docker-compose.yml的情况下自定义配置
   - 包含敏感信息，不应提交到版本控制系统

### 配置与启动

1. **配置环境变量**
   
   编辑`.env`文件，设置适合生产环境的配置：
   ```
   # 数据库配置
   MYSQL_ROOT_PASSWORD=your_secure_root_password
   MYSQL_DATABASE=repair_system
   MYSQL_USER=repair_user
   MYSQL_PASSWORD=your_secure_password
   
   # Redis配置
   REDIS_PASSWORD=your_secure_redis_password
   
   # JWT配置
   JWT_SECRET=your_secure_jwt_secret_key
   
   # 微信小程序配置
   WECHAT_MINIAPP_APPID=your_appid
   WECHAT_MINIAPP_SECRET=your_secret
   
   # 应用端口配置
   BACKEND_PORT=18080
   MYSQL_PORT=3306
   REDIS_PORT=6379
   NGINX_HTTP_PORT=80
   NGINX_HTTPS_PORT=443
   ```

2. **启动Docker服务**
   ```bash
   # 构建并启动所有服务
   docker-compose up -d
   ```

   Docker Compose会执行以下操作：
   - 读取`.env`文件中的环境变量
   - 构建后端镜像（如果不存在或有变更）
   - 创建或启动MySQL、Redis和Nginx容器
   - 创建Docker网络和卷
   - 在后台运行所有容器

   如果只需要启动特定服务：
   ```bash
   # 仅启动后端和数据库
   docker-compose up -d backend mysql redis
   ```

3. **查看服务状态**
   ```bash
   # 查看所有容器状态
   docker-compose ps
   
   # 查看后端日志
   docker-compose logs -f backend
   ```

### 验证部署

1. **检查服务健康状态**
   ```bash
   # 检查后端健康状态
   curl http://localhost:18080/api/health
   
   # 检查Nginx健康状态
   curl http://localhost/health
   ```

2. **测试API接口**
   ```bash
   # 测试登录接口
   curl -X POST http://localhost:18080/api/user/login -H "Content-Type: application/json" -d '{"username":"admin","password":"password"}'
   ```

## 域名与HTTPS配置

1. **准备SSL证书**
   
   如果使用Let's Encrypt：
   ```bash
   # 创建证书目录
   mkdir -p backend/nginx/ssl
   
   # 使用certbot获取证书
   sudo certbot certonly --standalone -d api.yourdomain.com
   
   # 复制证书到nginx目录
   sudo cp /etc/letsencrypt/live/api.yourdomain.com/fullchain.pem backend/nginx/ssl/cert.pem
   sudo cp /etc/letsencrypt/live/api.yourdomain.com/privkey.pem backend/nginx/ssl/key.pem
   ```

   如果使用自己的SSL证书：
   ```bash
   # 复制证书文件到nginx/ssl目录
   cp your_cert.pem backend/nginx/ssl/cert.pem
   cp your_key.pem backend/nginx/ssl/key.pem
   ```

2. **配置Nginx HTTPS**
   
   项目中已包含一个示例配置文件`backend/nginx/conf.d/xiushifu.xyz.conf`，您可以根据自己的域名修改此文件。
   
   如果使用Docker的Nginx（需要取消注释docker-compose.yml中的nginx服务），编辑`backend/nginx/conf.d/default.conf`文件：
   ```nginx
   server {
       listen 80;
       server_name api.yourdomain.com;
       
       # 重定向HTTP到HTTPS
       return 301 https://$host$request_uri;
   }
   
   server {
       listen 443 ssl;
       server_name api.yourdomain.com;
       
       ssl_certificate /etc/nginx/ssl/cert.pem;
       ssl_certificate_key /etc/nginx/ssl/key.pem;
       ssl_protocols TLSv1.2 TLSv1.3;
       ssl_ciphers HIGH:!aNULL:!MD5;
       
       # API反向代理
       location /api/ {
           proxy_pass http://backend:18080/;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }
   }
   ```
   
   如果使用宝塔面板，您可以参考`backend/nginx/conf.d/xiushifu.xyz.conf`的内容创建站点配置。

3. **重启Nginx容器（如果使用Docker的Nginx）**
   ```bash
   docker-compose restart nginx
   ```

4. **配置证书自动续期**
   
   创建证书续期脚本`renew-cert.sh`：
   ```bash
   #!/bin/bash
   
   # 续期证书
   certbot renew --quiet
   
   # 复制新证书到nginx目录
   cp /etc/letsencrypt/live/api.yourdomain.com/fullchain.pem /path/to/repair-system/backend/nginx/ssl/cert.pem
   cp /etc/letsencrypt/live/api.yourdomain.com/privkey.pem /path/to/repair-system/backend/nginx/ssl/key.pem
   
   # 重启nginx容器（如果使用Docker的Nginx）
   cd /path/to/repair-system
   docker-compose restart nginx
   
   # 如果使用宝塔面板，重启nginx服务
   # service nginx restart
   ```

   添加到crontab：
   ```bash
   # 每月1日凌晨2点执行续期
   0 2 1 * * /path/to/renew-cert.sh
   ```

## 监控与维护

1. **数据库备份**
   
   创建备份脚本`backup.sh`：
   ```bash
   #!/bin/bash
   
   # 设置备份目录和日期格式
   BACKUP_DIR="/path/to/backups"
   DATE=$(date +%Y%m%d_%H%M%S)
   
   # 创建备份目录
   mkdir -p $BACKUP_DIR
   
   # 备份MySQL数据
   docker exec repair-system-mysql sh -c 'exec mysqldump -uroot -p"$MYSQL_ROOT_PASSWORD" repair_system' > $BACKUP_DIR/repair_system_$DATE.sql
   
   # 压缩备份
   gzip $BACKUP_DIR/repair_system_$DATE.sql
   
   # 删除30天前的备份
   find $BACKUP_DIR -name "repair_system_*.sql.gz" -mtime +30 -delete
   ```
   
   添加到crontab：
   ```bash
   # 每天凌晨2点执行备份
   0 2 * * * /path/to/backup.sh
   ```

2. **日志管理**
   
   Docker容器的日志会自动保存，但建议定期清理：
   ```bash
   # 查看日志使用情况
   docker system df -v
   
   # 清理未使用的容器、网络和镜像
   docker system prune
   
   # 限制容器日志大小（在docker-compose.yml中添加）
   logging:
     options:
       max-size: "10m"
       max-file: "3"
   ```

3. **应用更新**
   
   创建更新脚本`update.sh`：
   ```bash
   #!/bin/bash
   
   # 进入项目目录
   cd /path/to/repair-system
   
   # 拉取最新代码
   git pull
   
   # 重新构建并启动后端服务
   docker-compose build backend
   docker-compose up -d backend
   ```

4. **监控系统资源**
   
   可以使用Docker自带的监控命令：
   ```bash
   # 查看容器资源使用情况
   docker stats
   
   # 查看容器日志
   docker-compose logs -f
   ```
   
   也可以考虑使用更高级的监控工具：
   - Prometheus + Grafana：用于监控系统和应用指标
   - ELK Stack (Elasticsearch, Logstash, Kibana)：用于日志聚合和分析
   - Portainer：Docker可视化管理界面，简化Docker管理

## 常见问题

### 1. 容器无法启动，报 `no such file or directory` 或 `Illegal option -`

- 重点排查 `docker-entrypoint.sh` 是否为 UTF-8 无 BOM 且 LF 行尾格式。
- 可用如下命令进入容器检查：
  ```sh
  docker run --rm -it --entrypoint sh repair-system-backend:latest
  ls -l /app
  head -1 /app/docker-entrypoint.sh | od -c
  ```
- 若看到 BOM 字节（`\357\273\277`），请用 VSCode 或 Notepad++ 转换为无 BOM 并保存。
- 检查 `.dockerignore` 是否误排除了脚本。
- 确认 build context 与 COPY 路径一致。

### 2. 数据库连接失败

确保MySQL容器已正常启动，并且连接配置正确：

```bash
# 检查MySQL容器状态
docker-compose ps mysql

# 进入MySQL容器验证
docker-compose exec mysql mysql -urepair_user -p
```

常见解决方案：
- 检查`.env`文件中的数据库配置
- 确认MySQL容器健康状态
- 验证网络连接是否正常

### 3. 数据库初始化失败

如果数据库表结构创建成功但数据未初始化，可能是初始化脚本存在语法问题：

```bash
# 检查初始化脚本是否正确挂载
docker-compose exec mysql ls -la /docker-entrypoint-initdb.d/

# 查看MySQL错误日志
docker-compose logs mysql | grep "ERROR"

# 手动执行初始化脚本
docker-compose exec -T mysql mysql -uroot -p${MYSQL_ROOT_PASSWORD} repair_system < ./backend/src/main/resources/db/data.sql
```

注意事项：
- MySQL 8.0不支持`ALTER TABLE ADD COLUMN IF NOT EXISTS`语法，确保schema.sql中不使用此语法
- 确保表结构在CREATE TABLE阶段就完整定义，避免冗余的ALTER TABLE操作
- 如果修改了初始化脚本，需要删除MySQL容器和数据卷后重新创建：
  ```bash
  docker-compose down -v
  docker-compose up -d
  ```

### 4. Redis连接失败

确保Redis容器已正常启动，并且连接配置正确：

```bash
# 检查Redis容器状态
docker-compose ps redis

# 测试Redis连接
docker-compose exec redis redis-cli -a ${REDIS_PASSWORD} ping
```

### 5. Nginx配置问题

检查Nginx配置是否正确：

```bash
# 进入Nginx容器
docker-compose exec nginx nginx -t

# 如果有错误，修改配置后重启
docker-compose restart nginx
```

### 6. 性能优化

如果系统运行缓慢，可以尝试以下优化：

- 调整JVM参数：修改Dockerfile中的JAVA_OPTS
- 优化数据库：添加索引，优化查询
- 使用Redis缓存：确保高频访问的数据使用缓存
- 扩展服务器资源：增加CPU、内存或磁盘空间
- 配置连接池：优化数据库和Redis连接池参数

### 7. Docker镜像构建失败

如果Docker镜像构建失败：

```bash
# 查看详细构建日志
docker-compose build --no-cache backend

# 检查Dockerfile语法
docker build -t test-build ./backend
```

常见原因：
- 网络问题导致依赖下载失败
- 构建上下文中存在不必要的大文件
- Maven或Gradle构建失败 