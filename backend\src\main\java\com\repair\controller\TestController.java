package com.repair.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api")
public class TestController {

    @GetMapping("/test")
    public String test() {
        return "Hello, Repair System is running!";
    }
    
    /**
     * 健康检查端点
     * 用于Docker容器健康检查和服务可用性监控
     * @return 包含状态信息的JSON响应
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("message", "服务运行正常");
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }
} 