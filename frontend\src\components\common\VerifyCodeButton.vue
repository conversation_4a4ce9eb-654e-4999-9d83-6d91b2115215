<template>
  <button 
    class="verify-code-btn" 
    :class="{'disabled': countdown > 0 || isSending}" 
    :disabled="countdown > 0 || isSending" 
    @click="sendVerificationCode"
  >
    <text v-if="isSending">发送中...</text>
    <text v-else-if="countdown > 0">{{ countdown }}s</text>
    <text v-else>{{ buttonText }}</text>
  </button>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted, defineComponent } from 'vue';
import { isValidPhone, useCountdown } from '@/utils/verifycode';
import { captchaService } from '@/utils/captchaService';
import { showToast } from '@/utils/uniapi';

// 确保组件有一个明确的名称
defineComponent({
  name: 'VerifyCodeButton'
});

// 定义组件属性
const props = defineProps({
  phone: {
    type: String,
    required: true
  },
  userType: {
    type: String as () => 'user' | 'repairer',
    required: true
  },
  forLogin: {
    type: Boolean,
    default: false
  },
  buttonText: {
    type: String,
    default: '获取验证码'
  }
});

// 定义组件事件
const emit = defineEmits(['send-success', 'send-fail']);

// 使用统一的倒计时工具
const countdownState = useCountdown(computed(() => props.phone), props.userType);

// 公开倒计时和发送状态
const countdown = computed(() => countdownState.countdown.value);
const isSending = computed(() => countdownState.isSending.value);

// 滑动验证状态
const showSliderCaptcha = computed({
  get: () => captchaService.showCaptcha.value,
  set: (value) => captchaService.showCaptcha.value = value
});

// 组件卸载时清理倒计时
onUnmounted(() => {
  if (countdownState.cleanup) {
    countdownState.cleanup();
  }
});

// 发送验证码
async function sendVerificationCode() {
  // 防止重复点击或倒计时中重复发送
  if (countdown.value > 0 || isSending.value) {
    return;
  }
  
  // 验证手机号
  if (!isValidPhone(props.phone)) {
    showToast('请输入正确的手机号码');
    return;
  }
  
  // 使用验证服务发送验证码（包含滑动验证）
  const success = await captchaService.sendVerifyCodeWithCaptcha(
    props.phone,
    props.userType,
    props.forLogin
  );
  
  // 触发回调
  if (success) {
    emit('send-success');
  } else {
    emit('send-fail');
  }
}

// 向父组件暴露方法和属性
defineExpose({
  countdown,
  isSending,
  sendVerificationCode
});
</script>

<style>
.verify-code-btn {
  height: 90rpx;
  padding: 0 30rpx;
  font-size: 26rpx;
  background-color: #3c9cff;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
  transition: all 0.3s;
  min-width: 200rpx;
  white-space: nowrap;
}

.verify-code-btn.disabled {
  background-color: #bbb;
  opacity: 0.8;
  color: #f5f5f5;
}
</style> 