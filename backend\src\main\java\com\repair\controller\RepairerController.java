package com.repair.controller;

import com.repair.common.Result;
import com.repair.dto.RepairerLoginDTO;
import com.repair.service.RepairerService;
import com.repair.vo.RepairerLoginVO;
import com.repair.vo.RepairerInfoVO;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.repair.vo.PageVO;
import com.repair.vo.OrderVO;
import com.repair.dto.RepairerInfoUpdateDTO;
import com.repair.dto.PasswordChangeDTO;
import com.repair.dto.ResetPasswordDTO;
import com.repair.service.VerifyCodeService;

import java.util.Map;

@RestController
@RequestMapping("/api/repairer")
@RequiredArgsConstructor
public class RepairerController {
    
    private final RepairerService repairerService;
    private final VerifyCodeService verifyCodeService;

    /**
     * 维修师傅登录
     */
    @PostMapping("/login")
    public Result<RepairerLoginVO> login(@RequestBody @Validated RepairerLoginDTO loginDTO) {
        RepairerLoginVO loginVO = repairerService.login(loginDTO);
        return Result.success(loginVO);
    }
    
    /**
     * 获取维修师信息
     */
    @GetMapping("/info")
    public Result<RepairerInfoVO> getRepairerInfo() {
        RepairerInfoVO repairerInfo = repairerService.getRepairerInfo();
        return Result.success(repairerInfo);
    }
    
    /**
     * 获取待接单列表
     */
    @GetMapping("/orders/pending")
    public Result<PageVO<OrderVO>> getPendingOrders(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        PageVO<OrderVO> pageData = repairerService.getPendingOrders(page, size);
        return Result.success(pageData);
    }
    
    /**
     * 获取已接订单列表
     */
    @GetMapping("/orders/accepted")
    public Result<PageVO<OrderVO>> getAcceptedOrders(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        PageVO<OrderVO> pageData = repairerService.getAcceptedOrders(page, size);
        return Result.success(pageData);
    }
    
    /**
     * 接单
     */
    @PostMapping("/orders/{orderId}/accept")
    public Result<Void> acceptOrder(@PathVariable Long orderId) {
        repairerService.acceptOrder(orderId);
        return Result.success();
    }
    
    /**
     * 取消接单
     */
    @PostMapping("/orders/{orderId}/cancel")
    public Result<Void> cancelOrder(@PathVariable Long orderId) {
        repairerService.cancelOrder(orderId);
        return Result.success();
    }
    
    /**
     * 完成订单
     */
    @PostMapping("/orders/{orderId}/complete")
    public Result<Void> completeOrder(@PathVariable Long orderId) {
        repairerService.completeOrder(orderId);
        return Result.success();
    }
    
    /**
     * 开始处理订单
     */
    @PostMapping("/orders/{orderId}/process")
    public Result<Void> processOrder(@PathVariable Long orderId) {
        repairerService.processOrder(orderId);
        return Result.success();
    }
    
    /**
     * 更新维修师信息
     */
    @PutMapping("/info")
    public Result<RepairerInfoVO> updateRepairerInfo(@RequestBody @Validated RepairerInfoUpdateDTO updateDTO) {
        RepairerInfoVO repairerInfo = repairerService.updateRepairerInfo(updateDTO);
        return Result.success(repairerInfo);
    }
    
    /**
     * 修改密码
     */
    @PutMapping("/password")
    public Result<String> changePassword(@RequestBody @Validated PasswordChangeDTO passwordDTO) {
        repairerService.changePassword(passwordDTO);
        return Result.success("密码修改成功");
    }
    
    /**
     * 发送验证码
     */
    @PostMapping("/verifyCode")
    public Result<String> sendVerifyCode(@RequestBody Map<String, String> params) {
        try {
            String phone = params.get("phone");
            if (phone == null || phone.isEmpty()) {
                return Result.error("手机号不能为空");
            }
            
            // 使用统一的验证码服务发送验证码
            verifyCodeService.sendVerifyCode(phone, "repairer");
            
            return Result.success("验证码发送成功");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 重置密码（忘记密码）
     */
    @PostMapping("/resetPassword")
    public Result<String> resetPassword(@RequestBody @Validated ResetPasswordDTO resetPasswordDTO) {
        repairerService.resetPassword(resetPasswordDTO);
        return Result.success("密码重置成功");
    }
    
    /**
     * 获取待处理订单数量（已接单和处理中状态）
     */
    @GetMapping("/orders/processing/count")
    public Result<Long> getProcessingOrderCount() {
        Long count = repairerService.getProcessingOrderCount();
        return Result.success(count);
    }
    
    /**
     * 获取待接单订单数量
     */
    @GetMapping("/orders/pending/count")
    public Result<Long> getPendingOrderCount() {
        Long count = repairerService.getPendingOrderCount();
        return Result.success(count);
    }
} 