package com.repair.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.repair.entity.RepairOrder;
import com.repair.entity.User;
import com.repair.entity.Repairer;
import com.repair.mapper.RepairOrderMapper;
import com.repair.mapper.UserMapper;
import com.repair.mapper.RepairerMapper;
import com.repair.service.RepairerOrderService;
import com.repair.utils.AddressUtil;
import com.repair.vo.OrderVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.repair.exception.BusinessException;
import com.repair.enums.OrderStatusEnum;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 维修人员订单服务实现类
 */
@Slf4j
@Service
public class RepairerOrderServiceImpl extends ServiceImpl<RepairOrderMapper, RepairOrder> implements RepairerOrderService {

    private final UserMapper userMapper;
    private final RepairerMapper repairerMapper;

    public RepairerOrderServiceImpl(UserMapper userMapper, RepairerMapper repairerMapper) {
        this.userMapper = userMapper;
        this.repairerMapper = repairerMapper;
    }

    @Override
    public Page<OrderVO> getPendingOrders(Page<RepairOrder> page) {
        // 查询待处理的订单
        LambdaQueryWrapper<RepairOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RepairOrder::getStatus, 0) // 待处理状态
                    .orderByDesc(RepairOrder::getUrgent) // 紧急订单优先
                    .orderByAsc(RepairOrder::getCreateTime); // 先创建先处理
        
        Page<RepairOrder> orderPage = this.page(page, queryWrapper);
        
        // 转换为VO
        List<OrderVO> voList = new ArrayList<>();
        for (RepairOrder order : orderPage.getRecords()) {
            // 获取用户名
            User user = userMapper.selectById(order.getUserId());
            String username = user != null ? user.getUsername() : "";
            
            voList.add(convertToVO(order, username, null));
        }
        
        // 组装分页结果
        Page<OrderVO> voPage = new Page<>();
        BeanUtils.copyProperties(orderPage, voPage, "records");
        voPage.setRecords(voList);
        
        return voPage;
    }

    @Override
    public Page<OrderVO> getAcceptedOrders(Long repairerId, Page<RepairOrder> page) {
        // 查询已接单的订单
        LambdaQueryWrapper<RepairOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RepairOrder::getRepairerId, repairerId)
                    .in(RepairOrder::getStatus, 1, 2) // 已接单或处理中
                    .orderByDesc(RepairOrder::getStatus) // 处理中的订单优先
                    .orderByDesc(RepairOrder::getUrgent) // 紧急订单优先
                    .orderByAsc(RepairOrder::getUpdateTime); // 按更新时间升序
        
        Page<RepairOrder> orderPage = this.page(page, queryWrapper);
        
        // 转换为VO
        List<OrderVO> voList = new ArrayList<>();
        for (RepairOrder order : orderPage.getRecords()) {
            // 获取用户名
            User user = userMapper.selectById(order.getUserId());
            String username = user != null ? user.getUsername() : "";
            
            // 获取维修师傅名（即当前登录用户）
            User repairer = userMapper.selectById(repairerId);
            String repairerName = repairer != null ? repairer.getUsername() : "";
            
            voList.add(convertToVO(order, username, repairerName));
        }
        
        // 组装分页结果
        Page<OrderVO> voPage = new Page<>();
        BeanUtils.copyProperties(orderPage, voPage, "records");
        voPage.setRecords(voList);
        
        return voPage;
    }

    @Override
    @Transactional
    public OrderVO acceptOrder(Long repairerId, Long orderId) {
        // 获取订单信息
        RepairOrder order = this.getById(orderId);
        if (order == null) {
            log.warn("订单不存在: {}", orderId);
            throw new BusinessException("订单不存在");
        }
        
        // 检查订单状态
        if (order.getStatus() != OrderStatusEnum.PENDING.getCode()) {
            log.warn("订单状态不是待处理，无法接单: 订单ID={}, 当前状态={}", orderId, order.getStatus());
            throw new BusinessException("订单状态不是待处理，无法接单");
        }
        
        // 更新订单状态
        order.setRepairerId(repairerId)
             .setStatus(OrderStatusEnum.ACCEPTED.getCode()) // 已接单
             .setUpdateTime(LocalDateTime.now());
        this.updateById(order);
        
        // 获取用户名
        User user = userMapper.selectById(order.getUserId());
        String username = user != null ? user.getUsername() : "";
        
        // 获取维修师傅名
        User repairer = userMapper.selectById(repairerId);
        String repairerName = repairer != null ? repairer.getUsername() : "";
        
        return convertToVO(order, username, repairerName);
    }

    @Override
    @Transactional
    public OrderVO processOrder(Long repairerId, Long orderId) {
        // 获取订单信息
        RepairOrder order = this.getById(orderId);
        if (order == null) {
            log.warn("订单不存在: {}", orderId);
            throw new BusinessException("订单不存在");
        }
        
        // 检查订单是否属于当前维修师傅
        if (!Objects.equals(order.getRepairerId(), repairerId)) {
            log.warn("无权操作此订单: 维修工ID={}, 订单ID={}", repairerId, orderId);
            throw new BusinessException("无权操作此订单");
        }
        
        // 检查订单状态
        if (order.getStatus() != OrderStatusEnum.ACCEPTED.getCode()) {
            log.warn("订单状态不是已接单，无法开始处理: 订单ID={}, 当前状态={}", orderId, order.getStatus());
            throw new BusinessException("订单状态不是已接单，无法开始处理");
        }
        
        // 更新订单状态
        order.setStatus(OrderStatusEnum.PROCESSING.getCode()) // 处理中
             .setUpdateTime(LocalDateTime.now());
        this.updateById(order);
        
        // 获取用户名
        User user = userMapper.selectById(order.getUserId());
        String username = user != null ? user.getUsername() : "";
        
        // 获取维修师傅名
        User repairer = userMapper.selectById(repairerId);
        String repairerName = repairer != null ? repairer.getUsername() : "";
        
        return convertToVO(order, username, repairerName);
    }

    @Override
    @Transactional
    public OrderVO completeOrder(Long repairerId, Long orderId) {
        // 获取订单信息
        RepairOrder order = this.getById(orderId);
        if (order == null) {
            log.warn("订单不存在: {}", orderId);
            throw new BusinessException("订单不存在");
        }
        
        // 检查订单是否属于当前维修师傅
        if (!Objects.equals(order.getRepairerId(), repairerId)) {
            log.warn("无权操作此订单: 维修工ID={}, 订单ID={}", repairerId, orderId);
            throw new BusinessException("无权操作此订单");
        }
        
        // 检查订单状态
        if (order.getStatus() != OrderStatusEnum.PROCESSING.getCode()) {
            log.warn("订单状态不是处理中，无法完成: 订单ID={}, 当前状态={}", orderId, order.getStatus());
            throw new BusinessException("订单状态不是处理中，无法完成");
        }
        
        // 更新订单状态
        LocalDateTime now = LocalDateTime.now();
        order.setStatus(OrderStatusEnum.COMPLETED.getCode()) // 已完成
             .setUpdateTime(now)
             .setCompleteTime(now);
        this.updateById(order);
        
        // 获取用户名
        User user = userMapper.selectById(order.getUserId());
        String username = user != null ? user.getUsername() : "";
        
        // 获取维修师傅名
        User repairer = userMapper.selectById(repairerId);
        String repairerName = repairer != null ? repairer.getUsername() : "";
        
        return convertToVO(order, username, repairerName);
    }

    @Override
    @Transactional
    public OrderVO transferOrder(Long repairerId, Long orderId, String reason) {
        // 获取订单信息
        RepairOrder order = this.getById(orderId);
        if (order == null) {
            log.warn("订单不存在: {}", orderId);
            throw new BusinessException("订单不存在");
        }
        
        // 检查订单是否属于当前维修师傅
        if (!Objects.equals(order.getRepairerId(), repairerId)) {
            log.warn("无权操作此订单: 维修工ID={}, 订单ID={}", repairerId, orderId);
            throw new BusinessException("无权操作此订单");
        }
        
        // 检查订单状态
        if (order.getStatus() != OrderStatusEnum.ACCEPTED.getCode() && order.getStatus() != OrderStatusEnum.PROCESSING.getCode()) {
            log.warn("订单状态不是已接单或处理中，无法转单: 订单ID={}, 当前状态={}", orderId, order.getStatus());
            throw new BusinessException("订单状态不是已接单或处理中，无法转单");
        }
        
        // 转单操作 - 重置为待处理状态
        order.setRepairerId(null)
             .setStatus(OrderStatusEnum.PENDING.getCode()) // 回到待处理状态
             .setUpdateTime(LocalDateTime.now());
             
        // 可以在这里记录转单原因到日志表，此处简化不做处理
        
        this.updateById(order);
        
        // 获取用户名
        User user = userMapper.selectById(order.getUserId());
        String username = user != null ? user.getUsername() : "";
        
        return convertToVO(order, username, null);
    }

    @Override
    public OrderVO getOrderDetail(Long repairerId, Long orderId) {
        // 获取订单信息
        RepairOrder order = this.getById(orderId);
        if (order == null) {
            log.warn("订单不存在: {}", orderId);
            throw new BusinessException("订单不存在");
        }
        
        // 检查订单是否属于当前维修师傅（如果订单已分配）
        if (order.getRepairerId() != null && !order.getRepairerId().equals(repairerId)) {
            log.warn("无权查看此订单: 维修工ID={}, 订单ID={}", repairerId, orderId);
            throw new BusinessException("无权查看此订单");
        }
        
        // 获取用户名
        User user = userMapper.selectById(order.getUserId());
        String username = user != null ? user.getUsername() : "";
        
        // 获取维修师傅名
        String repairerName = "";
        if (order.getRepairerId() != null) {
            Repairer repairer = repairerMapper.selectById(order.getRepairerId());
            if (repairer != null) {
                repairerName = repairer.getUsername();
            }
        }
        
        return convertToVO(order, username, repairerName);
    }
    
    /**
     * 将订单实体转换为视图对象
     */
    private OrderVO convertToVO(RepairOrder order, String username, String repairerName) {
        OrderVO vo = new OrderVO();
        BeanUtils.copyProperties(order, vo);
        vo.setUsername(username);
        vo.setRepairerName(repairerName);

        // 转换地址信息
        vo.setAddressInfo(AddressUtil.convertEntityToAddressVO(order));

        // 获取用户手机号
        if (order.getUserId() != null) {
            User user = userMapper.selectById(order.getUserId());
            if (user != null) {
                vo.setUserPhone(user.getPhone());
            }
        }

        // 获取维修师手机号
        if (order.getRepairerId() != null) {
            Repairer repairer = repairerMapper.selectById(order.getRepairerId());
            if (repairer != null) {
                vo.setRepairerPhone(repairer.getPhone());
            }
        }

        // 设置状态描述
        vo.setStatusDesc(OrderStatusEnum.getDescByCode(order.getStatus()));

        return vo;
    }
} 