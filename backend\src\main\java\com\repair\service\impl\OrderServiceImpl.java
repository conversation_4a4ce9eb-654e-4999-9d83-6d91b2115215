package com.repair.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.repair.dto.OrderDTO;
import com.repair.entity.RepairOrder;
import com.repair.entity.User;
import com.repair.entity.Repairer;
import com.repair.enums.OrderStatusEnum;
import com.repair.exception.BusinessException;
import com.repair.mapper.RepairerMapper;
import com.repair.mapper.RepairOrderMapper;
import com.repair.mapper.UserMapper;
import com.repair.service.OrderService;
import com.repair.utils.AddressUtil;
import com.repair.utils.JwtUtil;
import com.repair.vo.OrderVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import javax.servlet.http.HttpServletRequest;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 订单服务实现类
 */
@Service
public class OrderServiceImpl extends ServiceImpl<RepairOrderMapper, RepairOrder> implements OrderService {

    private final UserMapper userMapper;
    private final RepairerMapper repairerMapper;
    private static final Logger log = LoggerFactory.getLogger(OrderServiceImpl.class);

    public OrderServiceImpl(UserMapper userMapper, RepairerMapper repairerMapper) {
        this.userMapper = userMapper;
        this.repairerMapper = repairerMapper;
    }

    @Override
    @Transactional
    public OrderVO createOrder(Long userId, OrderDTO orderDTO) {
        // 验证地址信息
        validateAddressInfo(orderDTO);

        // 使用雪花算法生成订单号
        String orderId = com.repair.utils.SnowflakeUtil.nextIdStr();

        // 创建订单
        RepairOrder order = buildOrderEntity(userId, orderDTO, orderId);

        // 保存订单
        this.save(order);

        // 记录日志（地址脱敏）
        log.info("用户{}创建订单成功，订单号：{}，地址：{}",
                userId, order.getOrderId(), AddressUtil.maskFormattedAddress(order.getFormattedAddress()));

        // 转换为VO并返回
        return convertToVO(order);
    }

    private void validateAddressInfo(OrderDTO orderDTO) {
        // 验证结构化地址信息
        if (orderDTO.getAddressInfo() == null) {
            throw new BusinessException("地址信息不能为空");
        }

        if (!AddressUtil.validateAddress(orderDTO.getAddressInfo())) {
            throw new BusinessException("请提供完整的地址信息");
        }

        var addressInfo = orderDTO.getAddressInfo();

        // 验证坐标精度，限制过于精确的位置
        if (addressInfo.getLatitude() != null) {
            addressInfo.setLatitude(AddressUtil.limitCoordinatePrecision(addressInfo.getLatitude()));
        }
        if (addressInfo.getLongitude() != null) {
            addressInfo.setLongitude(AddressUtil.limitCoordinatePrecision(addressInfo.getLongitude()));
        }
    }

    private RepairOrder buildOrderEntity(Long userId, OrderDTO orderDTO, String orderId) {
        RepairOrder order = new RepairOrder();
        BeanUtils.copyProperties(orderDTO, order);

        // 处理地址信息
        AddressUtil.copyAddressToEntity(orderDTO.getAddressInfo(), order);

        order.setUserId(userId)
             .setOrderId(orderId)
             .setStatus(0) // 待处理状态
             .setCreateTime(LocalDateTime.now())
             .setUpdateTime(LocalDateTime.now());

        return order;
    }

    @Override
    public Page<OrderVO> getUserOrders(Long userId, Integer status, Page<RepairOrder> page) {
        // 构建查询条件
        LambdaQueryWrapper<RepairOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RepairOrder::getUserId, userId);
        
        // 如果指定了状态且不为-1，则按状态过滤
        if (status != null && status != -1) {
            queryWrapper.eq(RepairOrder::getStatus, status);
        }
        
        // 按创建时间倒序排序
        queryWrapper.orderByDesc(RepairOrder::getCreateTime);
        
        // 分页查询
        Page<RepairOrder> orderPage = this.page(page, queryWrapper);
        
        // 转换为VO
        List<OrderVO> voList = new ArrayList<>();
        for (RepairOrder order : orderPage.getRecords()) {
            voList.add(convertToVO(order));
        }
        
        // 组装分页结果
        Page<OrderVO> voPage = new Page<>();
        BeanUtils.copyProperties(orderPage, voPage, "records");
        voPage.setRecords(voList);
        
        return voPage;
    }

    @Override
    public OrderVO getOrderDetail(Long orderId) {
        RepairOrder order = this.getById(orderId);
        if (order == null) {
            log.warn("订单不存在: {}", orderId);
            throw new BusinessException("订单不存在");
        }

        // 在Service层进行权限校验
        Long currentUserId = JwtUtil.getCurrentUserId();
        String currentRole = getCurrentUserRole();
        
        // 非管理员用户只能查看自己的订单
        if (!"admin".equals(currentRole) && !order.getUserId().equals(currentUserId)) {
            log.warn("用户 {} (角色: {}) 尝试查看不属于自己的订单: {}", currentUserId, currentRole, orderId);
            throw new BusinessException("无权查看此订单详情");
        }

        return convertToVO(order);
    }

    @Override
    @Transactional
    public OrderVO cancelOrder(Long userId, Long orderId) {
        RepairOrder order = this.getById(orderId);
        if (order == null) {
            log.warn("订单不存在: {}", orderId);
            throw new BusinessException("订单不存在");
        }
        
        // 验证订单所属
        if (!order.getUserId().equals(userId)) {
            log.warn("无权操作此订单: 用户ID={}, 订单ID={}", userId, orderId);
            throw new BusinessException("无权操作此订单");
        }
        
        // 验证订单状态 - 允许取消待处理和处理中的订单
        if (order.getStatus() != OrderStatusEnum.PENDING.getCode() && order.getStatus() != OrderStatusEnum.PROCESSING.getCode()) {
            log.warn("只能取消待处理或处理中的订单: 订单ID={}, 当前状态={}", orderId, order.getStatus());
            throw new BusinessException("只能取消待处理或处理中的订单");
        }
        
        // 更新订单状态
        order.setStatus(OrderStatusEnum.CANCELED.getCode()); // 已取消
        order.setUpdateTime(LocalDateTime.now());
        this.updateById(order);
        
        return convertToVO(order);
    }

    @Override
    @Transactional
    public OrderVO rateOrder(Long userId, Long orderId, Integer rating, String comment) {
        RepairOrder order = this.getById(orderId);
        if (order == null) {
            log.warn("订单不存在: {}", orderId);
            throw new BusinessException("订单不存在");
        }
        
        // 验证订单所属
        if (!order.getUserId().equals(userId)) {
            log.warn("无权操作此订单: 用户ID={}, 订单ID={}", userId, orderId);
            throw new BusinessException("无权操作此订单");
        }
        
        // 验证订单状态
        if (order.getStatus() != OrderStatusEnum.COMPLETED.getCode()) {
            log.warn("只能评价已完成的订单: 订单ID={}, 当前状态={}", orderId, order.getStatus());
            throw new BusinessException("只能评价已完成的订单");
        }
        
        // 验证评分范围
        if (rating < 1 || rating > 5) {
            log.warn("评分范围应为1-5星: 订单ID={}, 评分={}", orderId, rating);
            throw new BusinessException("评分范围应为1-5星");
        }
        
        // 更新评价信息
        order.setRating(rating);
        order.setComment(comment);
        order.setUpdateTime(LocalDateTime.now());
        this.updateById(order);

        return convertToVO(order);
    }
    
    /**
     * 将订单实体转换为视图对象
     */
    private OrderVO convertToVO(RepairOrder order) {
        OrderVO vo = new OrderVO();
        BeanUtils.copyProperties(order, vo);

        // 转换地址信息
        vo.setAddressInfo(AddressUtil.convertEntityToAddressVO(order));

        // 获取用户手机号 - 这里使用userMapper再次查询用户信息获取手机号
        if (order.getUserId() != null) {
            User user = userMapper.selectById(order.getUserId());
            if (user != null) {
                vo.setUsername(user.getUsername());
                vo.setUserPhone(user.getPhone());
            }
        }

        // 获取维修师手机号 - 从维修师表中查询信息
        if (order.getRepairerId() != null) {
            // 查询维修师信息获取真实姓名和手机号
            Repairer repairer = repairerMapper.selectById(order.getRepairerId());
            if (repairer != null) {
                vo.setRepairerName(repairer.getUsername());
                vo.setRepairerPhone(repairer.getPhone());
            }
        }

        // 设置状态描述
        vo.setStatusDesc(OrderStatusEnum.getDescByCode(order.getStatus()));

        // 获取当前用户角色
        String userRole = "";
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                userRole = (String) request.getAttribute("userRole");
            }
        } catch (Exception e) {
            log.error("获取用户角色失败", e);
        }

        return vo;
    }

    /**
     * 获取当前用户角色
     */
    private String getCurrentUserRole() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                return (String) request.getAttribute("userRole");
            }
        } catch (Exception e) {
            log.error("获取用户角色失败", e);
        }
        return "";
    }
} 