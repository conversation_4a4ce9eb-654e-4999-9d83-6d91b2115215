import { useUserStore } from '@/store/user';
import { useRepairerStore } from '@/store/repairer';
import { useAdminStore } from '@/store/admin';

// 接口响应格式
interface ApiResponse<T = any> {
  code: number;
  data: T;
  message: string;
}

// 请求选项扩展
type RequestOptions = UniApp.RequestOptions & {
  auth?: boolean; // 是否需要认证
  timeout?: number; // 超时时间
  requestUrl?: string; // 完整请求URL，用于日志
}

// 基础配置
let BASE_URL = '';

// #ifdef MP-WEIXIN
// 微信小程序环境使用完整的URL
// @ts-ignore
BASE_URL = import.meta.env.VITE_API_BASE_URL;
console.log('微信小程序环境 - API基础URL:', BASE_URL);
console.log('环境模式:', import.meta.env.MODE);
// #endif

// #ifdef H5
// H5环境下使用相对路径，依赖vite的代理配置
BASE_URL = '';
// #endif

const TIMEOUT = 10000;

// 验证token有效性的简单函数
function isTokenValid(token: string): boolean {
  if (!token || token.trim() === '') return false;
  
  try {
    // 简单验证token格式
    const parts = token.split('.');
    if (parts.length !== 3) return false;
    
    return true;
  } catch (e) {
    console.error('Token验证失败', e);
    return false;
  }
}

// 请求拦截器
const httpInterceptor = {
  invoke(options: any) {
    console.log('原始请求URL:', options.url);
    
    if (!options.header) {
      options.header = {};
    }
    
    // 1. 拼接基础URL
    options.header['Content-Type'] = options.header['Content-Type'] || 'application/json';
    
    // 适应不同环境的请求URL
    // 处理不完整的URL: 如果URL不是以http开头，则进行补全
    if (options.url && !options.url.startsWith('http') && !options.url.startsWith('https')) {
      // 本地开发环境使用代理
      // #ifdef H5
      options.url = options.url;
      // #endif
      
      // 小程序环境使用实际API地址
      // #ifdef MP-WEIXIN
      options.url = BASE_URL + options.url;
      // #endif
    }
    
    console.log('处理后的请求URL:', options.url);
    
    // 2. 设置超时时间
    options.timeout = options.timeout || 10000;
    
    // 3. 添加小程序端请求头标识
    // #ifdef MP-WEIXIN
    options.header['source'] = 'miniprogram';
    // #endif
    
    // 4. 添加认证信息
    if (options.auth !== false) {
      let token = '';
      const url = options.url.toLowerCase();
      
      // 增强URL路径判断逻辑
      if (url.includes('/api/repairer') || url.includes('/api/repairer/')) {
        // 维修师接口
        const repairerStore = useRepairerStore();
        token = repairerStore.token;
        console.log('使用维修师token:', token);
      } else if (url.includes('/api/admin') || url.includes('/api/admin/')) {
        // 管理员接口
        const adminStore = useAdminStore();
        token = adminStore.token;
        console.log('使用管理员token:', token);
      } else {
        // 默认使用用户token处理所有其他API请求，包括/api/order/
        const userStore = useUserStore();
        token = userStore.token;
        console.log('使用用户token:', token);
      }
      
      if (token) {
        options.header.Authorization = `Bearer ${token.trim()}`;
      }
      
      // 5. 打印请求信息
      console.log(`[Request] ${options.method} ${options.url}`);
      if (options.header.Authorization) {
        console.log('[Request] Authorization:', options.header.Authorization);
      }
    }
  }
};

// 配置拦截器
uni.addInterceptor('request', httpInterceptor);
uni.addInterceptor('uploadFile', httpInterceptor);

// 响应拦截器
const responseInterceptor = (response: UniApp.RequestSuccessCallbackResult & { requestUrl?: string }) => {
  const { statusCode, data, requestUrl } = response;
  console.log(`[Response] ${requestUrl} Status:`, statusCode);

  if (statusCode === 200) {
    const res = data as ApiResponse;
    if (res.code === 0 || res.code === 200) {
      return res;
    }
    // 处理业务错误
    const message = res.message || '请求失败';
    uni.showToast({
      title: message,
      icon: 'none'
    });
    return Promise.reject({ code: res.code, message });
  }

  // 处理401未授权错误
  if (statusCode === 401) {
    const url = requestUrl?.toLowerCase() || '';
    console.log('[401 Error] URL:', url);
    
    // 根据URL判断是哪个角色的token过期
    if (url.includes('/api/repairer') || url.includes('/api/repairer/')) {
      const repairerStore = useRepairerStore();
      repairerStore.clearRepairerInfo();
      uni.showModal({
        title: '登录已过期',
        content: '请重新登录',
        showCancel: false,
        success: () => {
          uni.reLaunch({ url: '/pages/repairer/login' });
        }
      });
    } else if (url.includes('/api/admin') || url.includes('/api/admin/')) {
      const adminStore = useAdminStore();
      adminStore.clearAdminInfo();
      uni.reLaunch({ url: '/pages/admin/login' });
    } else {
      // 所有其他API路径都使用用户逻辑处理
      const userStore = useUserStore();
      userStore.clearUserInfo();
      uni.showModal({
        title: '登录已过期',
        content: '请重新登录',
        showCancel: false,
        success: () => {
          uni.reLaunch({ url: '/pages/user/login' });
        }
      });
    }
    return Promise.reject({ code: 401, message: '登录已过期，请重新登录' });
  }

  // 处理其他HTTP错误
  let errorMessage = '';
  switch (statusCode) {
    case 403:
      errorMessage = '没有权限访问该资源';
      break;
    case 404:
      errorMessage = '请求的资源不存在';
      break;
    case 500:
      errorMessage = '服务器内部错误';
      break;
    default:
      errorMessage = '请求失败，请稍后再试';
  }
  
  uni.showToast({
    title: errorMessage,
    icon: 'none'
  });
  
  return Promise.reject({ code: statusCode, message: errorMessage });
};

// 请求函数
export function request<T>(options: RequestOptions) {
  // 设置默认值
  options.auth = options.auth !== false; // 默认需要认证
  options.timeout = options.timeout || 10000; // 默认10秒超时
  
  return new Promise<ApiResponse<T>>((resolve, reject) => {
    // 拦截并处理请求
    uni.request({
      ...options,
      timeout: options.timeout,
      success: (res) => {
        // 附加请求URL信息，便于后续日志记录
        // @ts-ignore 使用扩展属性存储请求URL
        res.requestUrl = options.url;
        
        try {
          const processedResponse = responseInterceptor(res);
          resolve(processedResponse as any);
        } catch (error) {
          reject(error);
        }
      },
      fail: (err) => {
        console.error(`[Request Failed] ${options.url}`, err);
        if (err.errMsg && err.errMsg.includes('timeout')) {
          uni.showToast({
            title: '请求超时，请检查网络',
            icon: 'none'
          });
          reject({ code: -1, message: '请求超时' });
        } else {
          uni.showToast({
            title: '网络异常，请稍后再试',
            icon: 'none'
          });
          reject({ code: -1, message: '网络异常' });
        }
      }
    });
  });
}

// 扩展请求方法
request.get = <T = any>(url: string, options: Omit<UniApp.RequestOptions, 'url' | 'method'> = {}) => {
  return request<T>({
    url,
    method: 'GET',
    ...options
  });
};

request.post = <T = any>(url: string, data?: any, options: Omit<UniApp.RequestOptions, 'url' | 'method' | 'data'> = {}) => {
  return request<T>({
    url,
    method: 'POST',
    data,
    ...options
  });
};

request.put = <T = any>(url: string, data?: any, options: Omit<UniApp.RequestOptions, 'url' | 'method' | 'data'> = {}) => {
  return request<T>({
    url,
    method: 'PUT',
    data,
    ...options
  });
};

request.delete = <T = any>(url: string, options: Omit<UniApp.RequestOptions, 'url' | 'method'> = {}) => {
  return request<T>({
    url,
    method: 'DELETE',
    ...options
  });
}; 