package com.repair.vo;

import lombok.Data;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 维修师列表VO
 */
@Data
public class RepairerVO {
    
    /**
     * 维修师ID
     */
    private Long id;
    
    /**
     * 维修师用户名
     */
    private String username;
    
    /**
     * 维修师手机号
     */
    private String phone;
    
    /**
     * 技能标签
     */
    private String skillTags;
    
    /**
     * 状态：1-正常，0-禁用
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
} 