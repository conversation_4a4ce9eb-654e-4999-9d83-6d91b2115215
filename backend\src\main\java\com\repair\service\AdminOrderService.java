package com.repair.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.repair.entity.RepairOrder;
import com.repair.vo.OrderVO;

/**
 * 管理员订单服务接口
 */
public interface AdminOrderService extends IService<RepairOrder> {
    
    /**
     * 获取所有订单列表
     * @param status 订单状态(-1表示全部)
     * @param page 分页参数
     * @return 分页结果
     */
    Page<OrderVO> getOrderList(Integer status, Page<RepairOrder> page);
    
    /**
     * 获取订单详情
     * @param orderId 订单ID
     * @return 订单详情
     */
    OrderVO getOrderDetail(Long orderId);
    
    /**
     * 分配订单给指定维修人员
     * @param orderId 订单ID
     * @param repairerId 维修人员ID
     * @return 分配后的订单
     */
    OrderVO assignOrder(Long orderId, Long repairerId);
    
    /**
     * 取消订单
     * @param orderId 订单ID
     * @param reason 取消原因
     * @return 取消后的订单
     */
    OrderVO cancelOrder(Long orderId, String reason);
} 