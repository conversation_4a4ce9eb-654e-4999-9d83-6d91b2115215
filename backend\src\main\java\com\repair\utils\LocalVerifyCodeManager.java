package com.repair.utils;

import com.repair.config.VerifyCodeProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 本地内存验证码管理器
 * 在Redis不可用时使用
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "app.redis.fallback-to-memory", havingValue = "true")
public class LocalVerifyCodeManager implements InitializingBean, DisposableBean {
    
    // 使用本地内存存储验证码
    private final Map<String, VerifyCodeInfo> verifyCodeMap = new ConcurrentHashMap<>();
    
    // 存储验证码请求时间的Map
    private final Map<String, Long> verifyCodeRequestTimeMap = new ConcurrentHashMap<>();
    
    // 定时清理任务执行器
    private final ScheduledExecutorService scheduledExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread thread = new Thread(r, "verify-code-cleaner");
        thread.setDaemon(true);
        return thread;
    });
    
    @Autowired
    private VerifyCodeProperties verifyCodeProperties;
    
    /**
     * 验证码信息，包含验证码和过期时间
     */
    @Data
    @AllArgsConstructor
    static class VerifyCodeInfo {
        private String code;
        private LocalDateTime expireTime;
    }
    
    /**
     * 应用启动后自动调用，初始化定时任务
     */
    @Override
    public void afterPropertiesSet() {
        // 从配置中获取清理间隔
        long cleanupInterval = verifyCodeProperties.getCleanupInterval();
        
        // 启动定时清理任务
        scheduledExecutor.scheduleAtFixedRate(
            this::cleanupExpiredCodes,
            cleanupInterval,
            cleanupInterval,
            TimeUnit.SECONDS
        );
        log.info("本地验证码清理任务已启动，清理间隔: {}秒", cleanupInterval);
    }
    
    /**
     * 应用关闭时自动调用，关闭定时任务
     */
    @Override
    public void destroy() {
        scheduledExecutor.shutdown();
        try {
            if (!scheduledExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduledExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduledExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        log.info("本地验证码清理任务已关闭");
    }
    
    /**
     * 清理过期验证码
     */
    private void cleanupExpiredCodes() {
        try {
            int count = 0;
            LocalDateTime now = LocalDateTime.now();
            
            for (Map.Entry<String, VerifyCodeInfo> entry : verifyCodeMap.entrySet()) {
                if (now.isAfter(entry.getValue().getExpireTime())) {
                    verifyCodeMap.remove(entry.getKey());
                    count++;
                }
            }
            
            if (count > 0) {
                log.info("定时清理过期验证码完成，共清理 {} 条", count);
            }
            
            // 同时清理过期的请求时间记录
            cleanupExpiredRequestTimes();
        } catch (Exception e) {
            log.error("清理过期验证码时发生错误", e);
        }
    }
    
    /**
     * 检查验证码请求是否过于频繁
     *
     * @param phone 手机号
     * @param minInterval 最小间隔时间（秒）
     * @return 是否过于频繁
     */
    public boolean isVerifyCodeRequestTooFrequent(String phone, long minInterval) {
        try {
            String key = "verify_code_last_request:" + phone;
            Long lastRequestTime = verifyCodeRequestTimeMap.get(key);
            
            if (lastRequestTime == null) {
                // 第一次请求
                return false;
            }
            
            // 计算上次请求到现在的时间差
            long now = System.currentTimeMillis();
            long diff = (now - lastRequestTime) / 1000; // 转换为秒
            
            return diff < minInterval;
        } catch (Exception e) {
            log.error("检查验证码请求频率失败", e);
            // 发生错误时，不阻止用户请求
            return false;
        }
    }
    
    /**
     * 记录验证码请求时间
     *
     * @param phone 手机号
     */
    public void recordVerifyCodeRequestTime(String phone) {
        String key = "verify_code_last_request:" + phone;
        verifyCodeRequestTimeMap.put(key, System.currentTimeMillis());
    }
    
    /**
     * 清理过期的请求时间记录
     * 在定期清理验证码的同时，也清理长时间未使用的请求时间记录
     */
    private void cleanupExpiredRequestTimes() {
        try {
            int count = 0;
            long now = System.currentTimeMillis();
            long oneDayInMillis = 24 * 60 * 60 * 1000;
            
            for (Map.Entry<String, Long> entry : verifyCodeRequestTimeMap.entrySet()) {
                if (now - entry.getValue() > oneDayInMillis) {
                    verifyCodeRequestTimeMap.remove(entry.getKey());
                    count++;
                }
            }
            
            if (count > 0) {
                log.info("定时清理过期请求时间记录完成，共清理 {} 条", count);
            }
        } catch (Exception e) {
            log.error("清理过期请求时间记录时发生错误", e);
        }
    }
    
    /**
     * 保存验证码
     *
     * @param phone      手机号
     * @param verifyCode 验证码
     * @param expireTime 过期时间（秒）
     */
    public void saveVerifyCode(String phone, String verifyCode, long expireTime) {
        String key = "verify_code:" + phone;
        LocalDateTime expire = LocalDateTime.now().plusSeconds(expireTime);
        verifyCodeMap.put(key, new VerifyCodeInfo(verifyCode, expire));
        log.info("保存验证码到本地内存：phone={}, code={}, expireTime={}", phone, verifyCode, expire);
    }
    
    /**
     * 获取验证码
     *
     * @param phone 手机号
     * @return 验证码
     */
    public String getVerifyCode(String phone) {
        String key = "verify_code:" + phone;
        VerifyCodeInfo info = verifyCodeMap.get(key);
        
        if (info == null) {
            return null;
        }
        
        // 检查是否过期
        if (LocalDateTime.now().isAfter(info.getExpireTime())) {
            verifyCodeMap.remove(key);
            return null;
        }
        
        return info.getCode();
    }
    
    /**
     * 验证验证码
     *
     * @param phone      手机号
     * @param verifyCode 验证码
     * @return 是否有效
     */
    public boolean validateVerifyCode(String phone, String verifyCode) {
        String savedCode = getVerifyCode(phone);
        return savedCode != null && savedCode.equals(verifyCode);
    }
    
    /**
     * 删除验证码
     *
     * @param phone 手机号
     */
    public void deleteVerifyCode(String phone) {
        String key = "verify_code:" + phone;
        verifyCodeMap.remove(key);
        log.info("从本地内存删除验证码：phone={}", phone);
    }
    
    /**
     * 获取当前缓存的验证码数量（用于测试）
     */
    public int getVerifyCodeCount() {
        return verifyCodeMap.size();
    }
} 