// 地址数据接口
export interface AddressData {
  province: string;          // 省份
  city: string;             // 城市
  district: string;         // 区县
  street: string;           // 街道
  streetNumber?: string;    // 门牌号
  formattedAddress: string; // 格式化地址
  latitude: number;         // 纬度
  longitude: number;        // 经度
  accuracy?: number;        // 精度
  detailAddress?: string;   // 详细地址（用户补充）
}

// 位置数据接口
export interface LocationResult {
  latitude: number;
  longitude: number;
  accuracy: number;
  altitude?: number;
  speed?: number;
}

// 权限检查结果
export interface PermissionResult {
  authorized: boolean;
  denied: boolean;
  restricted: boolean;
}

// 地址输入组件配置
export interface AddressInputConfig {
  showCurrentLocation: boolean;  // 是否显示获取当前位置按钮
  showMapPicker: boolean;        // 是否显示地图选择按钮
  required: boolean;             // 是否必填
  placeholder: string;           // 占位符
  maxDetailLength: number;       // 详细地址最大长度
}

// 腾讯地图逆地址解析结果接口
export interface TencentMapGeocodeResult {
  status: number;
  message: string;
  result: {
    address: string;
    formatted_addresses: {
      recommend: string;
      rough: string;
    };
    address_component: {
      nation: string;
      province: string;
      city: string;
      district: string;
      street: string;
      street_number: string;
    };
    ad_info: {
      nation_code: string;
      adcode: string;
      city_code: string;
      name: string;
      location: {
        lat: number;
        lng: number;
      };
    };
  };
}

// 腾讯地图搜索结果接口
export interface TencentMapSearchResult {
  status: number;
  message: string;
  data: Array<{
    id: string;
    title: string;
    address: string;
    tel: string;
    category: string;
    type: number;
    location: {
      lat: number;
      lng: number;
    };
    ad_info: {
      adcode: string;
      province: string;
      city: string;
      district: string;
    };
  }>;
}
