package com.repair.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.repair.common.Result;
import com.repair.dto.OrderDTO;
import com.repair.dto.RatingDTO;
import com.repair.entity.RepairOrder;
import com.repair.service.OrderService;
import com.repair.utils.JwtUtil;
import com.repair.vo.OrderVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 订单控制器
 */
@RestController
@RequestMapping("/api/order")
@Validated
public class OrderController {

    private final OrderService orderService;
    private final JwtUtil jwtUtil;

    public OrderController(OrderService orderService, JwtUtil jwtUtil) {
        this.orderService = orderService;
        this.jwtUtil = jwtUtil;
    }

    /**
     * 创建订单
     */
    @PostMapping("/create")
    public Result<OrderVO> createOrder(@RequestBody @Valid OrderDTO orderDTO, HttpServletRequest request) {
        Long userId = jwtUtil.getUserIdFromRequest(request);
        OrderVO order = orderService.createOrder(userId, orderDTO);
        return Result.success(order);
    }

    /**
     * 获取用户订单列表
     */
    @GetMapping("/list")
    public Result<Page<OrderVO>> getUserOrders(
            @RequestParam(value = "status", required = false, defaultValue = "-1") Integer status,
            @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            HttpServletRequest request) {
        Long userId = jwtUtil.getUserIdFromRequest(request);
        Page<RepairOrder> pageParam = new Page<>(page, size);
        Page<OrderVO> orders = orderService.getUserOrders(userId, status, pageParam);
        return Result.success(orders);
    }

    /**
     * 获取订单详情
     */
    @GetMapping("/detail/{orderId}")
    public Result<OrderVO> getOrderDetail(@PathVariable("orderId") Long orderId) {
        OrderVO order = orderService.getOrderDetail(orderId);
        // 权限验证已经在Service层完成，不需要在Controller层重复验证
        return Result.success(order);
    }

    /**
     * 取消订单
     */
    @PostMapping("/cancel/{orderId}")
    public Result<OrderVO> cancelOrder(@PathVariable("orderId") Long orderId, HttpServletRequest request) {
        Long userId = jwtUtil.getUserIdFromRequest(request);
        OrderVO order = orderService.cancelOrder(userId, orderId);
        return Result.success(order);
    }

    /**
     * 评价订单
     */
    @PostMapping("/rate/{orderId}")
    public Result<OrderVO> rateOrder(
            @PathVariable("orderId") Long orderId,
            @RequestBody @Valid RatingDTO ratingDTO,
            HttpServletRequest request) {
        Long userId = jwtUtil.getUserIdFromRequest(request);
        OrderVO order = orderService.rateOrder(userId, orderId, ratingDTO.getRating(), ratingDTO.getComment());
        return Result.success(order);
    }
} 