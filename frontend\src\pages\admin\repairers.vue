<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useAdminStore } from '@/store/admin';
import { adminApi } from '@/api/admin';
import type { RepairerInfo } from '@/api/repairer';
import type { RepairerAddParams } from '@/api/admin';
import CommonBackButton from '@/components/common/BackButton.vue';
import PasswordInput from '@/components/common/PasswordInput.vue';

const adminStore = useAdminStore();

// 页面数据
const loading = ref(false);
const refreshing = ref(false);
const keyword = ref('');
const repairerList = ref<RepairerInfo[]>([]);
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0,
  hasMore: true
});

// 新增维修师相关数据
const showAddDialog = ref(false);
const addFormLoading = ref(false);
const addForm = reactive<RepairerAddParams>({
  username: '',
  password: '',
  phone: '',
  skillTags: ''
});
const addFormErrors = reactive({
  username: '',
  password: '',
  phone: '',
  skillTags: ''
});

// 修改密码相关数据
const showPasswordDialog = ref(false);
const passwordFormLoading = ref(false);
const currentRepairerId = ref<number>();
const passwordForm = reactive({
  password: '',
  confirmPassword: ''
});
const passwordFormErrors = reactive({
  password: '',
  confirmPassword: ''
});

// 页面初始化
onMounted(() => {
  // 验证登录状态
  if (!adminStore.token) {
    uni.redirectTo({ url: './login' });
    return;
  }
  
  // 加载维修师列表
  loadRepairerList();
});

// 加载维修师列表
async function loadRepairerList(reset = true) {
  if (reset) {
    pagination.page = 1;
    pagination.hasMore = true;
  }
  
  if (!pagination.hasMore || loading.value) return;
  
  try {
    loading.value = true;
    
    const params: any = {
      page: pagination.page,
      size: pagination.size
    };
    
    // 只有当关键词有效时才添加到查询参数
    if (keyword.value && keyword.value.trim() !== '') {
      params.keyword = keyword.value.trim();
    }
    
    const { data } = await adminApi.getRepairerList(params);
    
    if (reset) {
      repairerList.value = data.records;
    } else {
      repairerList.value = [...repairerList.value, ...data.records];
    }
    
    pagination.total = data.total;
    pagination.hasMore = repairerList.value.length < pagination.total;
    pagination.page++;
  } catch (error) {
    console.error('获取维修师列表失败', error);
    uni.showToast({
      title: '获取维修师列表失败',
      icon: 'none'
    });
  } finally {
    loading.value = false;
    if (refreshing.value) {
      uni.stopPullDownRefresh();
      refreshing.value = false;
    }
  }
}

// 搜索维修师
function searchRepairers() {
  loadRepairerList(true);
}

// 清空搜索
function clearSearch() {
  keyword.value = '';
  loadRepairerList(true);
}

// 下拉刷新
function onPullDownRefresh() {
  refreshing.value = true;
  loadRepairerList(true);
}

// 上拉加载更多
function onReachBottom() {
  if (pagination.hasMore && !loading.value) {
    loadRepairerList(false);
  }
}

// 修改维修师状态
async function toggleRepairerStatus(repairer: RepairerInfo) {
  try {
    const newStatus = repairer.status === 1 ? 0 : 1;
    
    await adminApi.updateRepairerStatus(repairer.id, newStatus);
    
    // 更新本地数据
    repairer.status = newStatus;
    
    uni.showToast({
      title: newStatus === 1 ? '已启用维修师' : '已禁用维修师',
      icon: 'success'
    });
  } catch (error) {
    console.error('修改维修师状态失败', error);
    uni.showToast({
      title: '操作失败',
      icon: 'none'
    });
  }
}

// 打开添加维修师弹窗
function openAddDialog() {
  // 重置表单
  Object.keys(addForm).forEach(key => {
    addForm[key as keyof RepairerAddParams] = '';
  });
  
  // 重置错误提示
  Object.keys(addFormErrors).forEach(key => {
    addFormErrors[key as keyof typeof addFormErrors] = '';
  });
  
  showAddDialog.value = true;
}

// 关闭添加维修师弹窗
function closeAddDialog() {
  showAddDialog.value = false;
}

// 验证添加维修师表单
function validateAddForm(): boolean {
  let isValid = true;
  
  // 重置错误提示
  Object.keys(addFormErrors).forEach(key => {
    addFormErrors[key as keyof typeof addFormErrors] = '';
  });
  
  // 验证用户名
  if (!addForm.username) {
    addFormErrors.username = '请输入用户名';
    isValid = false;
  } else if (addForm.username.length < 2 || addForm.username.length > 20) {
    addFormErrors.username = '用户名长度应为2-20个字符';
    isValid = false;
  }
  
  // 验证密码
  if (!addForm.password) {
    addFormErrors.password = '请输入密码';
    isValid = false;
  } else if (!/^[a-zA-Z0-9_]{6,20}$/.test(addForm.password)) {
    addFormErrors.password = '密码必须为6-20位字母、数字或下划线';
    isValid = false;
  }
  
  // 验证手机号
  if (!addForm.phone) {
    addFormErrors.phone = '请输入手机号';
    isValid = false;
  } else if (!/^1[3-9]\d{9}$/.test(addForm.phone)) {
    addFormErrors.phone = '请输入有效的手机号';
    isValid = false;
  }
  
  return isValid;
}

// 提交添加维修师表单
async function submitAddForm() {
  // 验证表单
  if (!validateAddForm()) {
    return;
  }
  
  try {
    addFormLoading.value = true;
    
    // 处理技能标签中的中英文逗号
    const normalizedSkillTags = addForm.skillTags
      .replace(/，/g, ',') // 将中文逗号替换为英文逗号
      .split(',') // 分割成数组
      .map(tag => tag.trim()) // 去除首尾空格
      .filter(tag => tag) // 过滤空字符串
      .join(','); // 重新用英文逗号连接

    addForm.skillTags = normalizedSkillTags;
    
    await adminApi.addRepairer(addForm);
    
    uni.showToast({
      title: '添加维修师成功',
      icon: 'success'
    });
    
    // 关闭弹窗
    closeAddDialog();
    
    // 重新加载维修师列表
    loadRepairerList(true);
  } catch (error: any) {
    console.error('添加维修师失败', error);
    
    // 根据错误信息显示提示
    if (error?.data?.message?.includes('用户名已存在')) {
      addFormErrors.username = '该用户名已存在';
    } else if (error?.data?.message?.includes('手机号已存在')) {
      addFormErrors.phone = '该手机号已被使用';
    } else {
      uni.showToast({
        title: error?.data?.message || '添加维修师失败',
        icon: 'none'
      });
    }
  } finally {
    addFormLoading.value = false;
  }
}

// 格式化日期
function formatDate(dateString?: string) {
  if (!dateString) return '-';
  
  // 处理Java LocalDateTime格式的时间字符串
  // 例如："2023-04-16T14:30:45" 或 "2023-04-16T14:30:45.123"
  try {
    // 移除可能存在的毫秒部分和时区信息
    const cleanDateStr = dateString.split('.')[0].replace('Z', '');
    const date = new Date(cleanDateStr.replace('T', ' '));
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.warn('Invalid date:', dateString);
      return '-';
    }
    
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  } catch (error) {
    console.error('日期格式化错误:', error);
    return '-';
  }
}

// 格式化技能标签
function formatSkillTags(tags?: string) {
  if (!tags) return '';
  // 将英文逗号替换为中文逗号，使显示更加美观
  return tags.split(',').join('，');
}

// 打开修改密码弹窗
function openPasswordDialog(repairer: RepairerInfo) {
  currentRepairerId.value = repairer.id;
  passwordForm.password = '';
  passwordForm.confirmPassword = '';
  // 重置错误提示
  passwordFormErrors.password = '';
  passwordFormErrors.confirmPassword = '';
  showPasswordDialog.value = true;
}

// 关闭修改密码弹窗
function closePasswordDialog() {
  showPasswordDialog.value = false;
  currentRepairerId.value = undefined;
}

// 验证密码表单
function validatePasswordForm(): boolean {
  let isValid = true;
  
  // 重置错误提示
  passwordFormErrors.password = '';
  passwordFormErrors.confirmPassword = '';
  
  // 验证新密码
  if (!passwordForm.password) {
    passwordFormErrors.password = '请输入新密码';
    isValid = false;
  } else if (!/^[a-zA-Z0-9_]{6,20}$/.test(passwordForm.password)) {
    passwordFormErrors.password = '密码必须为6-20位字母、数字或下划线';
    isValid = false;
  }
  
  // 验证确认密码
  if (!passwordForm.confirmPassword) {
    passwordFormErrors.confirmPassword = '请确认新密码';
    isValid = false;
  } else if (passwordForm.confirmPassword !== passwordForm.password) {
    passwordFormErrors.confirmPassword = '两次输入的密码不一致';
    isValid = false;
  }
  
  return isValid;
}

// 提交修改密码
async function submitPasswordForm() {
  if (!validatePasswordForm() || !currentRepairerId.value) {
    return;
  }
  
  try {
    passwordFormLoading.value = true;
    await adminApi.updateRepairerPassword(currentRepairerId.value, passwordForm.password);
    
    uni.showToast({
      title: '密码修改成功',
      icon: 'success'
    });
    
    closePasswordDialog();
  } catch (error: any) {
    console.error('修改密码失败', error);
    uni.showToast({
      title: error?.data?.message || '修改密码失败',
      icon: 'none'
    });
  } finally {
    passwordFormLoading.value = false;
  }
}
</script>

<template>
  <view class="container">
    <!-- 使用统一的返回按钮组件 -->
    <common-back-button></common-back-button>
    
    <!-- 顶部标题栏 -->
    <view class="header">
      <text class="title">维修师管理</text>
      <view class="add-button" @click="openAddDialog">
        <text class="add-icon">+</text>
        <text class="add-text">新增</text>
      </view>
    </view>
    
    <!-- 搜索区域 -->
    <view class="search-container">
      <view class="search-box">
        <view class="search-icon">🔍</view>
        <input 
          type="text" 
          v-model="keyword" 
          class="search-input" 
          placeholder="搜索用户名/手机号/技能" 
          confirm-type="search"
          @confirm="searchRepairers"
        />
        <text v-if="keyword" class="clear-icon" @click="clearSearch">✕</text>
      </view>
      <view class="search-button" @click="searchRepairers">搜索</view>
    </view>
    
    <!-- 维修师列表 -->
    <view class="repairer-list" v-if="repairerList.length > 0">
      <view v-for="repairer in repairerList" :key="repairer.id" class="repairer-card">
        <view class="repairer-info">
          <view class="repairer-avatar">
            <text class="avatar-text">{{ repairer.username?.substring(0, 1) || '修' }}</text>
          </view>
          <view class="repairer-detail">
            <view class="repairer-name-row">
              <text class="repairer-name">{{ repairer.username }}</text>
              <view :class="['status-tag', repairer.status === 1 ? 'status-active' : 'status-disabled']">
                {{ repairer.status === 1 ? '正常' : '禁用' }}
              </view>
            </view>
            <text class="repairer-phone">{{ repairer.phone || '未设置手机号' }}</text>
            <text class="repairer-skills">技能: {{ formatSkillTags(repairer.skillTags) || '未设置技能' }}</text>
            <text class="repairer-date">注册时间：{{ formatDate(repairer.createTime) }}</text>
          </view>
        </view>
        <view class="repairer-actions">
          <view 
            class="action-button password-button"
            @click="openPasswordDialog(repairer)"
          >
            改密码
          </view>
          <view 
            :class="['action-button', repairer.status === 1 ? 'disable-button' : 'enable-button']"
            @click="toggleRepairerStatus(repairer)"
          >
            {{ repairer.status === 1 ? '禁用' : '启用' }}
          </view>
        </view>
      </view>
      
      <!-- 加载更多提示 -->
      <view class="loading-more" v-if="pagination.hasMore">
        <text class="loading-text">加载更多...</text>
      </view>
      <view class="no-more" v-else>
        <text class="no-more-text">没有更多数据了</text>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" v-else-if="!loading">
      <text class="empty-icon">📭</text>
      <text class="empty-text">暂无维修师数据</text>
    </view>
    
    <!-- 加载中 -->
    <view class="loading-container" v-if="loading && repairerList.length === 0">
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 新增维修师弹窗 -->
    <view class="add-dialog-mask" v-if="showAddDialog" @click="closeAddDialog"></view>
    <view class="add-dialog" v-if="showAddDialog">
      <view class="add-dialog-header">
        <text class="add-dialog-title">新增维修师</text>
        <text class="add-dialog-close" @click="closeAddDialog">✕</text>
      </view>
      <view class="add-dialog-content">
        <view class="form-group">
          <text class="form-label">用户名</text>
          <input 
            class="form-input" 
            type="text" 
            v-model="addForm.username" 
            placeholder="请输入用户名"
          />
          <text v-if="addFormErrors.username" class="form-error">{{ addFormErrors.username }}</text>
        </view>
        
        <view class="form-group">
          <text class="form-label">密码</text>
          <password-input 
            v-model="addForm.password" 
            placeholder="请输入密码" 
          />
          <text v-if="addFormErrors.password" class="form-error">{{ addFormErrors.password }}</text>
        </view>
        
        <view class="form-group">
          <text class="form-label">手机号</text>
          <input 
            class="form-input" 
            type="number" 
            maxlength="11"
            v-model="addForm.phone" 
            placeholder="请输入手机号"
          />
          <text v-if="addFormErrors.phone" class="form-error">{{ addFormErrors.phone }}</text>
        </view>
        
        <view class="form-group">
          <text class="form-label">技能标签</text>
          <input 
            class="form-input" 
            type="text" 
            v-model="addForm.skillTags" 
            placeholder="多个标签用逗号分隔（支持中英文逗号）"
          />
          <text class="form-tip">例如：水电维修，家具安装,空调维修</text>
        </view>
      </view>
      <view class="add-dialog-footer">
        <view class="dialog-btn cancel-btn" @click="closeAddDialog">取消</view>
        <view class="dialog-btn confirm-btn" :class="{ 'loading-btn': addFormLoading }" @click="submitAddForm">
          {{ addFormLoading ? '提交中...' : '确认添加' }}
        </view>
      </view>
    </view>

    <!-- 修改密码弹窗 -->
    <view class="add-dialog-mask" v-if="showPasswordDialog" @click="closePasswordDialog"></view>
    <view class="add-dialog" v-if="showPasswordDialog">
      <view class="add-dialog-header">
        <text class="add-dialog-title">修改密码</text>
        <text class="add-dialog-close" @click="closePasswordDialog">✕</text>
      </view>
      <view class="add-dialog-content">
        <view class="form-group">
          <text class="form-label">新密码</text>
          <password-input 
            v-model="passwordForm.password" 
            placeholder="请输入新密码" 
          />
          <text v-if="passwordFormErrors.password" class="form-error">{{ passwordFormErrors.password }}</text>
        </view>
        
        <view class="form-group">
          <text class="form-label">确认密码</text>
          <password-input 
            v-model="passwordForm.confirmPassword" 
            placeholder="请再次输入新密码" 
          />
          <text v-if="passwordFormErrors.confirmPassword" class="form-error">{{ passwordFormErrors.confirmPassword }}</text>
        </view>
      </view>
      <view class="add-dialog-footer">
        <view class="dialog-btn cancel-btn" @click="closePasswordDialog">取消</view>
        <view 
          class="dialog-btn confirm-btn" 
          :class="{ 'loading-btn': passwordFormLoading }" 
          @click="submitPasswordForm"
        >
          {{ passwordFormLoading ? '提交中...' : '确认修改' }}
        </view>
      </view>
    </view>
  </view>
</template>

<style>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

.header {
  height: 96rpx;
  margin-top: 160rpx; /* 为返回按钮留出空间 */
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
  position: sticky;
  top: 0;
  z-index: 100;
  border-bottom: 1px solid #f0f0f0;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

.add-button {
  display: flex;
  align-items: center;
  background-color: #409eff;
  border-radius: 8rpx;
  padding: 8rpx 16rpx;
}

.add-icon {
  font-size: 28rpx;
  color: #fff;
  margin-right: 4rpx;
}

.add-text {
  font-size: 28rpx;
  color: #fff;
}

.search-container {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.search-box {
  flex: 1;
  height: 72rpx;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  margin-right: 20rpx;
}

.search-icon {
  font-size: 28rpx;
  color: #999;
  margin-right: 12rpx;
}

.search-input {
  flex: 1;
  height: 72rpx;
  font-size: 28rpx;
  color: #333;
}

.clear-icon {
  font-size: 28rpx;
  color: #999;
  padding: 10rpx;
}

.search-button {
  font-size: 28rpx;
  color: #409eff;
}

.repairer-list {
  padding: 24rpx;
}

.repairer-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.repairer-info {
  display: flex;
  margin-bottom: 24rpx;
}

.repairer-avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  background-color: #ff9800;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.avatar-text {
  font-size: 40rpx;
  color: #ffffff;
  font-weight: bold;
}

.repairer-detail {
  flex: 1;
}

.repairer-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.repairer-name {
  font-size: 32rpx;
  color: #333;
  margin-right: 16rpx;
}

.status-tag {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.status-active {
  background-color: #e8f5e9;
  color: #4caf50;
}

.status-disabled {
  background-color: #ffebee;
  color: #f44336;
}

.repairer-phone {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.repairer-skills {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.repairer-date {
  font-size: 24rpx;
  color: #999;
}

.repairer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
  border-top: 1px solid #f0f0f0;
  padding-top: 24rpx;
}

.password-button {
  background-color: #e6f7ff;
  color: #1890ff;
}

.action-button {
  font-size: 28rpx;
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
}

.disable-button {
  background-color: #ffebee;
  color: #f44336;
}

.enable-button {
  background-color: #e8f5e9;
  color: #4caf50;
}

.loading-more, .no-more {
  text-align: center;
  padding: 32rpx 0;
}

.loading-text, .no-more-text {
  font-size: 28rpx;
  color: #999;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.loading-container {
  padding: 80rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 新增维修师弹窗样式 */
.add-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.add-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 650rpx;
  background-color: #fff;
  border-radius: 16rpx;
  z-index: 1000;
  overflow: hidden;
}

.add-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.add-dialog-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
}

.add-dialog-close {
  font-size: 32rpx;
  color: #999;
  padding: 10rpx;
}

.add-dialog-content {
  padding: 30rpx;
  max-height: 800rpx;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.form-error {
  display: block;
  font-size: 24rpx;
  color: #f44336;
  margin-top: 8rpx;
}

.form-tip {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.add-dialog-footer {
  display: flex;
  border-top: 1rpx solid #eee;
}

.dialog-btn {
  flex: 1;
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 32rpx;
}

.cancel-btn {
  color: #666;
  border-right: 1rpx solid #eee;
}

.confirm-btn {
  color: #fff;
  background-color: #409eff;
}

.loading-btn {
  opacity: 0.8;
}
</style> 