<template>
  <puzzle-verify
    :visible="visible"
    :title="title"
    @success="$emit('success')"
    @fail="$emit('fail')"
    @cancel="$emit('cancel')"
    @update:visible="$emit('update:visible', $event)"
  />
</template>

<script setup lang="ts">
import { defineComponent } from 'vue';
import PuzzleVerify from './PuzzleVerify.vue';

// 确保组件有一个明确的名称
defineComponent({
  name: 'SlideVerify'
});

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '安全验证'
  }
});

// 定义组件事件
defineEmits(['success', 'fail', 'cancel', 'update:visible']);
</script>

<style>
/* 样式由PuzzleVerify组件提供 */
</style> 