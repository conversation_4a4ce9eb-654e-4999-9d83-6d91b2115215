package com.repair.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 微信小程序绑定手机号DTO
 * code和openid至少有一个不为空
 */
@Data
public class WxBindPhoneDTO {
    /**
     * 微信登录临时凭证
     * 与openid字段二选一，至少有一个不为空
     */
    private String code;
    
    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    /**
     * 手机验证码（如果需要）
     */
    private String phoneCode;
    
    /**
     * 微信加密数据（小程序获取手机号时使用）
     */
    private String encryptedData;
    
    /**
     * 加密算法初始向量
     */
    private String iv;
    
    /**
     * 微信用户唯一标识
     * 与code字段二选一，至少有一个不为空
     */
    private String openid;
    
    /**
     * 微信会话密钥
     */
    private String sessionKey;
    
    /**
     * 验证code和openid至少有一个不为空
     * 用于自定义验证逻辑
     * 
     * @return 是否满足验证条件
     */
    public boolean isValid() {
        return (code != null && !code.isEmpty()) || (openid != null && !openid.isEmpty());
    }
} 