package com.repair.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 地址信息DTO
 */
@Data
public class AddressDTO {
    /**
     * 纬度
     */
    @NotNull(message = "纬度不能为空")
    @DecimalMin(value = "-90.0", message = "纬度范围应在-90到90之间")
    @DecimalMax(value = "90.0", message = "纬度范围应在-90到90之间")
    private BigDecimal latitude;

    /**
     * 经度
     */
    @NotNull(message = "经度不能为空")
    @DecimalMin(value = "-180.0", message = "经度范围应在-180到180之间")
    @DecimalMax(value = "180.0", message = "经度范围应在-180到180之间")
    private BigDecimal longitude;
    
    /**
     * 省份
     */
    @Length(max = 50, message = "省份名称不能超过50个字符")
    private String province;
    
    /**
     * 城市
     */
    @Length(max = 50, message = "城市名称不能超过50个字符")
    private String city;
    
    /**
     * 区县
     */
    @Length(max = 50, message = "区县名称不能超过50个字符")
    private String district;
    
    /**
     * 街道
     */
    @Length(max = 100, message = "街道名称不能超过100个字符")
    private String street;
    
    /**
     * 门牌号
     */
    @Length(max = 50, message = "门牌号不能超过50个字符")
    private String streetNumber;
    
    /**
     * 详细地址
     */
    @Length(max = 200, message = "详细地址不能超过200个字符")
    private String detailAddress;
    
    /**
     * 格式化地址
     */
    @NotBlank(message = "格式化地址不能为空")
    @Length(max = 500, message = "格式化地址不能超过500个字符")
    private String formattedAddress;
    
    /**
     * 定位精度
     */
    private BigDecimal accuracy;
}
