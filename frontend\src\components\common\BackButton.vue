<template>
  <view :style="navBackStyle" class="back-button-container" @click="goBack">
    <view class="back-button">
      <view class="back-arrow"></view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';

// 状态
const statusBarHeight = ref(20);
const navBarHeight = ref(44);
const safeTop = ref(0);

// 计算样式
const navBackStyle = computed(() => {
  return {
    paddingTop: safeTop.value + 'px',
    height: navBarHeight.value + 'px',
  };
});

// 返回上一页
function goBack() {
  // 捕获可能的错误
  try {
    // 添加日志，记录返回操作
    console.log('返回按钮点击，准备退回上一页');
    
    // 检查当前是否在首页
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const currentRoute = currentPage?.route || '';
    
    console.log('当前页面路径:', currentRoute);
    
    // 如果当前是首页或TabBar页面，直接跳转到首页而不是尝试返回
    if (currentRoute.includes('pages/index/index') || pages.length <= 1) {
      console.log('当前在首页或TabBar页面，直接跳转到首页');
      uni.switchTab({
        url: '/pages/index/index',
        success() {
          console.log('成功跳转到首页');
        },
        fail(err) {
          console.error('跳转到首页失败:', err);
          // 如果switchTab失败，尝试其他方法
          uni.reLaunch({
            url: '/pages/index/index'
          });
        }
      });
      return;
    }
    
    // 尝试返回上一页
    uni.navigateBack({
      delta: 1,
      success() {
        console.log('返回上一页成功');
      },
      fail(err) {
        console.error('返回上一页失败，尝试回到首页:', err);
        
        // 延迟执行以避免快速连续导航引起的问题
        setTimeout(() => {
          // 优先使用switchTab回到首页
          uni.switchTab({
            url: '/pages/index/index',
            success() {
              console.log('成功跳转到首页');
            },
            fail(switchTabErr) {
              console.error('使用switchTab返回首页失败:', switchTabErr);
              
              // 如果 switchTab 失败，尝试 reLaunch
              setTimeout(() => {
                uni.reLaunch({
                  url: '/pages/index/index',
                  success() {
                    console.log('使用reLaunch成功跳转到首页');
                  },
                  fail(reLaunchErr) {
                    console.error('使用reLaunch也失败:', reLaunchErr);
                    
                    // 如果 reLaunch 也失败，尝试 redirectTo
                    setTimeout(() => {
                      uni.redirectTo({
                        url: '/pages/index/index',
                        success() {
                          console.log('使用redirectTo成功跳转到首页');
                        },
                        fail(redirectErr) {
                          console.error('所有导航方法都失败:', redirectErr);
                          
                          // 所有导航方法都失败时，显示提示
                          uni.showToast({
                            title: '页面跳转失败，请重启应用',
                            icon: 'none',
                            duration: 2000
                          });
                        }
                      });
                    }, 300);
                  }
                });
              }, 300);
            }
          });
        }, 300);
      }
    });
  } catch (error) {
    console.error('返回按钮执行异常:', error);
    
    // 显示错误提示
    uni.showToast({
      title: '操作异常，请重试',
      icon: 'none',
      duration: 2000
    });
    
    // 尝试回到首页
    setTimeout(() => {
      uni.switchTab({ 
        url: '/pages/index/index',
        fail() {
          uni.reLaunch({ url: '/pages/index/index' });
        }
      });
    }, 1000);
  }
}

onMounted(() => {
  try {
    // 获取系统信息
    const systemInfo = uni.getSystemInfoSync();
    console.log('系统信息:', JSON.stringify(systemInfo));
    
    // 根据不同平台设置顶部安全距离
    statusBarHeight.value = systemInfo.statusBarHeight || 20;
    safeTop.value = statusBarHeight.value;
    
    // 在控制台打印信息用于调试
    console.log('状态栏高度:', statusBarHeight.value);
    console.log('导航栏高度:', navBarHeight.value);
    console.log('安全顶部距离:', safeTop.value);
  } catch (e) {
    console.error('获取系统信息失败:', e);
    // 使用默认值
    statusBarHeight.value = 20;
    safeTop.value = 20;
  }
});
</script>

<style>
.back-button-container {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 999;
  display: flex;
  align-items: center;
  padding-left: 30rpx;
}

.back-button {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-arrow {
  width: 20rpx;
  height: 20rpx;
  border-left: 3rpx solid #333;
  border-bottom: 3rpx solid #333;
  transform: rotate(45deg);
  margin-left: 10rpx;
}
</style> 