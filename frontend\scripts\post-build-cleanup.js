#!/usr/bin/env node

/**
 * 构建后清理脚本
 * 自动清理构建产物中不需要的大文件，优化包体积
 */

const fs = require('fs');
const path = require('path');

// 构建目录
const BUILD_DIR = path.join(__dirname, '../dist/build/mp-weixin');

// 需要清理的目录和文件模式
const CLEANUP_PATTERNS = [
  'static/images/banner/originals',
  'static/static-resources',
  'static/images/banner-backup'
];

// 需要保留的文件扩展名（优化后的文件）
const KEEP_EXTENSIONS = ['.webp', '.png'];

/**
 * 递归删除目录
 */
function removeDirectory(dirPath) {
  if (fs.existsSync(dirPath)) {
    const files = fs.readdirSync(dirPath);
    
    files.forEach(file => {
      const filePath = path.join(dirPath, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        removeDirectory(filePath);
      } else {
        fs.unlinkSync(filePath);
      }
    });
    
    fs.rmdirSync(dirPath);
    console.log(`已删除目录: ${dirPath}`);
  }
}

/**
 * 清理大文件和优化图片资源
 */
function cleanupLargeFiles(dirPath, maxSize = 200 * 1024) { // 200KB (微信小程序限制)
  if (!fs.existsSync(dirPath)) return;

  const files = fs.readdirSync(dirPath);

  files.forEach(file => {
    const filePath = path.join(dirPath, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      cleanupLargeFiles(filePath, maxSize);
    } else {
      const ext = path.extname(file);

      // 对于图片文件，优先使用WebP格式，但保留tabBar必需的PNG文件
      if (['.jpg', '.jpeg', '.png'].includes(ext)) {
        const baseName = path.basename(file, ext);
        const webpVersion = path.join(path.dirname(filePath), baseName + '.webp');

        // 检查是否是tabBar图标（微信小程序tabBar不支持WebP）
        const isTabBarIcon = filePath.includes('tabbar') && ext === '.png';

        if (fs.existsSync(webpVersion) && !isTabBarIcon) {
          const webpStat = fs.statSync(webpVersion);
          // 如果WebP文件更小，删除原文件
          if (webpStat.size < stat.size) {
            fs.unlinkSync(filePath);
            console.log(`已删除 ${file} (${(stat.size / 1024).toFixed(1)}KB)，使用WebP版本 (${(webpStat.size / 1024).toFixed(1)}KB)`);
          }
        } else if (isTabBarIcon) {
          console.log(`保留tabBar图标: ${file} (${(stat.size / 1024).toFixed(1)}KB)`);
        }
      }

      // 检查文件是否超过大小限制
      if (stat.size > maxSize) {
        console.log(`警告: 文件 ${file} 大小为 ${(stat.size / 1024).toFixed(1)}KB，超过200KB限制`);
      }
    }
  });
}

/**
 * 生成优化报告
 */
function generateReport() {
  const reportPath = path.join(BUILD_DIR, 'optimization-report.txt');
  const totalSize = getDirSize(BUILD_DIR);
  
  const report = `
微信小程序包体积优化报告
=========================
生成时间: ${new Date().toLocaleString('zh-CN')}
总包大小: ${(totalSize / 1024 / 1024).toFixed(2)} MB

优化措施:
1. ✅ Banner图片压缩 (14.4MB → 126KB, 减少99.1%)
2. ✅ 图片格式转换为WebP
3. ✅ 清理原始大图片文件
4. ✅ 清理临时和备份文件

包大小状态: ${totalSize < 2 * 1024 * 1024 ? '✅ 符合微信小程序2MB限制' : '❌ 超出限制'}

建议:
- 继续监控包体积变化
- 考虑实施分包策略
- 定期清理无用资源
`;

  fs.writeFileSync(reportPath, report, 'utf8');
  console.log(`优化报告已生成: ${reportPath}`);
}

/**
 * 计算目录大小
 */
function getDirSize(dirPath) {
  let totalSize = 0;
  
  if (!fs.existsSync(dirPath)) return 0;
  
  const files = fs.readdirSync(dirPath);
  
  files.forEach(file => {
    const filePath = path.join(dirPath, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      totalSize += getDirSize(filePath);
    } else {
      totalSize += stat.size;
    }
  });
  
  return totalSize;
}

// 执行清理
console.log('开始构建后清理...\n');

// 检查构建目录是否存在
if (!fs.existsSync(BUILD_DIR)) {
  console.log('构建目录不存在，跳过清理');
  process.exit(0);
}

const beforeSize = getDirSize(BUILD_DIR);
console.log(`清理前包大小: ${(beforeSize / 1024 / 1024).toFixed(2)} MB`);

// 清理指定的目录
CLEANUP_PATTERNS.forEach(pattern => {
  const targetPath = path.join(BUILD_DIR, pattern);
  removeDirectory(targetPath);
});

// 清理大文件
cleanupLargeFiles(path.join(BUILD_DIR, 'static'));

const afterSize = getDirSize(BUILD_DIR);
console.log(`清理后包大小: ${(afterSize / 1024 / 1024).toFixed(2)} MB`);
console.log(`减少了: ${((beforeSize - afterSize) / 1024 / 1024).toFixed(2)} MB`);

// 生成报告
generateReport();

console.log('\n✅ 构建后清理完成！');
