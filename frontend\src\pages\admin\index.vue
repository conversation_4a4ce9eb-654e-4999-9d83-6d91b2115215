<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useAdminStore } from '@/store/admin';
import { type AdminInfo } from '@/api/admin';

const adminStore = useAdminStore();
const adminInfo = ref<AdminInfo>({
  id: 0,
  username: '',
  name: ''
});

onMounted(() => {
  // 验证登录状态
  if (!adminStore.token) {
    uni.redirectTo({ url: './login' });
    return;
  }
  
  // 更新管理员信息到组件状态
  if (adminStore.adminInfo) {
    adminInfo.value = adminStore.adminInfo;
  }
});

// 导航到不同页面
function navigateTo(page: string) {
  const pageMap: Record<string, string> = {
    userManagement: './users',
    repairerManagement: './repairers',
    orderManagement: './order-management',
    systemSettings: './system-settings'
  };

  if (pageMap[page]) {
    uni.navigateTo({
      url: pageMap[page]
    });
  }
}

// 退出登录
function logout() {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        adminStore.logout();
        uni.redirectTo({
          url: '../index/index'
        });
      }
    }
  });
}
</script>

<template>
  <view class="admin-index">
    <!-- 头部区域 -->
    <view class="header">
      <text class="title">管理员后台</text>
      <view class="user-info">
        <text class="welcome">欢迎您，{{ adminInfo.name || adminInfo.username }}</text>
        <text class="logout" @click="logout">退出登录</text>
      </view>
    </view>
    
    <!-- 功能区域 -->
    <view class="function-area">
      <!-- 用户管理 -->
      <view class="function-card" @click="navigateTo('userManagement')">
        <view class="card-icon user-icon">
          <text class="iconfont icon-user"></text>
        </view>
        <view class="card-content">
          <view class="card-title">用户管理</view>
          <view class="card-desc">管理系统用户信息</view>
        </view>
      </view>
      
      <!-- 维修师管理 -->
      <view class="function-card" @click="navigateTo('repairerManagement')">
        <view class="card-icon repairer-icon">
          <text class="iconfont icon-tool"></text>
        </view>
        <view class="card-content">
          <view class="card-title">维修师管理</view>
          <view class="card-desc">管理维修师信息</view>
        </view>
      </view>
      
      <!-- 订单管理 -->
      <view class="function-card" @click="navigateTo('orderManagement')">
        <view class="card-icon order-icon">
          <text class="iconfont icon-order"></text>
        </view>
        <view class="card-content">
          <view class="card-title">订单管理</view>
          <view class="card-desc">管理维修订单</view>
        </view>
      </view>
      
      <!-- 系统设置 -->
      <view class="function-card" @click="navigateTo('systemSettings')">
        <view class="card-icon settings-icon">
          <text class="iconfont icon-setting"></text>
        </view>
        <view class="card-content">
          <view class="card-title">系统设置</view>
          <view class="card-desc">管理系统配置</view>
        </view>
      </view>
    </view>
    
    <!-- 底部信息 -->
    <view class="footer">
      <text class="footer-text">快修侠维修管理系统 &copy; 2025</text>
    </view>
  </view>
</template>

<style>
.admin-index {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.title {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.user-info {
  display: flex;
  align-items: center;
}

.welcome {
  font-size: 16px;
  color: #606266;
  margin-right: 15px;
}

.logout {
  font-size: 14px;
  color: #f56c6c;
}

.function-area {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.function-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.function-card:active {
  transform: scale(0.98);
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20px;
  flex-shrink: 0;
}

.card-icon .iconfont {
  font-size: 30px;
  color: #fff;
}

.user-icon {
  background-color: #409eff;
}

.repairer-icon {
  background-color: #67c23a;
}

.order-icon {
  background-color: #e6a23c;
}

.settings-icon {
  background-color: #909399;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.card-desc {
  font-size: 14px;
  color: #909399;
}

.footer {
  margin-top: 50px;
  text-align: center;
}

.footer-text {
  font-size: 14px;
  color: #909399;
}
</style> 