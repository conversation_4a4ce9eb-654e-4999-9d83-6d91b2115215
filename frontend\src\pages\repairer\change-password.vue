<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRepairerStore } from '@/store/repairer';
import { repairerApi } from '@/api/repairer';
import CommonBackButton from '@/components/common/BackButton.vue';
import PasswordInput from '@/components/common/PasswordInput.vue';

const repairerStore = useRepairerStore();
const submitting = ref(false);

// 表单数据
const formData = reactive({
  newPassword: '',
  confirmPassword: ''
});

// 页面加载时检查登录状态
onMounted(() => {
  // 验证登录状态
  if (!repairerStore.token) {
    uni.redirectTo({ url: './login' });
    return;
  }
});

// 提交表单
async function submitForm() {
  // 表单验证
  if (!formData.newPassword) {
    uni.showToast({
      title: '请输入新密码',
      icon: 'none'
    });
    return;
  }
  
  if (formData.newPassword !== formData.confirmPassword) {
    uni.showToast({
      title: '两次输入的密码不一致',
      icon: 'none'
    });
    return;
  }
  
  if (formData.newPassword.length < 6) {
    uni.showToast({
      title: '密码长度至少为6位',
      icon: 'none'
    });
    return;
  }
  
  try {
    submitting.value = true;
    
    // 调用API修改密码
    await repairerApi.changePassword({
      newPassword: formData.newPassword
    });
    
    uni.showToast({
      title: '密码修改成功',
      icon: 'success'
    });
    
    // 清空表单
    formData.newPassword = '';
    formData.confirmPassword = '';
    
    // 延迟返回上一页
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  } catch (error) {
    console.error('修改密码失败', error);
    uni.showToast({
      title: '修改密码失败，请稍后重试',
      icon: 'none'
    });
  } finally {
    submitting.value = false;
  }
}

// 返回上一页
function goBack() {
  uni.navigateBack();
}
</script>

<template>
  <view class="container">
    <!-- 引入统一的返回按钮组件 -->
    <common-back-button></common-back-button>

    <!-- 页面内容 -->
    <view class="content">
      <!-- 页面标题 -->
      <view class="header">
        <text class="title">修改密码</text>
      </view>

      <!-- 表单区域 -->
      <view class="form-container">
        <!-- 新密码 -->
        <view class="form-item">
          <text class="label">新密码<text class="required">*</text></text>
          <password-input
            v-model="formData.newPassword"
            placeholder="请输入6-20位新密码"
            placeholder-class="placeholder"
          />
        </view>

        <!-- 确认密码 -->
        <view class="form-item">
          <text class="label">确认密码<text class="required">*</text></text>
          <password-input
            v-model="formData.confirmPassword"
            placeholder="请再次输入新密码"
            placeholder-class="placeholder"
          />
        </view>

        <!-- 提交按钮 -->
        <button
          class="submit-btn"
          :class="{'btn-loading': submitting}"
          :disabled="submitting"
          @click="submitForm"
        >
          <text v-if="submitting">提交中...</text>
          <text v-else>确认修改</text>
        </button>
      </view>
    </view>
  </view>
</template>

<style>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

.content {
  padding-top: 20rpx;
}

.header {
  position: relative;
  height: 96rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #f0f0f0;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

.form-container {
  padding: 32rpx 24rpx;
}

.form-item {
  padding: 24rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}

.required {
  color: #ff0000;
}

.input-wrapper {
  width: 100%;
}

.input {
  font-size: 32rpx;
  color: #333;
  width: 100%;
  height: 72rpx;
}

.placeholder {
  color: #999;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  font-size: 32rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 8rpx;
  margin: 0;
  background-color: #ff6b00;
  color: #ffffff;
}

.btn-loading {
  background-color: #ffc08f;
}
</style> 