package com.repair.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 维修师信息更新DTO
 */
@Data
public class RepairerInfoUpdateDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    private String username;
    
    /**
     * 技能标签，逗号分隔
     */
    private String skillTags;
} 