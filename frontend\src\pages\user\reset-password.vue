<template>
  <view class="container">
    <!-- 返回按钮 -->
    <common-back-button></common-back-button>

    <!-- 重置密码区域 -->
    <view class="reset-container">
      <!-- 标题文本 -->
      <view class="header-text">
        <text class="title">重置密码</text>
        <text class="subtitle">通过手机验证码重置您的密码</text>
      </view>

      <!-- 重置密码表单 -->
      <view class="form-container">
        <!-- 手机号输入 -->
        <view class="form-item">
          <text class="label">手机号码</text>
          <view class="input-wrapper">
            <input 
              type="number" 
              v-model="form.phone" 
              class="input" 
              placeholder="请输入注册手机号"
              placeholder-class="placeholder"
              maxlength="11"
              @input="clearErrorOnInput"
            />
          </view>
        </view>

        <!-- 验证码输入 -->
        <view class="form-item">
          <text class="label">验证码</text>
          <view class="input-wrapper code-wrapper">
            <input 
              type="number" 
              v-model="form.verifyCode" 
              class="input" 
              placeholder="请输入验证码"
              placeholder-class="placeholder"
              maxlength="6"
              @input="clearErrorOnInput"
            />
            <view 
              class="send-code-btn" 
              :class="{ 'disabled': countdownValue > 0 || isSending }"
              @click="sendVerificationCode"
            >
              <text v-if="isSending">发送中...</text>
              <text v-else-if="countdownValue > 0">{{ countdownValue }}秒</text>
              <text v-else>获取验证码</text>
            </view>
          </view>
        </view>

        <!-- 新密码输入 -->
        <view class="form-item">
          <text class="label">新密码</text>
          <password-input
            v-model="form.newPassword"
            placeholder="请输入新密码"
            placeholder-class="placeholder"
          />
        </view>
        
        <!-- 确认密码输入 -->
        <view class="form-item">
          <text class="label">确认密码</text>
          <password-input
            v-model="form.confirmPassword"
            placeholder="请再次输入新密码"
            placeholder-class="placeholder"
          />
        </view>

        <!-- 错误提示区域 -->
        <view class="error-message" v-if="errorMessage">
          <text>{{ errorMessage }}</text>
        </view>

        <!-- 重置密码按钮 -->
        <button 
          class="reset-btn"
          :class="{'btn-loading': loading}"
          :disabled="loading"
          @click="handleResetPassword"
        >
          <text v-if="loading">提交中...</text>
          <text v-else>重置密码</text>
        </button>

        <!-- 返回登录 -->
        <view class="back-to-login" @click="navigateToLogin">
          <text>返回登录</text>
        </view>
      </view>
    </view>
    
    <!-- 滑块验证组件 -->
    <puzzle-verify
      :visible="showCaptcha"
      @success="captchaService.handleCaptchaSuccess"
      @fail="captchaService.handleCaptchaFail"
      @cancel="captchaService.handleCaptchaCancel"
      @update:visible="showCaptcha = $event"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onUnmounted } from 'vue';
import { userApi } from '@/api/user';
import CommonBackButton from '@/components/common/BackButton.vue';
import { isValidPhone, sendVerifyCode, useCountdown } from '@/utils/verifycode';
import PasswordInput from '@/components/common/PasswordInput.vue';
import { captchaService } from '@/utils/captchaService';
import PuzzleVerify from '@/components/common/PuzzleVerify.vue';

const loading = ref(false);
const errorMessage = ref('');

// 表单数据
const form = reactive({
  phone: '',
  verifyCode: '',
  newPassword: '',
  confirmPassword: ''
});

// 滑块验证状态
const showCaptcha = computed({
  get: () => captchaService.showCaptcha.value,
  set: (value) => captchaService.showCaptcha.value = value
});

// 使用统一的验证码倒计时
const phone = computed(() => form.phone);
const countdownState = useCountdown(phone, 'user');
const countdownValue = computed(() => countdownState.countdown.value);
const isSending = computed(() => countdownState.isSending.value);

// 组件卸载时清理倒计时
onUnmounted(() => {
  if (countdownState.cleanup) {
    countdownState.cleanup();
  }
});

// 工具函数：显示提示
function showToast(title: string, icon: 'none' | 'success' = 'none') {
  uni.showToast({
    title,
    icon
  });
}

// 工具函数：显示加载
function showLoading(title: string) {
  uni.showLoading({
    title,
    mask: true
  });
}

// 工具函数：隐藏加载
function hideLoading() {
  uni.hideLoading();
}

// 监听输入变化，清除错误信息
function clearErrorOnInput() {
  if (errorMessage.value) {
    errorMessage.value = '';
  }
}

// 发送验证码
async function sendVerificationCode() {
  // 防止重复点击或倒计时中重复发送
  if (countdownValue.value > 0 || isSending.value) {
    return;
  }
  
  // 执行发送逻辑前再次验证手机号
  if (!isValidPhone(form.phone)) {
    showToast('请输入正确的手机号码');
    return;
  }
  
  // 使用滑块验证后发送验证码
  await captchaService.sendVerifyCodeWithCaptcha(form.phone, 'user', true);
}

// 表单验证
function validateForm() {
  // 清除之前的错误信息
  errorMessage.value = '';
  
  if (!isValidPhone(form.phone)) {
    showToast('请输入正确的手机号');
    return false;
  }
  
  if (!form.verifyCode) {
    showToast('请输入验证码');
    return false;
  }

  if (!form.newPassword) {
    showToast('请输入新密码');
    return false;
  }

  if (form.newPassword.length < 6 || form.newPassword.length > 20) {
    showToast('密码长度应为6-20位');
    return false;
  }

  if (form.newPassword !== form.confirmPassword) {
    showToast('两次输入的密码不一致');
    return false;
  }

  return true;
}

// 重置密码
async function handleResetPassword() {
  if (!validateForm()) return;

  loading.value = true;
  
  try {
    showLoading('提交中...');
    
    // 调用重置密码接口
    const resetParams = {
      phone: form.phone,
      verifyCode: form.verifyCode,
      newPassword: form.newPassword
    };
    
    console.log('重置密码提交数据:', JSON.stringify(resetParams));
    
    await userApi.resetPassword(resetParams);
    
    hideLoading();
    showToast('密码重置成功', 'success');
    
    // 重置成功后跳转到登录页
    setTimeout(() => {
      uni.redirectTo({
        url: './login'
      });
    }, 1500);
  } catch (error: any) {
    hideLoading();
    console.error('重置密码失败:', error);
    
    // 显示具体的错误信息
    let errorMsg = '重置密码失败，请重试';
    
    // 处理常见错误情况
    if (error.response && error.response.status === 404) {
      errorMsg = '重置密码服务暂不可用，请联系客服';
    } else if (error.message && error.message.includes('验证码')) {
      errorMsg = '验证码错误或已过期';
    } else if (error.message && error.message.includes('用户')) {
      errorMsg = '该手机号未注册';
    } else if (error.message) {
      errorMsg = error.message;
    }
    
    // 显示错误信息
    errorMessage.value = errorMsg;
  } finally {
    loading.value = false;
  }
}

// 返回登录页
function navigateToLogin() {
  uni.navigateBack();
}
</script>

<style>
.container {
  width: 100%;
  min-height: 100vh;
  background-color: #ffffff;
  position: relative;
  padding-bottom: 40rpx;
  box-sizing: border-box;
}

.reset-container {
  padding: 40rpx 32rpx;
  box-sizing: border-box;
  padding-top: calc(var(--status-bar-height) + 140rpx); /* 增加顶部间距，避免与返回按钮重叠 */
}

/* 标题文本样式 */
.header-text {
  margin-bottom: 48rpx;
  text-align: left;
}

.title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  display: block;
}

/* 表单样式 */
.form-container {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx 0;
}

.form-item {
  margin-bottom: 28rpx;
}

.label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
  font-weight: 500;
}

.input-wrapper {
  background-color: #f8f9fc;
  border-radius: 12rpx;
  overflow: hidden;
  border: 2rpx solid #eaedf5;
  transition: all 0.3s;
}

.input-wrapper:focus-within {
  border-color: #FF6B00;
  box-shadow: 0 0 0 2rpx rgba(255, 107, 0, 0.1);
}

.input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
}

/* 验证码输入框样式 */
.code-wrapper {
  display: flex;
  align-items: center;
}

.code-wrapper .input {
  flex: 1;
}

.send-code-btn {
  padding: 0 24rpx;
  height: 88rpx;
  line-height: 88rpx;
  color: #FF6B00;
  font-size: 26rpx;
  border-left: 1px solid #eaedf5;
  text-align: center;
  white-space: nowrap;
}

.send-code-btn.disabled {
  color: #999;
}

/* 按钮样式 */
.reset-btn {
  margin-top: 40rpx;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  background: linear-gradient(to right, #FF6B00, #FF9248);
  color: #ffffff;
  font-size: 32rpx;
  box-shadow: 0 6rpx 16rpx rgba(255, 107, 0, 0.2);
  border: none;
  text-align: center;
}

.reset-btn.btn-loading {
  opacity: 0.8;
}

.back-to-login {
  margin-top: 32rpx;
  text-align: center;
  font-size: 28rpx;
  color: #FF6B00;
}

.placeholder {
  color: #c0c0c0;
}

.error-message {
  margin: 16rpx 0 28rpx;
  padding: 16rpx;
  text-align: center;
  font-size: 28rpx;
  color: #ff4d4f;
  background-color: #fff2f0;
  border-radius: 8rpx;
  border: 1px solid #ffccc7;
}
</style> 