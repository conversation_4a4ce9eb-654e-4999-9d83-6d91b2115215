// 地址格式化工具函数

export interface AddressInfo {
  latitude?: number;
  longitude?: number;
  province?: string;
  city?: string;
  district?: string;
  street?: string;
  streetNumber?: string;
  detailAddress?: string;
  formattedAddress?: string;
  accuracy?: number;
}

/**
 * 格式化地址信息
 * @param addressInfo 地址信息对象
 * @param fallbackAddress 备用地址字符串（兼容性）
 * @returns 格式化后的地址字符串
 */
export function formatAddress(addressInfo?: AddressInfo, fallbackAddress?: string): string {
  // 如果没有地址信息，返回备用地址或默认值
  if (!addressInfo) {
    return fallbackAddress || '-';
  }

  // 如果有完整的格式化地址，优先使用
  if (addressInfo.formattedAddress) {
    const detailAddress = addressInfo.detailAddress ? ` ${addressInfo.detailAddress}` : '';
    return `${addressInfo.formattedAddress}${detailAddress}`;
  }

  // 否则拼接地址组件
  const parts = [
    addressInfo.province,
    addressInfo.city,
    addressInfo.district,
    addressInfo.street,
    addressInfo.detailAddress
  ].filter(Boolean);

  if (parts.length > 0) {
    return parts.join('');
  }

  // 如果结构化地址为空，使用备用地址
  return fallbackAddress || '-';
}

/**
 * 获取简短地址（省市区）
 * @param addressInfo 地址信息对象
 * @returns 简短地址字符串
 */
export function getShortAddress(addressInfo?: AddressInfo): string {
  if (!addressInfo) return '-';

  const parts = [
    addressInfo.province,
    addressInfo.city,
    addressInfo.district
  ].filter(Boolean);

  return parts.length > 0 ? parts.join('') : '-';
}

/**
 * 获取详细地址（街道+门牌号+详细地址）
 * @param addressInfo 地址信息对象
 * @returns 详细地址字符串
 */
export function getDetailAddress(addressInfo?: AddressInfo): string {
  if (!addressInfo) return '-';

  const parts = [
    addressInfo.street,
    addressInfo.streetNumber,
    addressInfo.detailAddress
  ].filter(Boolean);

  return parts.length > 0 ? parts.join('') : '-';
}
