# 维修管理系统后端

## 项目介绍

本项目是维修管理系统的后端部分，基于SpringBoot开发，提供用户、维修师傅、订单和管理员等相关功能的API接口。

## 技术栈

- **核心框架**：Spring Boot 2.7.9
- **ORM框架**：MyBatis-Plus 3.5.3
- **数据库**：MySQL 8.0
- **认证方式**：JWT (JSON Web Token)
- **API文档**：Swagger/OpenAPI
- **构建工具**：Maven

## 项目结构

```
backend/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/repair/
│   │   │       ├── config/         # 配置类
│   │   │       ├── controller/     # 控制器
│   │   │       ├── dto/            # 数据传输对象
│   │   │       ├── entity/         # 实体类
│   │   │       ├── exception/      # 异常处理
│   │   │       ├── mapper/         # MyBatis映射接口
│   │   │       ├── service/        # 服务接口及实现
│   │   │       ├── utils/          # 工具类
│   │   │       └── vo/             # 视图对象
│   │   └── resources/
│   │       ├── mapper/             # XML映射文件
│   │       ├── application.yml     # 应用配置
│   │       └── db/                 # 数据库脚本
│   └── test/                      # 测试代码
└── pom.xml                        # Maven配置
```

## 开发环境要求

- JDK 17+
- Maven 3.6+
- MySQL 8.0+

## 本地开发设置

### 1. 克隆仓库

```bash
git clone <仓库地址>
cd repair-system/backend
```

### 2. 配置数据库

创建MySQL数据库并导入初始脚本：

```bash
mysql -u username -p
CREATE DATABASE repair_system CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
```

执行`src/main/resources/db`目录下的SQL脚本。

### 3. 配置应用

修改`application.yml`中的数据库连接信息：

```yaml
spring:
  datasource:
    url: **************************************************************************************************************
    username: root
    password: yourpassword
```

### 4. 构建与运行

```bash
# 使用Maven构建项目
mvn clean package -DskipTests

# 运行Spring Boot应用
java -jar target/repair-system-0.0.1-SNAPSHOT.jar
```

应用将在 http://localhost:18080 上运行。

## API文档

启动应用后，访问 http://localhost:18080/swagger-ui/index.html 查看API文档。

## 主要功能模块

### 用户模块
- 用户注册
- 用户登录
- 获取/更新个人信息
- 密码修改

### 维修师傅模块
- 维修师傅注册/登录
- 查看未接/已接订单
- 接单/取消接单

### 订单模块
- 创建维修订单
- 订单列表查看
- 订单状态更新

### 管理员模块
- 管理员登录
- 用户/维修师傅管理
- 订单管理

## 安全说明

- 使用JWT进行身份验证
- 密码使用MD5加密存储
- 实现请求签名机制
- 数据脱敏处理
- 防SQL注入措施

## 线上部署

### 使用Docker部署（推荐）

1. 构建Docker镜像：

```bash
docker build -t repair-system-backend .
```

2. 为镜像添加标签（推送到DockerHub前）：

```bash
# 为镜像添加DockerHub标签，替换 your-dockerhub-username 为你的DockerHub用户名
docker tag repair-system-backend your-dockerhub-username/repair-system-backend:latest
```

3. 登录DockerHub并推送镜像：

```bash
# 登录DockerHub（需要输入用户名和密码）
docker login

# 推送镜像到DockerHub
docker push your-dockerhub-username/repair-system-backend:latest
```

4. 从DockerHub拉取并运行镜像：

```bash
# 拉取镜像（可选，docker run会自动拉取）
docker pull your-dockerhub-username/repair-system-backend:latest

# 运行容器
docker run -d -p 18080:18080 --name repair-backend your-dockerhub-username/repair-system-backend:latest
```

5. 本地运行（如果已经构建了本地镜像）：

```bash
docker run -d -p 18080:18080 --name repair-backend repair-system-backend
```

### 使用Docker Compose部署

1. 创建`.env`文件，配置必要的环境变量：

```
# 数据库配置
SPRING_DATASOURCE_URL=**********************************************************************************************************************
SPRING_DATASOURCE_USERNAME=repair_user
SPRING_DATASOURCE_PASSWORD=repair_password

# MySQL配置
MYSQL_ROOT_PASSWORD=root_password
MYSQL_DATABASE=repair_system
MYSQL_USER=repair_user
MYSQL_PASSWORD=repair_password

# Redis配置
SPRING_REDIS_HOST=redis
SPRING_REDIS_PORT=6379
SPRING_REDIS_DATABASE=0
SPRING_REDIS_PASSWORD=redis_password

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRATION=86400000

# 微信小程序配置
WECHAT_MINIAPP_APPID=your_miniapp_appid
WECHAT_MINIAPP_SECRET=your_miniapp_secret

# 应用配置
APP_MYSQL_CHECK_ON_STARTUP=true
APP_MYSQL_FAIL_ON_ERROR=false

# 阿里云SMS配置
APP_ALIYUN_SMS_ENABLED=false
ALIYUN_ACCESS_KEY_ID=your_aliyun_access_key_id
ALIYUN_ACCESS_KEY_SECRET=your_aliyun_access_key_secret
ALIYUN_SIGN_NAME=your_aliyun_sign_name
ALIYUN_TEMPLATE_CODE=SMS_319371136
```

2. 运行Docker Compose：

```bash
docker-compose up -d
```

### 传统部署

1. 在服务器上安装JDK 17
2. 构建项目并将JAR文件上传到服务器
3. 使用`nohup`或系统服务运行：

```bash
nohup java -jar repair-system-0.0.1-SNAPSHOT.jar --spring.profiles.active=prod > app.log 2>&1 &
```

## 常见问题

1. **JDK 17兼容性问题**  
   在JDK 17环境下运行时，可能需要添加JAXB API依赖，或者升级JJWT库至0.11.5以上版本。

2. **JWT依赖错误**  
   遇到`java.lang.NoClassDefFoundError: javax/xml/bind/DatatypeConverter`错误，需要更新JJWT依赖。

## 贡献指南

1. Fork项目
2. 创建特性分支：`git checkout -b feature/xxx`
3. 提交更改：`git commit -m 'Add some feature'`
4. 推送到分支：`git push origin feature/xxx`
5. 提交Pull Request

## 许可证

[MIT License](LICENSE) 