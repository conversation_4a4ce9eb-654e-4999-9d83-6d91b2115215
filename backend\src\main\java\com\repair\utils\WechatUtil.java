package com.repair.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.repair.config.WechatConfig;
import com.repair.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信小程序工具类
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class WechatUtil {

    private final WechatConfig wechatConfig;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    // 微信小程序登录接口
    private static final String WX_CODE_2_SESSION_URL = "https://api.weixin.qq.com/sns/jscode2session?appid={appid}&secret={secret}&js_code={code}&grant_type=authorization_code";

    /**
     * 通过code获取微信用户信息
     *
     * @param code 微信登录临时凭证
     * @return 微信用户信息
     */
    public Map<String, Object> getWxUserInfoByCode(String code) {
        log.info("开始获取微信用户信息，code: {}", code);

        try {
            // 请求参数
            Map<String, String> params = new HashMap<>();
            String appid = wechatConfig.getAppid();
            String secret = wechatConfig.getSecret();
            
            log.info("微信配置信息 - appid: {}, secret长度: {}", appid, secret != null ? secret.length() : 0);
            
            params.put("appid", appid);
            params.put("secret", secret);
            params.put("code", code);
            
            // 记录完整请求URL
            String requestUrl = WX_CODE_2_SESSION_URL.replace("{appid}", appid)
                .replace("{secret}", secret)
                .replace("{code}", code);
            log.info("完整请求URL(敏感信息已隐藏): {}", requestUrl.replace(secret, "******"));
            
            // 发送请求
            log.info("开始发送请求到微信服务器");
            String response = null;
            try {
                response = restTemplate.getForObject(WX_CODE_2_SESSION_URL, String.class, params);
                log.info("微信接口响应: {}", response);
            } catch (Exception e) {
                log.error("微信请求发送异常: {}", e.getMessage(), e);
                throw new BusinessException("微信请求发送失败: " + e.getMessage());
            }
            
            if (response == null || response.isEmpty()) {
                log.error("微信接口返回空响应");
                throw new BusinessException("微信接口返回空响应");
            }
            
            // 解析响应
            JsonNode jsonNode = null;
            try {
                jsonNode = objectMapper.readTree(response);
                log.info("解析微信响应为JSON成功");
            } catch (Exception e) {
                log.error("解析微信响应JSON失败: {}", e.getMessage(), e);
                throw new BusinessException("解析微信响应数据失败: " + e.getMessage());
            }
            
            if (jsonNode.has("errcode") && jsonNode.get("errcode").asInt() != 0) {
                int errcode = jsonNode.get("errcode").asInt();
                String errmsg = jsonNode.get("errmsg").asText();
                log.error("微信接口返回错误代码: {}, 错误信息: {}", errcode, errmsg);
                
                // 40029通常是无效的code
                if (errcode == 40029) {
                    throw new BusinessException("微信登录凭证无效，请重新获取");
                }
                // 40013通常是appid无效
                else if (errcode == 40013) {
                    throw new BusinessException("微信AppID无效");
                }
                // 40125通常是secret错误
                else if (errcode == 40125) {
                    throw new BusinessException("微信AppSecret无效");
                } 
                // 其他错误
                else {
                    throw new BusinessException("微信登录失败: " + errmsg);
                }
            }
            
            if (!jsonNode.has("openid")) {
                log.error("微信响应中没有openid字段: {}", response);
                throw new BusinessException("微信响应异常，未返回openid");
            }
            
            // 获取会话密钥、用户唯一标识
            String openid = jsonNode.get("openid").asText();
            String sessionKey = jsonNode.has("session_key") ? jsonNode.get("session_key").asText() : "";
            String unionid = jsonNode.has("unionid") ? jsonNode.get("unionid").asText() : "";
            
            log.info("成功获取微信用户标识 - openid: {}, session_key长度: {}, 是否有unionid: {}", 
                    openid, sessionKey.length(), !unionid.isEmpty());
            
            // 封装结果
            Map<String, Object> result = new HashMap<>();
            result.put("openid", openid);
            result.put("sessionKey", sessionKey);
            if (!unionid.isEmpty()) {
                result.put("unionid", unionid);
            }
            
            log.info("获取微信用户信息成功, openid: {}", openid);
            return result;
        } catch (BusinessException e) {
            log.error("业务异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("获取微信用户信息失败", e);
            throw new BusinessException("获取微信用户信息失败: " + e.getMessage());
        }
    }
} 