<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useAdminStore } from '@/store/admin';
import { adminApi } from '@/api/admin';
import CommonBackButton from '@/components/common/BackButton.vue';

// 创建日期格式化函数
function formatDate(date: string | Date | null | undefined): string {
  if (!date) return '-';
  
  const d = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(d.getTime())) return '-';
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}`;
}

// 订单ID
const orderId = ref<string | number>('');
// 订单详情
const orderDetail = ref<any>(null);
// 加载状态
const loading = ref(false);

// 定义状态对应的样式类型
interface StatusStyle {
  text: string;
  color: string;
}

// 使用数字索引签名
const statusStyles: Record<number, StatusStyle> = {
  0: { text: '待处理', color: '#909399' },
  1: { text: '已接单', color: '#E6A23C' },
  2: { text: '处理中', color: '#409EFF' },
  3: { text: '已完成', color: '#67C23A' },
  4: { text: '已取消', color: '#F56C6C' }
};

// 获取状态文本
function getStatusText(status: number): string {
  return statusStyles[status]?.text || '未知状态';
}

// 获取状态颜色
function getStatusColor(status: number): string {
  return statusStyles[status]?.color || '#909399';
}

// 获取订单详情
function getOrderDetail() {
  loading.value = true;
  
  adminApi.getOrderDetail(orderId.value)
    .then(res => {
      if (res.code === 200) {
        orderDetail.value = res.data;
      } else {
        uni.showToast({
          title: res.message || '获取订单详情失败',
          icon: 'none'
        });
      }
    })
    .catch(err => {
      console.error('获取订单详情失败', err);
      uni.showToast({
        title: '获取订单详情失败',
        icon: 'none'
      });
    })
    .finally(() => {
      loading.value = false;
    });
}

// 确认更新状态
function confirmUpdateStatus(status: number, message: string) {
  uni.showModal({
    title: '确认操作',
    content: message,
    success: (res) => {
      if (res.confirm) {
        updateOrderStatus(status);
      }
    }
  });
}

// 更新订单状态
function updateOrderStatus(status: number) {
  uni.showLoading({ title: '处理中...' });
  
  adminApi.updateOrderStatus(orderId.value, status)
    .then(res => {
      if (res.code === 200) {
        uni.showToast({
          title: '更新成功',
          icon: 'success'
        });
        // 发送事件通知订单列表页面刷新数据
        uni.$emit('orderUpdated');
        // 重新获取订单详情
        getOrderDetail();
      } else {
        uni.showToast({
          title: res.message || '更新失败',
          icon: 'none'
        });
      }
    })
    .catch(err => {
      console.error('更新订单状态失败', err);
      uni.showToast({
        title: '更新失败',
        icon: 'none'
      });
    })
    .finally(() => {
      uni.hideLoading();
    });
}

// 分配订单
function assignOrder() {
  uni.navigateTo({
    url: `/pages/admin/assign-order?id=${orderId.value}`
  });
}

// 取消订单
function cancelOrder() {
  confirmUpdateStatus(4, '确认取消该订单吗？');
}

// 完成订单
function completeOrder() {
  confirmUpdateStatus(3, '确认将订单标记为已完成吗？');
}

// 判断是否是管理员
const adminStore = useAdminStore();
function checkAdmin() {
  if (!adminStore.isLoggedIn) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    });
    
    setTimeout(() => {
      uni.redirectTo({
        url: '/pages/admin/login'
      });
    }, 1500);
    
    return false;
  }
  return true;
}

onMounted(() => {
  if (checkAdmin()) {
    // 获取订单ID
    const pages = getCurrentPages();
    const page = pages[pages.length - 1];
    // @ts-ignore
    const query = page?.options || {};
    
    if (query.id) {
      orderId.value = query.id;
      getOrderDetail();
    } else {
      uni.showToast({
        title: '订单ID不存在',
        icon: 'none'
      });
      
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  }
});
</script>

<template>
  <view class="order-detail container">
    <!-- 使用统一的返回按钮组件 -->
    <common-back-button></common-back-button>
    
    <!-- 顶部导航 -->
    <view class="page-header">
      <text class="page-title">订单详情</text>
    </view>
    
    <!-- 加载中 -->
    <view v-if="loading" class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 订单详情 -->
    <block v-else-if="orderDetail">
      <!-- 订单状态卡片 -->
      <view class="status-card">
        <view class="order-id-container">
          <view class="order-id-section">
            <view class="order-label">订单号:</view>
            <view class="order-value">{{ orderDetail.orderId }}</view>
          </view>
          <text 
            class="order-status" 
            :style="{ backgroundColor: getStatusColor(orderDetail.status) }"
          >
            {{ getStatusText(orderDetail.status) }}
          </text>
        </view>
        <view class="status-row" v-if="orderDetail.urgent">
          <view class="urgent-tag">紧急订单</view>
        </view>
      </view>
      
      <!-- 基本信息 -->
      <view class="section-card">
        <view class="section-title">基本信息</view>
        <view class="info-row">
          <text class="info-label">联系人:</text>
          <text class="info-value">{{ orderDetail.contactName }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">联系电话:</text>
          <text class="info-value">{{ orderDetail.contactPhone }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">维修地址:</text>
          <text class="info-value">{{ orderDetail.address }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">维修内容:</text>
          <text class="info-value">{{ orderDetail.description }}</text>
        </view>
      </view>
      
      <!-- 维修师信息 -->
      <view class="section-card" v-if="orderDetail.repairerId && orderDetail.repairerName">
        <view class="section-title">维修师信息</view>
        <view class="info-row">
          <text class="info-label">维修师:</text>
          <text class="info-value">{{ orderDetail.repairerName }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">联系电话:</text>
          <text class="info-value">{{ orderDetail.repairerPhone || '暂无' }}</text>
        </view>
      </view>
      
      <!-- 用户信息 -->
      <view class="section-card" v-if="orderDetail.userId && orderDetail.username">
        <view class="section-title">用户信息</view>
        <view class="info-row">
          <text class="info-label">用户名:</text>
          <text class="info-value">{{ orderDetail.username }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">联系电话:</text>
          <text class="info-value">{{ orderDetail.userPhone || '暂无' }}</text>
        </view>
      </view>
      
      <!-- 时间信息 -->
      <view class="section-card">
        <view class="section-title">时间信息</view>
        <view class="info-row">
          <text class="info-label">创建时间:</text>
          <text class="info-value">{{ formatDate(orderDetail.createTime) }}</text>
        </view>
        <view class="info-row" v-if="orderDetail.appointmentTime">
          <text class="info-label">预约时间:</text>
          <text class="info-value">{{ formatDate(orderDetail.appointmentTime) }}</text>
        </view>
        <view class="info-row" v-if="orderDetail.completeTime">
          <text class="info-label">完成时间:</text>
          <text class="info-value">{{ formatDate(orderDetail.completeTime) }}</text>
        </view>
      </view>
      
      <!-- 评价信息 -->
      <view class="section-card" v-if="orderDetail.status === 3">
        <view class="section-title">评价信息</view>
        <view class="info-row">
          <text class="info-label">评分:</text>
          <view class="rating">
            <text v-for="i in 5" :key="i" class="rating-star" :class="{ active: i <= orderDetail.rating }">★</text>
          </view>
        </view>
        <view class="info-row" v-if="orderDetail.comment">
          <text class="info-label">评价内容:</text>
          <text class="info-value">{{ orderDetail.comment }}</text>
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="action-buttons">
        <!-- 待处理状态可分配和取消 -->
        <template v-if="orderDetail.status === 0">
          <button class="action-btn assign-btn" @click="assignOrder">分配订单</button>
          <button class="action-btn cancel-btn" @click="cancelOrder">取消订单</button>
        </template>
        
        <!-- 已接单状态可标记完成和取消 -->
        <template v-else-if="orderDetail.status === 1">
          <button class="action-btn complete-btn" @click="completeOrder">标记完成</button>
          <button class="action-btn cancel-btn" @click="cancelOrder">取消订单</button>
        </template>
      </view>
    </block>
    
    <!-- 无数据 -->
    <view v-else class="empty-container">
      <text class="empty-text">订单不存在或已被删除</text>
    </view>
  </view>
</template>

<style>
.order-detail {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 40rpx;
}

.page-header {
  background-color: #ffffff;
  padding: calc(var(--status-bar-height) + 20rpx) 30rpx 20rpx;
  position: relative;
  border-bottom: 1rpx solid #ebeef5;
  margin-bottom: 20rpx;
}

.page-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  text-align: center;
  margin-top: 60rpx;
}

.status-card {
  background-color: #ffffff;
  margin: 24rpx;
  padding: 24rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  margin-top: 40rpx;
}

.order-id-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0 10rpx;
}

.order-id-section {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.order-label {
  font-size: 28rpx;
  color: #666666;
}

.order-value {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.order-status {
  padding: 6rpx 20rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 500;
}

.status-row {
  margin-top: 16rpx;
  display: flex;
  align-items: center;
}

.urgent-tag {
  background: linear-gradient(45deg, #ff4d4d, #f56c6c);
  color: #ffffff;
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.section-card {
  background-color: #ffffff;
  margin: 0 24rpx 24rpx;
  padding: 24rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 16rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4rpx;
  height: 24rpx;
  background-color: #409eff;
  border-radius: 2rpx;
}

.info-row {
  display: flex;
  margin-bottom: 16rpx;
  line-height: 1.6;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 140rpx;
  color: #666666;
  font-size: 28rpx;
  flex-shrink: 0;
}

.info-value {
  flex: 1;
  color: #333333;
  font-size: 28rpx;
  word-break: break-all;
}

.rating {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.rating-star {
  color: #dcdfe6;
  font-size: 32rpx;
}

.rating-star.active {
  color: #faad14;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 24rpx;
  margin: 40rpx 24rpx;
  padding: 0 30rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #ffffff;
  max-width: 240rpx;
}

.assign-btn {
  background: linear-gradient(45deg, #409eff, #1890ff);
  box-shadow: 0 4rpx 12rpx rgba(64, 158, 255, 0.2);
}

.complete-btn {
  background: linear-gradient(45deg, #67c23a, #4caf50);
  box-shadow: 0 4rpx 12rpx rgba(103, 194, 58, 0.2);
}

.cancel-btn {
  background: linear-gradient(45deg, #f56c6c, #e53935);
  box-shadow: 0 4rpx 12rpx rgba(245, 108, 108, 0.2);
}

.loading-container, .empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  background-color: #ffffff;
  margin: 24rpx;
  border-radius: 12rpx;
}

.loading-text, .empty-text {
  color: #909399;
  font-size: 28rpx;
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
  .order-detail {
    background-color: #1a1a1a;
  }
  
  .page-header,
  .status-card,
  .section-card,
  .loading-container,
  .empty-container {
    background-color: #2a2a2a;
  }
  
  .page-title,
  .order-value,
  .info-value {
    color: #e0e0e0;
  }
  
  .order-label,
  .info-label {
    color: #909399;
  }
  
  .section-title {
    color: #e0e0e0;
  }
  
  .rating-star {
    color: #4c4c4c;
  }
  
  .rating-star.active {
    color: #faad14;
  }
}

/* 移动端适配 */
@media screen and (max-width: 375px) {
  .status-card,
  .section-card {
    margin: 20rpx;
    padding: 20rpx;
  }
  
  .action-buttons {
    margin: 30rpx 20rpx;
    flex-direction: column;
    gap: 20rpx;
  }
  
  .action-btn {
    max-width: none;
  }
}
</style> 