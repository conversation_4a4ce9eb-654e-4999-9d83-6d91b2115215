package com.repair.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 验证码配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "app.verify-code")
public class VerifyCodeProperties {

    /**
     * 验证码长度
     */
    private int length = 6;

    /**
     * 验证码有效期（秒）
     */
    private long expireTime = 300;
    
    /**
     * 验证码清理间隔（秒）
     */
    private long cleanupInterval = 60;
    
    /**
     * 验证码请求最小间隔（秒）
     * 防止频繁请求验证码
     */
    private long requestInterval = 60;
} 