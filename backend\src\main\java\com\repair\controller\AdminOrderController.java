package com.repair.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.repair.entity.RepairOrder;
import com.repair.service.AdminOrderService;
import com.repair.utils.JwtUtil;
import com.repair.common.Result;
import com.repair.vo.OrderVO;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;

/**
 * 管理员订单控制器
 */
@RestController
@RequestMapping("/api/admin/order")
public class AdminOrderController {

    private final AdminOrderService adminOrderService;
    private final JwtUtil jwtUtil;

    public AdminOrderController(AdminOrderService adminOrderService, JwtUtil jwtUtil) {
        this.adminOrderService = adminOrderService;
        this.jwtUtil = jwtUtil;
    }

    /**
     * 获取所有订单列表
     */
    @GetMapping("/list")
    public Result<Page<OrderVO>> getOrderList(
            @RequestParam(value = "status", required = false, defaultValue = "-1") Integer status,
            @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            HttpServletRequest request) {
        // 验证是否是管理员
        String role = jwtUtil.getRoleFromRequest(request);
        if (!"admin".equals(role)) {
            return Result.fail("无权限访问管理员接口");
        }
        
        Page<RepairOrder> pageParam = new Page<>(page, size);
        Page<OrderVO> orders = adminOrderService.getOrderList(status, pageParam);
        return Result.success(orders);
    }

    /**
     * 获取订单详情
     */
    @GetMapping("/detail/{orderId}")
    public Result<OrderVO> getOrderDetail(@PathVariable("orderId") Long orderId) {
        OrderVO order = adminOrderService.getOrderDetail(orderId);
        return Result.success(order);
    }

    /**
     * 分配订单
     */
    @PostMapping("/assign/{orderId}")
    public Result<OrderVO> assignOrder(
            @PathVariable("orderId") Long orderId,
            @RequestParam("repairerId") @NotNull Long repairerId) {
        OrderVO order = adminOrderService.assignOrder(orderId, repairerId);
        return Result.success(order);
    }

    /**
     * 取消订单
     */
    @PostMapping("/cancel/{orderId}")
    public Result<OrderVO> cancelOrder(
            @PathVariable("orderId") Long orderId,
            @RequestParam("reason") String reason) {
        OrderVO order = adminOrderService.cancelOrder(orderId, reason);
        return Result.success(order);
    }
} 