<template>
  <view class="container">
    <!-- 使用统一的返回按钮组件 -->
    <common-back-button></common-back-button>
    
    <!-- 页面标题 -->
    <view class="header">
      <text class="title">我的订单</text>
    </view>

    <!-- 未登录状态提示 -->
    <view v-if="!isLoggedIn" class="login-required">
      <text class="login-emoji">🔒</text>
      <text class="login-tip">您需要登录后才能查看订单信息</text>
      <button class="login-btn" @click="goToLogin">立即登录</button>
    </view>

    <!-- 已登录状态内容 -->
    <template v-else>
      <!-- 订单状态筛选 -->
      <view class="filter-tabs">
        <view 
          v-for="(tab, index) in tabs" 
          :key="index" 
          class="tab-item"
          :class="{ active: currentTab === index }"
          @click="changeTab(index)"
        >
          <text>{{ tab.name }}</text>
        </view>
      </view>

      <!-- 订单列表 -->
      <scroll-view 
        class="order-list" 
        scroll-y 
        @scrolltolower="loadMore"
        :style="{ height: scrollHeight + 'px' }"
        refresher-enabled
        :refresher-triggered="refreshing"
        @refresherrefresh="onRefresh"
      >
        <!-- 无订单提示 -->
        <view class="empty-tip" v-if="orderList.length === 0 && !loading">
          <text class="empty-emoji">📋</text>
          <text class="empty-text">{{ loading ? '加载中...' : '暂无订单' }}</text>
        </view>

        <!-- 订单列表 -->
        <view class="order-item" v-for="order in orderList" :key="order.id" @click="goToOrderDetail(order.id)">
          <view class="order-top">
            <text class="order-id">订单号: {{ order.orderId || order.id }}</text>
            <text class="order-status" :class="getStatusClass(order.status)">{{ getStatusText(order.status) }}</text>
          </view>

          <view class="order-content">
            <view class="order-info">
              <text class="info-label">维修内容: </text>
              <text class="info-value">{{ order.description }}</text>
            </view>
            <view class="order-info">
              <text class="info-label">联系人: </text>
              <text class="info-value">{{ order.contactName }}</text>
            </view>
            <view class="order-info">
              <text class="info-label">地址: </text>
              <text class="info-value">{{ formatAddress(order.addressInfo) }}</text>
            </view>
            <view class="order-info">
              <text class="info-label">预约时间: </text>
              <text class="info-value">{{ formatDateTime(order.appointmentTime) }}</text>
            </view>
          </view>

          <view class="order-bottom">
            <text class="order-time">{{ formatDateTime(order.createTime) }}</text>
            <!-- 订单操作按钮，根据状态显示不同按钮 -->
            <view class="order-actions">
              <button 
                class="action-btn cancel-btn" 
                v-if="order.status === 0 || order.status === 2" 
                @click.stop="cancelOrder(order.id)"
              >取消订单</button>
              <button 
                class="action-btn detail-btn" 
                @click.stop="goToOrderDetail(order.id)"
              >查看详情</button>
            </view>
          </view>
        </view>

        <!-- 加载更多提示 -->
        <view class="loading-more" v-if="loading && orderList.length > 0">
          <text>加载中...</text>
        </view>

        <!-- 到底提示 -->
        <view class="list-bottom" v-if="!hasMore && orderList.length > 0">
          <text>-- 已经到底啦 --</text>
        </view>
      </scroll-view>
    </template>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { orderApi, type OrderInfo, OrderStatus } from '@/api/order';
import CommonBackButton from '@/components/common/BackButton.vue';
import { useUserStore } from '@/store/user'; // 添加引入用户状态

// 获取用户状态
const userStore = useUserStore();
const isLoggedIn = computed(() => userStore.isLoggedIn);

// 页面状态
const loading = ref(false);
const refreshing = ref(false);
const orderList = ref<OrderInfo[]>([]);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const hasMore = computed(() => orderList.value.length < total.value);
const scrollHeight = ref(0);

// 订单状态标签
const tabs = [
  { name: '全部', status: -1 },
  { name: '待处理', status: OrderStatus.PENDING },
  { name: '已接单', status: OrderStatus.ACCEPTED },
  { name: '处理中', status: OrderStatus.PROCESSING },
  { name: '已完成', status: OrderStatus.COMPLETED },
  { name: '已取消', status: OrderStatus.CANCELED }
];
const currentTab = ref(0);

// 获取窗口高度
onMounted(() => {
  const info = uni.getSystemInfoSync();
  // 减去头部和筛选栏的高度
  scrollHeight.value = info.windowHeight - 90;
  
  // 只有在登录状态下才加载订单
  if (isLoggedIn.value) {
    loadOrders();
  }
});

// 切换标签
function changeTab(index: number) {
  if (currentTab.value === index) return;
  currentTab.value = index;
  resetList();
  loadOrders();
}

// 重置列表
function resetList() {
  orderList.value = [];
  currentPage.value = 1;
  total.value = 0;
}

// 下拉刷新
function onRefresh() {
  refreshing.value = true;
  resetList();
  loadOrders().finally(() => {
    refreshing.value = false;
  });
}

// 加载更多
function loadMore() {
  if (loading.value || !hasMore.value) return;
  currentPage.value++;
  loadOrders();
}

// 加载订单数据
async function loadOrders() {
  loading.value = true;
  try {
    const tabStatus = tabs[currentTab.value].status;
    // 基础参数
    const params: { page: number; pageSize: number; status?: OrderStatus } = {
      page: currentPage.value,
      pageSize: pageSize.value
    };
    
    // 只有当状态不为-1（全部）时才添加status参数
    if (tabStatus !== -1) {
      params.status = tabStatus as OrderStatus;
    }

    const { data } = await orderApi.getOrderList(params);
    
    // 如果是第一页，替换列表，否则追加
    if (currentPage.value === 1) {
      orderList.value = data.records;
    } else {
      orderList.value = [...orderList.value, ...data.records];
    }
    
    total.value = data.total;
  } catch (error) {
    console.error('获取订单列表失败:', error);
    uni.showToast({
      title: '获取订单列表失败',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
}

// 取消订单
async function cancelOrder(orderId: number) {
  uni.showModal({
    title: '提示',
    content: '确定要取消该订单吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          await orderApi.cancelOrder(orderId);
          uni.showToast({
            title: '订单已取消',
            icon: 'success'
          });
          // 延迟1.5秒后刷新列表
          setTimeout(() => {
            resetList();
            loadOrders();
          }, 1500);
        } catch (error) {
          console.error('取消订单失败:', error);
          uni.showToast({
            title: '取消订单失败',
            icon: 'none'
          });
        }
      }
    }
  });
}

// 前往订单详情页
function goToOrderDetail(orderId: number) {
  console.log('跳转到订单详情，orderId参数:', orderId);
  console.log('订单完整数据:', orderList.value.find(item => item.id === orderId));
  
  if (!orderId) {
    uni.showToast({
      title: '订单ID不存在',
      icon: 'none'
    });
    return;
  }
  
  // 跳转到订单详情页面，传递ID参数
  const url = `/pages/user/order-detail?id=${orderId}`;
  console.log('跳转URL:', url);
  
  uni.navigateTo({
    url,
    success: () => {
      console.log('跳转成功');
    },
    fail: (err) => {
      console.error('跳转失败:', err);
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none'
      });
    }
  });
}

// 格式化地址信息
function formatAddress(addressInfo?: any): string {
  if (!addressInfo) return '-';

  // 如果有完整的格式化地址，优先使用
  if (addressInfo.formattedAddress) {
    const detailAddress = addressInfo.detailAddress ? ` ${addressInfo.detailAddress}` : '';
    return `${addressInfo.formattedAddress}${detailAddress}`;
  }

  // 否则拼接地址组件
  const parts = [
    addressInfo.province,
    addressInfo.city,
    addressInfo.district,
    addressInfo.street,
    addressInfo.detailAddress
  ].filter(Boolean);

  return parts.length > 0 ? parts.join('') : '-';
}

// 格式化日期时间
function formatDateTime(dateTime?: string): string {
  if (!dateTime) return '-';
  const date = new Date(dateTime);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
}

// 获取状态文本
function getStatusText(status?: number): string {
  switch (status) {
    case OrderStatus.PENDING:
      return '待处理';
    case OrderStatus.ACCEPTED:
      return '已接单';
    case OrderStatus.PROCESSING:
      return '处理中';
    case OrderStatus.COMPLETED:
      return '已完成';
    case OrderStatus.CANCELED:
      return '已取消';
    default:
      return '未知状态';
  }
}

// 获取状态样式类
function getStatusClass(status?: number): string {
  switch (status) {
    case OrderStatus.PENDING:
      return 'status-pending';
    case OrderStatus.ACCEPTED:
      return 'status-accepted';
    case OrderStatus.PROCESSING:
      return 'status-processing';
    case OrderStatus.COMPLETED:
      return 'status-completed';
    case OrderStatus.CANCELED:
      return 'status-canceled';
    default:
      return '';
  }
}

// 登录页面跳转
function goToLogin() {
  // 保存当前页面路径，登录后返回
  uni.setStorageSync('redirect_after_login', '/pages/user/order-list');
  
  uni.navigateTo({
    url: '/pages/user/login',
    fail: (err) => {
      console.error('跳转登录页面失败', err);
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none'
      });
    }
  });
}
</script>

<style>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  position: relative;
  height: 96rpx;
  margin-top: 160rpx; /* 为返回按钮留出空间 */
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #f0f0f0;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

.filter-tabs {
  display: flex;
  background-color: #ffffff;
  height: 88rpx;
  border-bottom: 1px solid #f0f0f0;
}

.tab-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #07c160;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #07c160;
}

.order-list {
  padding: 20rpx;
  box-sizing: border-box;
}

.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-emoji {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  color: #c8c8c8;
  line-height: 1;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
  margin-top: 10rpx;
}

.order-item {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.order-top {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 16rpx;
}

.order-id {
  font-size: 26rpx;
  color: #666;
}

.order-status {
  font-size: 26rpx;
  font-weight: 500;
}

.status-pending {
  color: #ff9800;
}

.status-accepted {
  color: #2196f3;
}

.status-processing {
  color: #673ab7;
}

.status-completed {
  color: #4caf50;
}

.status-canceled {
  color: #9e9e9e;
}

.order-content {
  margin-bottom: 16rpx;
}

.order-info {
  display: flex;
  margin-bottom: 12rpx;
  align-items: flex-start;
}

.info-label {
  width: 140rpx;
  font-size: 26rpx;
  color: #666;
  flex-shrink: 0;
}

.info-value {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  word-break: break-all;
  line-height: 1.4;
}

.order-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #f5f5f5;
  padding-top: 16rpx;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

.order-actions {
  display: flex;
}

.action-btn {
  font-size: 24rpx;
  padding: 8rpx 20rpx;
  border-radius: 24rpx;
  margin-left: 16rpx;
  background-color: #fff;
  line-height: 1.5;
}

.cancel-btn {
  color: #ff4d4f;
  border: 1px solid #ff4d4f;
}

.detail-btn {
  color: #07c160;
  border: 1px solid #07c160;
}

.loading-more, .list-bottom {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  padding: 20rpx 0;
}

/* 未登录状态样式 */
.login-required {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.login-emoji {
  font-size: 80rpx;
  margin-bottom: 30rpx;
  color: #4a90e2;
  line-height: 1;
}

.login-tip {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.login-btn {
  background: linear-gradient(135deg, #4a90e2 0%, #1890ff 100%);
  color: white;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 32rpx;
  border-radius: 40rpx;
  padding: 0 60rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
}

.login-btn::after {
  border: none;
}
</style> 