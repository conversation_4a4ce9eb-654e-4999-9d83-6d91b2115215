<template>
  <view v-if="visible" class="slider-captcha-container">
    <view class="captcha-overlay" @click="cancel"></view>
    <view class="captcha-content" @click.stop>
      <!-- 标题 -->
      <view class="captcha-header">
        <text class="captcha-title">{{ title }}</text>
        <text class="captcha-close" @click="cancel">×</text>
      </view>
      
      <!-- 验证区域 -->
      <view class="captcha-body">
        <!-- 简化的滑块验证区域 -->
        <view class="captcha-simple-verify">
          <view class="verify-tip">
            <text>请将滑块拖动到右侧箭头处完成验证</text>
          </view>
          
          <!-- 滑块区域 -->
          <view class="slider-container">
            <view class="slider-track">
              <!-- 滑动进度条 -->
              <view 
                class="slider-process" 
                :style="{ width: `${sliderLeft}px` }"
              ></view>
              
              <!-- 目标区域指示 -->
              <view class="slider-target">
                <text class="target-icon">✓</text>
              </view>
            </view>
            
            <!-- 滑块 -->
            <view 
              class="slider-button" 
              :class="{ 'button-success': verifySuccess }"
              :style="{ left: `${sliderLeft}px` }"
              @touchstart="onTouchStart"
              @touchmove="onTouchMove"
              @touchend="onTouchEnd"
              @mousedown="onMouseDown"
            >
              <text class="slider-icon">》</text>
            </view>
          </view>
          
          <!-- 验证结果提示 -->
          <view v-if="showResult" class="verify-result" :class="{ 'result-success': verifySuccess }">
            <text>{{ verifySuccess ? successText : failText }}</text>
          </view>
        </view>
      </view>
      
      <!-- 底部操作区 -->
      <view class="captcha-footer">
        <view class="captcha-refresh" @click="reset">
          <text>刷新验证</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, defineComponent } from 'vue';

// 确保组件有一个明确的名称
defineComponent({
  name: 'SliderCaptcha'
});

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '安全验证'
  },
  successText: {
    type: String,
    default: '验证成功'
  },
  failText: {
    type: String,
    default: '验证失败，请重试'
  }
});

// 定义组件事件
const emit = defineEmits(['success', 'fail', 'cancel', 'update:visible']);

// 内部状态
const verifySuccess = ref(false);
const showResult = ref(false);
const sliderLeft = ref(0);
const trackWidth = ref(280); // 滑动轨道宽度
const buttonWidth = ref(40); // 滑块宽度
const isDragging = ref(false);
const startX = ref(0);
const targetReached = ref(false);

// 滑块提示文本
const sliderTip = computed(() => {
  if (verifySuccess.value) return '验证通过';
  if (isDragging.value) return '松开验证';
  return '向右滑动验证';
});

// 组件挂载时初始化
onMounted(() => {
  reset();
});

// 处理触摸开始事件
function onTouchStart(e: TouchEvent) {
  if (verifySuccess.value) return;
  
  isDragging.value = true;
  startX.value = e.touches[0].clientX;
}

// 处理触摸移动事件
function onTouchMove(e: TouchEvent) {
  if (!isDragging.value) return;
  
  const currentX = e.touches[0].clientX;
  const moveX = currentX - startX.value;
  
  // 计算新的滑块位置
  let newLeft = sliderLeft.value + moveX;
  
  // 限制滑块在有效范围内
  newLeft = Math.max(0, Math.min(newLeft, trackWidth.value - buttonWidth.value));
  
  sliderLeft.value = newLeft;
  startX.value = currentX;
  
  // 检查是否已经滑到最右侧
  checkTargetReached();
  
  // 阻止页面滚动
  e.preventDefault();
}

// 处理触摸结束事件
function onTouchEnd() {
  if (!isDragging.value) return;
  
  isDragging.value = false;
  verifySlider();
}

// 处理鼠标按下事件
function onMouseDown(e: MouseEvent) {
  if (verifySuccess.value) return;
  
  isDragging.value = true;
  startX.value = e.clientX;
  
  // 添加鼠标移动和抬起事件监听
  document.addEventListener('mousemove', onMouseMove);
  document.addEventListener('mouseup', onMouseUp);
}

// 处理鼠标移动事件
function onMouseMove(e: MouseEvent) {
  if (!isDragging.value) return;
  
  const currentX = e.clientX;
  const moveX = currentX - startX.value;
  
  // 计算新的滑块位置
  let newLeft = sliderLeft.value + moveX;
  
  // 限制滑块在有效范围内
  newLeft = Math.max(0, Math.min(newLeft, trackWidth.value - buttonWidth.value));
  
  sliderLeft.value = newLeft;
  startX.value = currentX;
  
  // 检查是否已经滑到最右侧
  checkTargetReached();
  
  // 阻止默认行为
  e.preventDefault();
}

// 处理鼠标抬起事件
function onMouseUp() {
  if (!isDragging.value) return;
  
  isDragging.value = false;
  verifySlider();
  
  // 移除事件监听
  document.removeEventListener('mousemove', onMouseMove);
  document.removeEventListener('mouseup', onMouseUp);
}

// 检查是否滑到目标位置
function checkTargetReached() {
  // 如果滑块已经接近最右侧，则认为已达到目标
  const threshold = trackWidth.value - buttonWidth.value - 5; // 允许5像素的误差
  targetReached.value = sliderLeft.value >= threshold;
}

// 验证滑块位置
function verifySlider() {
  showResult.value = true;
  verifySuccess.value = targetReached.value;
  
  // 发送验证结果事件
  if (targetReached.value) {
    emit('success');
    
    // 1.5秒后自动关闭
    setTimeout(() => {
      hide();
    }, 1500);
  } else {
    emit('fail');
    
    // 失败后重置
    setTimeout(() => {
      reset();
    }, 1000);
  }
}

// 重置验证码
function reset() {
  showResult.value = false;
  verifySuccess.value = false;
  sliderLeft.value = 0;
  targetReached.value = false;
}

// 取消验证
function cancel() {
  emit('cancel');
  hide();
}

// 隐藏验证码
function hide() {
  emit('update:visible', false);
}

// 提供给父组件的方法
defineExpose({
  reset,
  hide
});
</script>

<style>
.slider-captcha-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.captcha-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
}

.captcha-content {
  position: relative;
  width: 300px;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.captcha-header {
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
}

.captcha-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.captcha-close {
  font-size: 20px;
  color: #999;
  cursor: pointer;
}

.captcha-body {
  padding: 16px;
}

.captcha-simple-verify {
  width: 100%;
  padding: 20px 0;
}

.verify-tip {
  text-align: center;
  margin-bottom: 20px;
  font-size: 14px;
  color: #666;
}

.slider-container {
  position: relative;
  height: 40px;
  margin-bottom: 20px;
}

.slider-track {
  position: relative;
  height: 100%;
  background-color: #f7f7f7;
  border-radius: 20px;
}

.slider-process {
  position: absolute;
  height: 100%;
  background-color: rgba(82, 196, 26, 0.2);
  border-radius: 20px 0 0 20px;
}

/* 目标区域样式 */
.slider-target {
  position: absolute;
  top: 0;
  right: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0 20px 20px 0;
  background-color: rgba(82, 196, 26, 0.2);
  border: 1px dashed #52c41a;
  box-sizing: border-box;
}

.target-icon {
  font-size: 16px;
  color: #52c41a;
}

.slider-button {
  position: absolute;
  top: 0;
  left: 0;
  width: 40px;
  height: 40px;
  background-color: #FF6B00;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  cursor: grab;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: background-color 0.3s;
  z-index: 10;
}

.button-success {
  background-color: #52c41a;
}

.slider-icon {
  font-size: 16px;
}

.verify-result {
  text-align: center;
  font-size: 14px;
  font-weight: bold;
  color: #ff4d4f;
  margin-top: 10px;
}

.result-success {
  color: #52c41a;
}

.captcha-footer {
  padding: 12px 16px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #eee;
}

.captcha-refresh {
  font-size: 14px;
  color: #FF6B00;
  cursor: pointer;
}
</style> 