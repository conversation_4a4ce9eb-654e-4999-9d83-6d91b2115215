package com.repair.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Pattern;

/**
 * 用户信息更新数据传输对象
 */
@Data
public class UserInfoUpdateDTO {
    
    /**
     * 用户名
     */
    @Length(min = 2, max = 20, message = "用户名长度应在2-20个字符之间")
    private String username;
    
    /**
     * 手机号码
     */
    @Pattern(regexp = "^1\\d{10}$", message = "请输入正确的手机号")
    private String phone;
    
    /**
     * 用户地址
     */
    @Length(max = 200, message = "地址长度不能超过200个字符")
    private String address;
} 