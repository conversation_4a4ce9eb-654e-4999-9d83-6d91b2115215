<template>
  <view class="container">
    <view class="flex flex-col items-center justify-center min-h-screen p-4">
      <view class="w-full max-w-md bg-white rounded-lg shadow-lg p-6">
        <text class="text-2xl font-bold text-center mb-8">维修师注册</text>
        <view class="space-y-4">
          <view class="space-y-2">
            <text class="text-gray-600">用户名</text>
            <input 
              type="text" 
              v-model="form.username" 
              class="w-full px-4 py-2 border rounded-lg focus:border-secondary" 
              placeholder="请输入用户名"
            />
          </view>
          <view class="space-y-2">
            <text class="text-gray-600">手机号</text>
            <input 
              type="number" 
              v-model="form.phone" 
              class="w-full px-4 py-2 border rounded-lg focus:border-secondary" 
              placeholder="请输入手机号"
            />
          </view>
          <view class="space-y-2">
            <text class="text-gray-600">验证码</text>
            <view class="flex">
              <input 
                type="number" 
                v-model="form.verifyCode" 
                class="flex-1 px-4 py-2 border rounded-lg focus:border-secondary" 
                placeholder="请输入验证码"
              />
              <view 
                class="ml-2 px-4 py-2 rounded-lg text-sm bg-secondary text-white flex items-center justify-center"
                :class="{
                  'bg-secondary text-white cursor-pointer': !countdownValue && !isSending,
                  'bg-gray-300 text-gray-600': countdownValue > 0 || isSending
                }"
                :style="{ minWidth: '120px', whiteSpace: 'nowrap' }"
                @click="sendVerificationCode"
              >
                <text v-if="isSending">发送中...</text>
                <text v-else-if="countdownValue > 0">{{ countdownValue }}秒</text>
                <text v-else>获取验证码</text>
              </view>
            </view>
          </view>
          <view class="space-y-2">
            <text class="text-gray-600">密码</text>
            <password-input
              v-model="form.password"
              placeholder="请输入密码"
              placeholder-class="placeholder"
            />
          </view>
          <view class="space-y-2">
            <text class="text-gray-600">确认密码</text>
            <password-input
              v-model="form.confirmPassword"
              placeholder="请再次输入密码"
              placeholder-class="placeholder"
            />
          </view>
          <view class="space-y-2">
            <text class="text-gray-600">技能标签</text>
            <view class="flex flex-wrap gap-2">
              <view
                v-for="(tag, index) in skillTags"
                :key="index"
                class="px-3 py-1 rounded-full cursor-pointer"
                :class="selectedTags.includes(tag) ? 'bg-secondary text-white' : 'bg-gray-100 text-gray-600'"
                @click="toggleTag(tag)"
              >
                {{ tag }}
              </view>
            </view>
          </view>
          <button 
            class="w-full bg-secondary text-white py-3 rounded-lg" 
            :disabled="loading"
            @click="handleRegister"
          >
            {{ loading ? '注册中...' : '注册' }}
          </button>
          <view class="text-center mt-4">
            <text class="text-gray-600">已有账号？</text>
            <text class="text-secondary ml-2" @click="navigateToLogin">立即登录</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 滑块验证组件 -->
    <puzzle-verify
      :visible="showCaptcha"
      @success="captchaService.handleCaptchaSuccess"
      @fail="captchaService.handleCaptchaFail"
      @cancel="captchaService.handleCaptchaCancel"
      @update:visible="showCaptcha = $event"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onUnmounted } from 'vue';
import { repairerApi } from '@/api/repairer';
import { showToast, showLoading, hideLoading } from '@/utils/uniapi';
import { isValidPhone, sendVerifyCode, useCountdown } from '@/utils/verifycode';
import PasswordInput from '@/components/common/PasswordInput.vue';
import { captchaService } from '@/utils/captchaService';
import PuzzleVerify from '@/components/common/PuzzleVerify.vue';

const loading = ref(false);
const form = reactive({
  username: '',
  phone: '',
  verifyCode: '',
  password: '',
  confirmPassword: ''
});

// 滑块验证状态
const showCaptcha = computed({
  get: () => captchaService.showCaptcha.value,
  set: (value) => captchaService.showCaptcha.value = value
});

// 使用统一的验证码倒计时
const phone = computed(() => form.phone);
const countdownState = useCountdown(phone, 'repairer');
const countdownValue = computed(() => countdownState.countdown.value);
const isSending = computed(() => countdownState.isSending.value);

// 组件卸载时清理倒计时
onUnmounted(() => {
  if (countdownState.cleanup) {
    countdownState.cleanup();
  }
});

const skillTags = [
  '电器维修',
  '水管维修',
  '木工维修',
  '空调维修',
  '电脑维修',
  '家具维修'
];

const selectedTags = ref<string[]>([]);

function toggleTag(tag: string) {
  const index = selectedTags.value.indexOf(tag);
  if (index === -1) {
    selectedTags.value.push(tag);
  } else {
    selectedTags.value.splice(index, 1);
  }
}

// 发送验证码
async function sendVerificationCode() {
  // 防止重复点击或倒计时中重复发送
  if (countdownValue.value > 0 || isSending.value) {
    return;
  }
  
  // 验证手机号
  if (!isValidPhone(form.phone)) {
    showToast('请输入正确的手机号码');
    return;
  }
  
  // 使用滑块验证后发送验证码
  await captchaService.sendVerifyCodeWithCaptcha(form.phone, 'repairer', false);
}

function validateForm() {
  if (!form.username || !form.phone || !form.verifyCode || !form.password || !form.confirmPassword) {
    uni.showToast({
      title: '请填写完整信息',
      icon: 'none'
    });
    return false;
  }

  if (form.password !== form.confirmPassword) {
    uni.showToast({
      title: '两次输入的密码不一致',
      icon: 'none'
    });
    return false;
  }

  if (!/^1[3-9]\d{9}$/.test(form.phone)) {
    uni.showToast({
      title: '请输入正确的手机号',
      icon: 'none'
    });
    return false;
  }

  if (selectedTags.value.length === 0) {
    uni.showToast({
      title: '请至少选择一个技能标签',
      icon: 'none'
    });
    return false;
  }

  return true;
}

function handleRegister() {
  if (!validateForm()) return;

  loading.value = true;
  // TODO: 调用注册接口
  setTimeout(() => {
    loading.value = false;
    uni.showToast({
      title: '注册成功',
      icon: 'success'
    });
    // 注册成功后跳转到登录页
    setTimeout(() => {
      uni.redirectTo({
        url: './login'
      });
    }, 1500);
  }, 1500);
}

function navigateToLogin() {
  uni.navigateBack();
}
</script>

<style scoped>
.container {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
}
</style> 