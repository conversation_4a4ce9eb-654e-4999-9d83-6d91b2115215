<template>
  <view class="container">
    <!-- 使用统一的返回按钮组件 -->
    <common-back-button></common-back-button>
    
    <!-- 头部导航 -->
    <view class="header">
      <text class="title">订单详情</text>
    </view>

    <!-- 加载提示 -->
    <view class="loading-container" v-if="loading">
      <view class="loading">加载中...</view>
    </view>

    <!-- 订单详情内容 -->
    <scroll-view class="detail-container" scroll-y v-else>
      <!-- 订单状态条 -->
      <view class="status-bar" :class="getStatusClass(orderDetail.status)">
        <text class="status-text">{{ getStatusText(orderDetail.status) }}</text>
      </view>

      <!-- 订单基本信息 -->
      <view class="info-card">
        <view class="card-title">基本信息</view>
        <view class="info-item">
          <text class="info-label">订单号</text>
          <text class="info-value">{{ orderDetail.orderId || orderDetail.id }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">创建时间</text>
          <text class="info-value">{{ formatDateTime(orderDetail.createTime) }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">预约时间</text>
          <text class="info-value">{{ formatDateTime(orderDetail.appointmentTime) }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">紧急程度</text>
          <text class="info-value urgent-tag" v-if="orderDetail.urgent">紧急</text>
          <text class="info-value" v-else>普通</text>
        </view>
      </view>

      <!-- 维修信息 -->
      <view class="info-card">
        <view class="card-title">维修信息</view>
        <view class="info-item">
          <text class="info-label">联系人</text>
          <text class="info-value">{{ orderDetail.contactName }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">联系电话</text>
          <text class="info-value">{{ orderDetail.contactPhone }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">维修地址</text>
          <text class="info-value">{{ formatAddress(orderDetail.addressInfo) }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">维修描述</text>
          <text class="info-value description">{{ orderDetail.description }}</text>
        </view>
      </view>

      <!-- 维修师信息 -->
      <view class="info-card" v-if="orderDetail.repairerId">
        <view class="card-title">维修师信息</view>
        <view class="info-item">
          <text class="info-label">维修师</text>
          <text class="info-value">{{ orderDetail.repairerName || '未分配' }}{{ orderDetail.repairerPhone ? `(${orderDetail.repairerPhone})` : '' }}</text>
        </view>
      </view>

      <!-- 订单进度 -->
      <view class="info-card" v-if="orderDetail.status > 0 && orderDetail.status < 4">
        <view class="card-title">订单进度</view>
        <view class="timeline">
          <view class="timeline-item" :class="{ active: orderDetail.status >= 1 }">
            <view class="timeline-dot"></view>
            <view class="timeline-content">
              <view class="timeline-title">已接单</view>
              <view class="timeline-time">{{ formatDateTime(orderDetail.updateTime) }}</view>
            </view>
          </view>
          <view class="timeline-item" :class="{ active: orderDetail.status >= 2 }">
            <view class="timeline-dot"></view>
            <view class="timeline-content">
              <view class="timeline-title">处理中</view>
              <view class="timeline-time" v-if="orderDetail.status >= 2">{{ formatDateTime(orderDetail.updateTime) }}</view>
            </view>
          </view>
          <view class="timeline-item" :class="{ active: orderDetail.status >= 3 }">
            <view class="timeline-dot"></view>
            <view class="timeline-content">
              <view class="timeline-title">已完成</view>
              <view class="timeline-time" v-if="orderDetail.status >= 3">{{ formatDateTime(orderDetail.completeTime) }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 评价信息 -->
      <view class="info-card" v-if="orderDetail.status === 3">
        <view class="card-title">服务评价</view>
        <view v-if="orderDetail.rating">
          <view class="rating">
            <text class="star" v-for="index in 5" :key="index">
              {{ index <= orderDetail.rating ? '★' : '☆' }}
            </text>
            <text class="rating-text">{{ orderDetail.rating }}分</text>
          </view>
          <view class="comment" v-if="orderDetail.comment">
            {{ orderDetail.comment }}
          </view>
        </view>
        <view v-else>
          <view class="no-rating">暂未评价</view>
          <view class="rating-form">
            <view class="rating-stars">
              <text class="star" 
                v-for="index in 5" 
                :key="index"
                :class="{ active: ratingValue >= index }"
                @click="setRating(index)">
                {{ ratingValue >= index ? '★' : '☆' }}
              </text>
            </view>
            <textarea 
              class="rating-comment" 
              v-model="commentValue" 
              placeholder="请输入评价内容（选填）"
              placeholder-class="placeholder"></textarea>
            <button class="submit-btn" @click="submitRating">提交评价</button>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons" v-if="orderDetail.status === 0 || orderDetail.status === 2">
        <button class="cancel-btn" @click="cancelOrder">取消订单</button>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, getCurrentInstance } from 'vue';
import { OrderStatus, orderApi, type OrderInfo } from '@/api/order';
import CommonBackButton from '@/components/common/BackButton.vue';
import { useUserStore } from '@/store/user';

// 获取路由参数
const orderId = ref<number>(0);
const loading = ref<boolean>(true);
const errorMessage = ref<string>('');
const retryCount = ref<number>(0);
const orderDetail = reactive<OrderInfo>({
  id: 0,
  orderId: '',
  userId: 0,
  description: '',
  contactName: '',
  contactPhone: '',
  status: OrderStatus.PENDING,
  urgent: false,
  appointmentTime: '',
  createTime: '',
  addressInfo: {
    province: '',
    city: '',
    district: '',
    street: '',
    streetNumber: '',
    detailAddress: '',
    formattedAddress: '',
    latitude: 0,
    longitude: 0,
    accuracy: 0
  }
});

// 评价相关
const ratingValue = ref<number>(0);
const commentValue = ref<string>('');

// 获取页面实例，用于访问小程序特有的生命周期
const instance = getCurrentInstance();

// 同时使用Vue和小程序的生命周期
onMounted(() => {
  console.log('Vue生命周期onMounted执行');
  
  // 只在非小程序环境下尝试获取ID参数
  // #ifndef MP-WEIXIN
  tryGetIdFromRouteParams();
  // #endif
});

// 使用小程序的onLoad生命周期钩子获取参数
// @ts-ignore - onLoad是小程序特有的生命周期
if (instance?.proxy?.$scope) {
  console.log('注册小程序onLoad生命周期');
  // @ts-ignore
  instance.proxy.$scope.onLoad = function(options) {
    console.log('小程序onLoad生命周期触发，参数:', options);
    if (options && options.id) {
      const idNum = parseInt(options.id);
      console.log('onLoad解析后的订单ID:', idNum);
      
      if (!isNaN(idNum)) {
        orderId.value = idNum;
        loadOrderDetail();
      } else {
        showError(`订单ID格式错误: ${options.id}`, true);
      }
    } else {
      console.error('小程序onLoad未获取到ID参数');
    }
  }
}

// 尝试从路由参数获取订单ID (主要用于H5环境)
function tryGetIdFromRouteParams() {
  console.log('尝试从H5路由参数获取订单ID');
  
  try {
    // 尝试获取当前页面实例
    const pages = getCurrentPages();
    // 使用类型断言处理页面实例
    const currentPage = pages[pages.length - 1] as any;
    console.log('getCurrentPages获取到的页面信息:', currentPage ? '成功' : '失败');
    
    // 尝试从页面路由获取ID
    let idFromRoute = null;
    
    // 方法1: 通过$page.fullPath获取
    if (currentPage && currentPage.$page && currentPage.$page.fullPath) {
      const fullPath = currentPage.$page.fullPath;
      console.log('$page.fullPath路径:', fullPath);
      const idMatch = fullPath.match(/id=(\d+)/);
      if (idMatch && idMatch[1]) {
        idFromRoute = parseInt(idMatch[1]);
        console.log('从$page.fullPath提取的ID:', idFromRoute);
      }
    }
    
    // 方法2: 尝试通过options获取
    if (!idFromRoute && currentPage && currentPage.options && currentPage.options.id) {
      idFromRoute = parseInt(currentPage.options.id);
      console.log('从options获取到的ID:', idFromRoute);
    }
    
    // 方法3: H5环境下尝试从URL获取
    // #ifdef H5
    if (!idFromRoute && typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const idStr = urlParams.get('id');
      if (idStr) {
        idFromRoute = parseInt(idStr);
        console.log('从URL参数获取到的ID:', idFromRoute);
      }
    }
    // #endif
    
    // 如果已经成功获取ID，则加载订单详情
    if (idFromRoute && !isNaN(idFromRoute)) {
      console.log('成功获取到有效的订单ID:', idFromRoute);
      
      // 只有在订单ID不同时才重新加载
      if (orderId.value !== idFromRoute) {
        orderId.value = idFromRoute;
        loadOrderDetail();
      } else {
        console.log('订单ID未变化，无需重新加载');
      }
      return true;
    }
    
    // 判断是否已有有效的ID (可能由onLoad设置)
    if (orderId.value > 0) {
      console.log('已经有有效的订单ID:', orderId.value);
      return true;
    }
    
    console.error('无法从任何来源获取有效的订单ID');
    // 只在H5环境显示错误，避免与小程序环境冲突
    // #ifdef H5
    showError('未能找到订单ID', true);
    // #endif
    return false;
  } catch (error) {
    console.error('获取路由参数出错:', error);
    return false;
  }
}

// 加载订单详情
async function loadOrderDetail() {
  if (!orderId.value) {
    console.error('订单ID为空');
    // 区分环境，在小程序中避免自动返回
    // #ifdef MP-WEIXIN
    showError('无效的订单ID');
    // #endif
    // #ifndef MP-WEIXIN
    showError('无效的订单ID', true);
    // #endif
    return;
  }
  
  loading.value = true;
  errorMessage.value = '';
  
  try {
    console.log('开始加载订单详情，订单ID:', orderId.value);
    // 验证用户登录状态
    const userStore = useUserStore();
    if (!userStore.isLoggedIn) {
      console.error('用户未登录或token已过期');
      showError('请先登录后再查看订单详情');
      setTimeout(() => {
        uni.redirectTo({ url: '/pages/user/login' });
      }, 1500);
      return;
    }
    
    console.log('使用token:', userStore.token);
    const { data } = await orderApi.getOrderDetail(orderId.value);
    
    if (!data) {
      console.error('订单详情数据为空');
      // 区分环境，在小程序中避免自动返回
      // #ifdef MP-WEIXIN
      showError('未找到订单信息');
      // #endif
      // #ifndef MP-WEIXIN
      showError('未找到订单信息', true);
      // #endif
      return;
    }
    
    console.log('订单详情数据:', data);
    // 将返回的数据复制到reactive对象中
    Object.assign(orderDetail, data);
    
  } catch (error: any) {
    console.error('获取订单详情失败:', error);
    
    if (error.code === 401) {
      // 401错误单独处理，request.ts中已经处理了登录跳转
      showError('登录已过期，请重新登录');
    } else {
      showError(error.message || '获取订单详情失败');
      
      // 如果连续失败多次，提供返回选项
      if (retryCount.value >= 2) {
        uni.showModal({
          title: '获取订单详情失败',
          content: '是否返回上一页?',
          confirmText: '返回',
          success: (res) => {
            if (res.confirm) {
              uni.navigateBack();
            }
          }
        });
      } else {
        retryCount.value++;
      }
    }
  } finally {
    loading.value = false;
  }
}

// 格式化地址信息
function formatAddress(addressInfo?: any): string {
  if (!addressInfo) return '-';

  // 如果有完整的格式化地址，优先使用
  if (addressInfo.formattedAddress) {
    const detailAddress = addressInfo.detailAddress ? ` ${addressInfo.detailAddress}` : '';
    return `${addressInfo.formattedAddress}${detailAddress}`;
  }

  // 否则拼接地址组件
  const parts = [
    addressInfo.province,
    addressInfo.city,
    addressInfo.district,
    addressInfo.street,
    addressInfo.detailAddress
  ].filter(Boolean);

  return parts.length > 0 ? parts.join('') : '-';
}

// 格式化日期时间
function formatDateTime(dateTime?: string): string {
  if (!dateTime) return '-';
  const date = new Date(dateTime);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
}

// 获取状态文本
function getStatusText(status?: number): string {
  switch (status) {
    case OrderStatus.PENDING:
      return '待处理';
    case OrderStatus.ACCEPTED:
      return '已接单';
    case OrderStatus.PROCESSING:
      return '处理中';
    case OrderStatus.COMPLETED:
      return '已完成';
    case OrderStatus.CANCELED:
      return '已取消';
    default:
      return '未知状态';
  }
}

// 获取状态类名
function getStatusClass(status?: number): string {
  switch (status) {
    case OrderStatus.PENDING:
      return 'status-pending';
    case OrderStatus.ACCEPTED:
      return 'status-accepted';
    case OrderStatus.PROCESSING:
      return 'status-processing';
    case OrderStatus.COMPLETED:
      return 'status-completed';
    case OrderStatus.CANCELED:
      return 'status-canceled';
    default:
      return '';
  }
}

// 取消订单
function cancelOrder() {
  uni.showModal({
    title: '提示',
    content: '确定要取消该订单吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          await orderApi.cancelOrder(orderId.value);
          uni.showToast({
            title: '订单已取消',
            icon: 'success'
          });
          // 延迟1.5秒后重新加载订单详情
          setTimeout(() => {
            loadOrderDetail();
          }, 1500);
        } catch (error) {
          console.error('取消订单失败:', error);
          uni.showToast({
            title: '取消订单失败',
            icon: 'none'
          });
        }
      }
    }
  });
}

// 设置评分
function setRating(value: number) {
  ratingValue.value = value;
}

// 提交评价
async function submitRating() {
  if (ratingValue.value === 0) {
    uni.showToast({
      title: '请选择评分',
      icon: 'none'
    });
    return;
  }
  
  try {
    await orderApi.rateOrder(orderId.value, ratingValue.value, commentValue.value);
    uni.showToast({
      title: '评价成功',
      icon: 'success'
    });
    // 延迟1.5秒后重新加载订单详情
    setTimeout(() => {
      loadOrderDetail();
    }, 1500);
  } catch (error) {
    console.error('评价失败:', error);
    uni.showToast({
      title: '评价失败',
      icon: 'none'
    });
  }
}

// 返回上一页
function navigateBack() {
  uni.navigateBack({
    delta: 1
  });
}

// 显示错误信息
function showError(message: string, shouldReturn: boolean = false) {
  errorMessage.value = message;
  uni.showToast({
    title: message,
    icon: 'none',
    duration: 2000
  });
  
  // 只有在显式要求返回的情况下才执行返回逻辑
  if (shouldReturn) {
    setTimeout(() => {
      navigateBack();
    }, 2000);
  }
}
</script>

<style>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  position: relative;
  height: 96rpx;
  margin-top: 160rpx; /* 为返回按钮留出空间 */
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #f0f0f0;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
}

.detail-container {
  padding-bottom: 40rpx;
}

.status-bar {
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 36rpx;
  font-weight: 500;
}

.status-pending {
  background-color: #1989fa;
}

.status-accepted {
  background-color: #07c160;
}

.status-processing {
  background-color: #ff9800;
}

.status-completed {
  background-color: #27ae60;
}

.status-canceled {
  background-color: #909399;
}

.info-card {
  margin: 24rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 16rpx;
}

.info-item {
  display: flex;
  margin-bottom: 20rpx;
}

.info-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #666;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.description {
  word-break: break-all;
}

.urgent-tag {
  color: #ff4d4f;
  font-weight: 500;
}

.timeline {
  margin-top: 16rpx;
}

.timeline-item {
  position: relative;
  padding-bottom: 48rpx;
  padding-left: 48rpx;
}

.timeline-item:last-child {
  padding-bottom: 0;
}

.timeline-dot {
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: #ddd;
  border: 4rpx solid #f5f5f5;
}

.timeline-item::before {
  content: "";
  position: absolute;
  left: 11rpx;
  top: 24rpx;
  width: 2rpx;
  height: calc(100% - 24rpx);
  background-color: #ddd;
}

.timeline-item:last-child::before {
  display: none;
}

.timeline-item.active .timeline-dot {
  background-color: #07c160;
}

.timeline-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.timeline-time {
  font-size: 24rpx;
  color: #999;
}

.rating {
  margin: 16rpx 0;
}

.star {
  font-size: 40rpx;
  color: #ddd;
  margin-right: 8rpx;
}

.rating .star {
  color: #ff9800;
}

.rating-text {
  font-size: 28rpx;
  color: #333;
  margin-left: 16rpx;
}

.comment {
  font-size: 28rpx;
  color: #666;
  background-color: #f8f8f8;
  padding: 16rpx;
  border-radius: 8rpx;
  margin-top: 16rpx;
}

.no-rating {
  font-size: 28rpx;
  color: #999;
  margin: 16rpx 0;
}

.rating-form {
  margin-top: 24rpx;
}

.rating-stars {
  margin-bottom: 16rpx;
}

.rating-stars .star {
  font-size: 48rpx;
  cursor: pointer;
}

.rating-stars .star.active {
  color: #ff9800;
}

.rating-comment {
  width: 100%;
  height: 160rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  margin-bottom: 24rpx;
}

.placeholder {
  color: #999;
}

.submit-btn {
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  color: #fff;
  background: #07c160;
  border-radius: 40rpx;
  text-align: center;
}

.action-buttons {
  margin: 24rpx;
  padding: 24rpx 0;
}

.cancel-btn {
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  color: #fff;
  background: #ff4d4f;
  border-radius: 40rpx;
  text-align: center;
}
</style> 