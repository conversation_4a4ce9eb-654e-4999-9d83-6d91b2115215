package com.repair.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 用户账号合并日志实体类
 */
@Data
@Accessors(chain = true)
@TableName("user_merge_log")
public class UserMergeLog {
    /**
     * 日志ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 源用户ID（被合并的微信账号）
     */
    private Long sourceUserId;
    
    /**
     * 源用户名
     */
    private String sourceUsername;
    
    /**
     * 源用户OpenID
     */
    private String sourceOpenid;
    
    /**
     * 目标用户ID（保留的手机号账号）
     */
    private Long targetUserId;
    
    /**
     * 目标用户名
     */
    private String targetUsername;
    
    /**
     * 目标用户手机号
     */
    private String targetPhone;
    
    /**
     * 迁移的订单数量
     */
    private Long migratedOrderCount;
    
    /**
     * 合并原因
     */
    private String mergeReason;
    
    /**
     * 合并时间
     */
    private LocalDateTime mergeTime;
    
    /**
     * 操作结果：1-成功，0-失败
     */
    private Integer status;
    
    /**
     * 错误信息（如果有）
     */
    private String errorMessage;
} 