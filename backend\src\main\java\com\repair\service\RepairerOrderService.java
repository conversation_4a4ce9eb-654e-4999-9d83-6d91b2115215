package com.repair.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.repair.entity.RepairOrder;
import com.repair.vo.OrderVO;

/**
 * 维修人员订单服务接口
 */
public interface RepairerOrderService extends IService<RepairOrder> {
    
    /**
     * 获取待处理订单列表
     * @param page 分页参数
     * @return 分页结果
     */
    Page<OrderVO> getPendingOrders(Page<RepairOrder> page);
    
    /**
     * 获取已接单列表
     * @param repairerId 维修人员ID
     * @param page 分页参数
     * @return 分页结果
     */
    Page<OrderVO> getAcceptedOrders(Long repairerId, Page<RepairOrder> page);
    
    /**
     * 接单
     * @param repairerId 维修人员ID
     * @param orderId 订单ID
     * @return 接单后的订单信息
     */
    OrderVO acceptOrder(Long repairerId, Long orderId);
    
    /**
     * 开始处理订单
     * @param repairerId 维修人员ID
     * @param orderId 订单ID
     * @return 处理后的订单信息
     */
    OrderVO processOrder(Long repairerId, Long orderId);
    
    /**
     * 完成订单
     * @param repairerId 维修人员ID
     * @param orderId 订单ID
     * @return 完成后的订单信息
     */
    OrderVO completeOrder(Long repairerId, Long orderId);
    
    /**
     * 转单
     * @param repairerId 当前维修人员ID
     * @param orderId 订单ID
     * @param reason 转单原因
     * @return 转单后的订单信息
     */
    OrderVO transferOrder(Long repairerId, Long orderId, String reason);
    
    /**
     * 获取订单详情
     * @param repairerId 维修人员ID
     * @param orderId 订单ID
     * @return 订单详情
     */
    OrderVO getOrderDetail(Long repairerId, Long orderId);
} 