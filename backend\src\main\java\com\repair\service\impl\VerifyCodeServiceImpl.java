package com.repair.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.repair.entity.Repairer;
import com.repair.entity.User;
import com.repair.exception.BusinessException;
import com.repair.mapper.RepairerMapper;
import com.repair.mapper.UserMapper;
import com.repair.service.VerifyCodeService;
import com.repair.utils.RedisUtil;
import com.repair.utils.LocalVerifyCodeManager;
import com.repair.config.VerifyCodeProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Random;

/**
 * 验证码服务实现类
 */
@Service
@Slf4j
public class VerifyCodeServiceImpl implements VerifyCodeService {

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private VerifyCodeProperties verifyCodeProperties;

    @Autowired(required = false)
    private LocalVerifyCodeManager localVerifyCodeManager;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private RepairerMapper repairerMapper;

    @Autowired(required = false)
    private AliyunSmsService aliyunSmsService;

    private final Random random = new Random();

    @Override
    public String generateVerifyCode(int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(random.nextInt(10));
        }
        return sb.toString();
    }

    @Override
    public String sendVerifyCode(String phone, String userType) {
        log.info("发送验证码请求: 手机号={}, 用户类型={}", phone, userType);
        // 校验手机号格式
        if (!phone.matches("^1[3-9]\\d{9}$")) {
            throw new BusinessException("手机号格式不正确");
        }

        // 检查发送频率
        try {
            long requestInterval = verifyCodeProperties.getRequestInterval();
            if (redisUtil.isVerifyCodeRequestTooFrequent(phone, requestInterval)) {
                log.warn("验证码发送过于频繁: {}, 需等待{}秒", phone, requestInterval);
                throw new BusinessException("验证码发送过于频繁，请" + requestInterval + "秒后再试");
            }

            // 记录本次请求时间
            redisUtil.recordVerifyCodeRequestTime(phone);
        } catch (BusinessException e) {
            // 业务异常直接抛出
            throw e;
        } catch (Exception e) {
            // Redis操作异常时，仍允许发送验证码，但记录警告日志
            log.warn("检查验证码发送频率失败（Redis异常），允许发送: {}", e.getMessage());
        }

        // 生成验证码
        int codeLength = verifyCodeProperties.getLength();
        String verifyCode = generateVerifyCode(codeLength);

        try {
            // 存储到Redis
            redisUtil.saveVerifyCode(phone, verifyCode, verifyCodeProperties.getExpireTime());
            log.info("验证码已存储到Redis: {}, {}", phone, verifyCode);
        } catch (Exception e) {
            log.error("Redis存储验证码失败: {}", e.getMessage());

            // Redis存储失败，检查是否配置了内存降级
            if (localVerifyCodeManager != null) {
                log.warn("使用本地内存存储验证码: {}", phone);
                localVerifyCodeManager.saveVerifyCode(phone, verifyCode, verifyCodeProperties.getExpireTime());
            } else {
                throw new BusinessException("验证码服务暂不可用，请稍后再试");
            }
        }

        // 调用阿里云短信服务发送验证码
        if (aliyunSmsService != null) {
            try {
                if (!aliyunSmsService.sendSms(phone, verifyCode)) {
                    // 如果是因为服务未启用而返回false，这是预期的行为，使用模拟方式
                    log.info("阿里云短信服务未启用或发送失败，使用模拟方式: {}, {}", phone, verifyCode);
                }
            } catch (Exception e) {
                log.error("调用短信服务异常: {}", e.getMessage(), e);
                // 短信发送失败，但验证码已生成，仍然返回成功
                log.warn("短信发送失败，但验证码已生成: {}, {}", phone, verifyCode);
            }
        } else {
            // 短信服务未配置，使用模拟方式
            log.info("短信服务未配置，使用模拟方式: {}, {}", phone, verifyCode);
        }

        return verifyCode;
    }

    @Override
    public boolean validateVerifyCode(String phone, String verifyCode) {
        try {
            // 首先尝试从Redis验证
            boolean isValid = redisUtil.validateVerifyCode(phone, verifyCode);
            if (isValid) {
                return true;
            }
        } catch (Exception e) {
            log.warn("Redis验证失败，尝试使用本地验证: {}", e.getMessage());

            // Redis验证失败，尝试本地验证
            if (localVerifyCodeManager != null) {
                boolean isValid = localVerifyCodeManager.validateVerifyCode(phone, verifyCode);
                if (isValid) {
                    return true;
                }
            }
        }

        log.warn("{}的验证码错误或已过期: {}", phone, verifyCode);
        return false;
    }

    @Override
    public void deleteVerifyCode(String phone) {
        try {
            redisUtil.deleteVerifyCode(phone);
        } catch (Exception e) {
            log.warn("Redis删除验证码失败: {}", e.getMessage());

            // Redis删除失败，尝试从本地存储删除
            if (localVerifyCodeManager != null) {
                localVerifyCodeManager.deleteVerifyCode(phone);
            }
        }
    }

    @Override
    public boolean checkPhoneExist(String phone, String userType) {
        if ("user".equals(userType)) {
            // 普通用户
            LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(User::getPhone, phone);
            return userMapper.selectCount(queryWrapper) > 0;
        } else if ("repairer".equals(userType)) {
            // 维修师
            LambdaQueryWrapper<Repairer> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Repairer::getPhone, phone);
            return repairerMapper.selectCount(queryWrapper) > 0;
        } else if ("admin".equals(userType)) {
            // 管理员不支持通过手机号验证
            return false;
        } else if ("any".equals(userType)) {
            // 任意类型的手机号，用于注册场景
            return true;
        }

        return false;
    }
}