/**
 * 验证码工具模块
 * 统一封装验证码发送、倒计时等通用逻辑
 */

import { ref, watch } from 'vue';
import { userApi } from '@/api/user';
import { repairerApi } from '@/api/repairer';

// 验证码倒计时(单例模式，所有页面共享)
const countdownMap = new Map<string, number>();
const countdownTimers = new Map<string, number>(); // 使用number替代NodeJS.Timeout

// 防抖状态(发送中状态)
const sendingStatusMap = new Map<string, boolean>();

// 验证手机号格式
export function isValidPhone(phone: string): boolean {
  return /^1[3456789]\d{9}$/.test(phone);
}

/**
 * 发送验证码
 * @param phone 手机号
 * @param userType 用户类型：user(普通用户)、repairer(维修师)
 * @param forLogin 是否为登录场景，会影响验证手机号是否存在的策略
 * @param skipCaptcha 是否跳过滑动验证（内部使用，由滑动验证服务传入）
 * @returns Promise<boolean> 是否发送成功
 */
export async function sendVerifyCode(
  phone: string, 
  userType: 'user' | 'repairer', 
  forLogin = false,
  skipCaptcha = false
): Promise<boolean> {
  // 检查手机号格式
  if (!isValidPhone(phone)) {
    uni.showToast({
      title: '请输入正确的手机号码',
      icon: 'none'
    });
    return false;
  }
  
  // 创建防抖键
  const key = `${userType}_${phone}`;
  
  // 检查是否在发送中状态(防抖)
  if (sendingStatusMap.get(key)) {
    console.log('验证码发送请求正在处理中，忽略重复点击');
    return false;
  }
  
  // 检查倒计时
  if (countdownMap.get(key) && countdownMap.get(key)! > 0) {
    uni.showToast({
      title: `请等待${countdownMap.get(key)}秒后再获取验证码`,
      icon: 'none'
    });
    return false;
  }
  
  // 设置发送中状态
  sendingStatusMap.set(key, true);
  
  // 显示加载
  uni.showLoading({
    title: '发送中...',
    mask: true
  });
  
  try {
    console.log(`开始发送验证码请求: 手机号=${phone}, 用户类型=${userType}, 登录场景=${forLogin}, 跳过验证=${skipCaptcha}`);
    
    // 调用对应API发送验证码
    if (userType === 'user') {
      // 修复参数格式，将forLogin作为参数对象的属性
      await userApi.sendVerifyCode(phone, { forLogin });
    } else if (userType === 'repairer') {
      await repairerApi.sendVerifyCode(phone);
    }
    
    uni.hideLoading();
    uni.showToast({
      title: '验证码发送成功',
      icon: 'success'
    });
    
    // 开始倒计时
    startCountdown(key);
    
    return true;
  } catch (error: any) {
    uni.hideLoading();
    console.error('发送验证码失败:', error);
    
    uni.showToast({
      title: error.message || '验证码发送失败，请稍后再试',
      icon: 'none'
    });
    
    return false;
  } finally {
    // 重置发送中状态
    sendingStatusMap.set(key, false);
  }
}

/**
 * 开始倒计时
 * @param key 倒计时键，格式为：userType_phone
 * @param duration 倒计时时长(秒)
 */
function startCountdown(key: string, duration = 60): void {
  // 清除可能存在的旧定时器
  if (countdownTimers.has(key)) {
    clearInterval(countdownTimers.get(key)!);
  }
  
  // 设置初始倒计时值
  countdownMap.set(key, duration);
  
  // 创建新定时器
  const timer = setInterval(() => {
    const currentValue = countdownMap.get(key)!;
    if (currentValue <= 1) {
      // 倒计时结束
      clearInterval(timer);
      countdownMap.set(key, 0);
      countdownTimers.delete(key);
    } else {
      // 倒计时递减
      countdownMap.set(key, currentValue - 1);
    }
  }, 1000);
  
  // 存储定时器ID
  countdownTimers.set(key, timer as unknown as number);
}

/**
 * 获取特定手机号的倒计时值
 * @param phone 手机号
 * @param userType 用户类型
 * @returns 倒计时值(秒)
 */
export function getCountdown(phone: string, userType: 'user' | 'repairer'): number {
  const key = `${userType}_${phone}`;
  return countdownMap.get(key) || 0;
}

/**
 * 检查是否正在发送验证码
 * @param phone 手机号
 * @param userType 用户类型
 * @returns 是否正在发送中
 */
export function isSendingCode(phone: string, userType: 'user' | 'repairer'): boolean {
  const key = `${userType}_${phone}`;
  return sendingStatusMap.get(key) || false;
}

/**
 * 创建响应式倒计时
 * @param phone 手机号引用，当手机号变化时自动更新
 * @param userType 用户类型
 * @returns 响应式倒计时值和发送中状态
 */
export function useCountdown(phone: any, userType: 'user' | 'repairer') {
  const countdown = ref(0);
  const isSending = ref(false);
  
  // 创建更新函数
  const updateCountdown = () => {
    // 获取当前手机号，确保是字符串类型
    const currentPhone = typeof phone === 'object' && phone.value !== undefined 
      ? phone.value.toString() 
      : phone.toString();
    
    // 只有当手机号有效时才更新倒计时状态
    if (currentPhone && currentPhone.length > 0) {
      const key = `${userType}_${currentPhone}`;
      countdown.value = countdownMap.get(key) || 0;
      isSending.value = sendingStatusMap.get(key) || false;
      
      // 调试输出
      console.log(`更新倒计时状态 - 手机号: ${currentPhone}, 倒计时: ${countdown.value}, 发送中: ${isSending.value}`);
    }
  };
  
  // 初始化当前值
  updateCountdown();
  
  // 创建定时器每秒更新
  const interval = setInterval(() => {
    updateCountdown();
  }, 500);
  
  // 组件卸载时清除定时器
  const cleanup = () => {
    clearInterval(interval);
    console.log('倒计时监听已清除');
  };
  
  // 返回响应式倒计时值、发送中状态和清理函数
  return {
    countdown,
    isSending,
    cleanup
  };
} 