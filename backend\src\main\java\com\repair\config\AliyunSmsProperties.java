package com.repair.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 阿里云短信服务配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "app.aliyun-sms")
public class AliyunSmsProperties {

    /**
     * 是否启用阿里云短信服务
     * 开发环境默认不启用，生产环境需要手动启用
     */
    private boolean enabled = false;

    /**
     * 阿里云AccessKey ID
     */
    private String accessKeyId;

    /**
     * 阿里云AccessKey Secret
     */
    private String accessKeySecret;

    /**
     * 短信签名名称
     */
    private String signName;

    /**
     * 短信模板代码
     */
    private String templateCode;

    /**
     * 阿里云区域ID
     */
    private String regionId = "cn-chengdu";

    /**
     * 服务端点
     */
    private String endpoint = "dysmsapi.aliyuncs.com";
}