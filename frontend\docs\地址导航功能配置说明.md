# 地址导航功能配置说明

## 功能概述

维修师端的地址导航功能允许维修师点击订单中的地址，自动打开地图应用进行导航。该功能支持：

- 智能地址解析和坐标转换
- 多种地图应用选择（高德、百度、腾讯、苹果地图）
- 自动降级处理（坐标 → 地理编码 → 地图应用选择）

## 配置步骤

### 1. 地图API密钥配置（二选一或都配置）

#### 选项A：腾讯地图API密钥（推荐）

1. 访问 [腾讯位置服务](https://lbs.qq.com/console/mykey.html)
2. 注册并登录账号
3. 创建应用，选择"微信小程序"类型
4. 获取API Key

配置环境变量：
```env
# .env.development 或 .env.production
VITE_TENCENT_MAP_KEY=您的腾讯地图API密钥
```

#### 选项B：高德地图API密钥（备用）

1. 访问 [高德开放平台](https://console.amap.com/dev/key/app)
2. 注册并登录账号
3. 创建应用，选择"Web服务"类型
4. 获取API Key

配置环境变量：
```env
# .env.development 或 .env.production
VITE_AMAP_KEY=您的高德地图API密钥
```

**注意**：高德地图API密钥现在也从环境变量中获取，不再硬编码在代码中，提高了安全性。

### 2. 地理编码服务优先级

系统会按以下优先级使用地理编码服务：
1. **腾讯地图API**（如果已配置VITE_TENCENT_MAP_KEY）
2. **高德地图API**（如果腾讯地图失败且已配置AMAP_KEY）
3. **降级到地图应用选择器**（如果所有API都不可用）

### 3. 功能验证

配置完成后，在维修师端的以下页面中，地址文字会显示为蓝色并带有下划线，表示可点击：

- 待处理订单列表页面
- 我的订单列表页面  
- 订单详情页面

点击地址后会：
1. 如果地址已有坐标，直接打开系统地图
2. 如果没有坐标，先进行地理编码转换
3. 如果地理编码失败，显示地图应用选择列表

## 功能特性

### 智能地址处理
- **优先级1**：使用现有的经纬度坐标
- **优先级2**：通过地理编码API转换地址为坐标
- **优先级3**：降级到地图应用选择器

### 地图应用支持
- 高德地图
- 百度地图
- 腾讯地图
- 苹果地图（iOS）

### 缓存机制
- 地理编码结果会缓存24小时
- 避免重复API调用，提升响应速度

### 错误处理
- API密钥未配置时自动跳过地理编码
- 网络异常时显示友好提示
- 地图应用未安装时提示用户安装

## 注意事项

### API配额限制
- 高德地图免费版有调用次数限制
- 建议根据业务量选择合适的套餐

### 平台兼容性
- App端：支持所有功能
- H5端：支持基础功能
- 小程序：功能受限，建议在App中使用

### 隐私权限
- 某些地图应用可能需要位置权限
- 建议在应用中说明权限用途

## 故障排除

### 地址点击无反应
1. 检查API密钥是否正确配置
2. 查看控制台是否有错误信息
3. 确认网络连接正常

### 地理编码失败
1. 检查地址格式是否正确
2. 确认API密钥有效且有剩余配额
3. 检查网络连接

### 地图应用打开失败
1. 确认手机已安装对应地图应用
2. 检查应用版本是否支持URL Scheme
3. 尝试选择其他地图应用

## 开发说明

### 核心文件
- `frontend/src/config/map.ts` - 配置文件
- `frontend/src/utils/geocoding.ts` - 地理编码服务
- `frontend/src/utils/mapNavigation.ts` - 导航工具
- `frontend/src/utils/addressFormatter.ts` - 地址格式化

### 扩展功能
如需添加其他地图应用支持，可在 `mapNavigation.ts` 中的 `MAP_APPS` 数组中添加新的地图应用配置。

### 自定义配置
可根据需要修改 `MAP_CONFIG` 中的各项参数，如超时时间、缓存时长等。
