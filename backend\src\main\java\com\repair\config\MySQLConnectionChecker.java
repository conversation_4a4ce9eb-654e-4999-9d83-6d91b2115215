package com.repair.config;

import com.repair.utils.ApplicationContextProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.SQLException;
import java.util.Arrays;

/**
 * MySQL数据库连接检查器
 * 在应用启动时检查数据库连接是否正常
 */
@Component
@Slf4j
@Order(1)  // 确保在环境变量验证器之后运行
public class MySQLConnectionChecker implements ApplicationRunner {

    private final DataSource dataSource;
    private final Environment environment;
    
    @Value("${spring.datasource.url}")
    private String jdbcUrl;
    
    @Value("${spring.datasource.username}")
    private String username;
    
    @Value("${spring.datasource.driver-class-name}")
    private String driverClassName;
    
    @Value("${app.mysql.check-on-startup:true}")
    private boolean checkOnStartup;
    
    @Value("${app.mysql.fail-on-error:false}")
    private boolean failOnError;

    public MySQLConnectionChecker(DataSource dataSource, Environment environment) {
        this.dataSource = dataSource;
        this.environment = environment;
    }
    
    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 检查当前环境
        boolean isProdEnvironment = Arrays.asList(environment.getActiveProfiles()).contains("prod");
        
        if (!checkOnStartup) {
            log.info("MySQL数据库连接检查已禁用");
            return;
        }
        
        log.info("正在检查MySQL数据库连接...");
        log.info("数据库连接信息：");
        log.info("URL: {}", jdbcUrl);
        log.info("用户名: {}", username);
        log.info("驱动类: {}", driverClassName);
        
        // 在生产环境中，检查是否使用了环境变量
        if (isProdEnvironment) {
            log.info("当前运行环境: 生产环境");
            
            // 检查是否使用了环境变量中的配置值
            String envUrl = environment.getProperty("SPRING_DATASOURCE_URL");
            String envUsername = environment.getProperty("SPRING_DATASOURCE_USERNAME");
            
            if (envUrl == null || envUsername == null) {
                log.warn("警告：未检测到数据库连接的环境变量，可能使用了默认值");
            } else {
                log.info("数据库连接使用了环境变量中的配置值");
            }
        } else {
            log.info("当前运行环境: 开发环境");
        }
        
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            log.info("数据库连接成功！");
            log.info("=================================================================");
            log.info("数据库详细信息：");
            log.info("数据库产品名称: {}", metaData.getDatabaseProductName());
            log.info("数据库产品版本: {}", metaData.getDatabaseProductVersion());
            log.info("数据库驱动名称: {}", metaData.getDriverName());
            log.info("数据库驱动版本: {}", metaData.getDriverVersion());
            log.info("JDBC版本: {}.{}", metaData.getJDBCMajorVersion(), metaData.getJDBCMinorVersion());
            log.info("是否支持事务: {}", metaData.supportsTransactions());
            log.info("默认事务隔离级别: {}", getIsolationLevelName(metaData.getDefaultTransactionIsolation()));
            log.info("=================================================================");
        } catch (SQLException e) {
            String errorMsg = String.format(
                "无法连接到MySQL数据库，错误: %s", 
                e.getMessage()
            );
            log.error(errorMsg);
            log.error("=================================================================");
            log.error("MySQL数据库连接失败！请检查：");
            log.error("1. MySQL服务是否已启动");
            log.error("2. application.yml中数据库配置是否正确");
            log.error("3. 如果是远程数据库，检查网络连接和防火墙设置");
            log.error("4. 数据库用户是否有权限连接");
            log.error("=================================================================");

            // 在生产环境中，总是在连接失败时终止应用
            if (failOnError || isProdEnvironment) {
                log.error("由于数据库连接失败，应用将终止启动");
                // 停止应用
                SpringApplication.exit(ApplicationContextProvider.getApplicationContext(), () -> 1);
                System.exit(1);
            }
        }
    }
    
    /**
     * 获取事务隔离级别的名称
     */
    private String getIsolationLevelName(int level) {
        switch (level) {
            case Connection.TRANSACTION_NONE:
                return "TRANSACTION_NONE";
            case Connection.TRANSACTION_READ_UNCOMMITTED:
                return "TRANSACTION_READ_UNCOMMITTED";
            case Connection.TRANSACTION_READ_COMMITTED:
                return "TRANSACTION_READ_COMMITTED";
            case Connection.TRANSACTION_REPEATABLE_READ:
                return "TRANSACTION_REPEATABLE_READ";
            case Connection.TRANSACTION_SERIALIZABLE:
                return "TRANSACTION_SERIALIZABLE";
            default:
                return "UNKNOWN (" + level + ")";
        }
    }
} 