# 维修管理系统前端

## 项目介绍

本项目是维修管理系统的前端部分，基于uni-app框架开发，支持微信小程序和H5多端部署。提供用户报修、维修师傅接单和管理员管理等功能界面。

## 技术栈

- **核心框架**：uni-app + Vue 3 + TypeScript
- **状态管理**：Pinia
- **UI框架**：uni-ui + TailwindCSS
- **HTTP请求**：uni.request封装
- **构建工具**：Vite

## 项目结构

```
frontend/
├── public/               # 静态资源
├── src/
│   ├── api/              # API接口
│   ├── components/       # 公共组件
│   ├── pages/            # 页面组件
│   │   ├── admin/        # 管理员相关页面
│   │   ├── repairer/     # 维修师傅相关页面
│   │   ├── user/         # 用户相关页面
│   │   └── index/        # 入口页面
│   ├── static/           # 静态资源
│   ├── store/            # Pinia状态管理
│   ├── types/            # TypeScript类型定义
│   ├── utils/            # 工具函数
│   ├── App.vue           # 应用入口组件
│   ├── main.ts           # 主入口文件
│   ├── pages.json        # 页面路由配置
│   ├── manifest.json     # 应用配置
│   └── uni.scss          # 全局样式变量
├── .gitignore            # Git忽略文件
├── package.json          # 项目依赖
├── tsconfig.json         # TypeScript配置
├── vite.config.ts        # Vite配置
└── tailwind.config.js    # TailwindCSS配置
```

## 开发环境要求

- Node.js 16+
- pnpm 7+
- HBuilderX (可选，用于调试小程序)

## 本地开发设置

### 1. 克隆仓库

```bash
git clone <仓库地址>
cd repair-system/frontend
```

### 2. 安装依赖

```bash
# 建议使用pnpm
pnpm install
```

### 3. 启动开发服务器

```bash
# 启动开发服务器
pnpm dev
```

开发服务器将在 http://localhost:5173 上运行。

### 4. 调试不同平台

```bash
# H5平台
pnpm dev:h5

# 微信小程序
pnpm dev:mp-weixin
```

## 构建和部署

### H5版本构建

```bash
pnpm build:h5
```

构建产物将位于`dist/build/h5`目录。

### 微信小程序构建

```bash
pnpm build:mp-weixin
```

构建产物将位于`dist/build/mp-weixin`目录，可使用微信开发者工具打开进行预览和上传。

## 代码规范

项目使用ESLint和Prettier进行代码格式化和规范检查。

```bash
# 运行代码检查
pnpm lint

# 运行代码格式化
pnpm format
```

## 主要功能模块

### 用户模块
- 用户注册/登录
- 提交维修订单
- 查看订单状态
- 个人信息管理

### 维修师傅模块
- 维修师傅注册/登录
- 查看可接订单
- 接单/取消接单
- 订单管理

### 管理员模块
- 管理员登录
- 用户/维修师傅管理
- 订单管理
- 系统配置

## 样式主题定制

项目使用TailwindCSS进行样式管理，可通过修改`tailwind.config.js`文件自定义主题。

```js
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: '#07c160', // 修改主题色
        // ...更多自定义颜色
      },
      // ...其他自定义主题配置
    },
  },
};
```

## 接口代理配置

开发环境使用Vite代理转发API请求，配置位于`vite.config.ts`：

```typescript
// vite.config.ts
export default defineConfig({
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:18080',
        changeOrigin: true,
        // rewrite: (path) => path.replace(/^\/api/, '')
      },
    },
  },
});
```

## 常见问题

1. **跨域问题**  
   确保后端服务已正确配置CORS，或者在前端开发环境中使用正确的Vite代理配置。

2. **小程序登录问题**  
   微信小程序登录需要使用特定的API，与H5版本使用不同的逻辑，请参考`src/utils/auth.ts`中的平台判断。

3. **样式不一致问题**  
   不同平台间样式可能有差异，建议使用条件编译保证各平台体验一致。

## 贡献指南

1. Fork项目
2. 创建特性分支：`git checkout -b feature/xxx`
3. 提交更改：`git commit -m 'Add some feature'`
4. 推送到分支：`git push origin feature/xxx`
5. 提交Pull Request

## 页面截图

*(此处可以添加几张关键页面的截图)*

## 许可证

[MIT License](LICENSE) 