# 开发环境特有配置
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************
    username: repair_user
    password: repair_password
  
  # Redis开发环境配置
  redis:
    host: localhost
    port: 6379
    database: 0
    password: admin2012!

# MyBatis-Plus开发环境特有配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# JWT开发环境配置
jwt:
  secret: repair_system_secret_key_Q+DFbT9gjlcxBiEMDWsTw04NiFfZJ6EmIoBAxZbtaZM=

# 微信小程序开发环境配置
wechat:
  miniapp:
    appid: wx26b4fa1d2a7fbd9a
    secret: 76a3f50550ee186c8f53f9954450cc45

# 自定义配置
app:
  mysql:
    check-on-startup: true
    fail-on-error: false
  # 阿里云SMS配置
  aliyun-sms:
    enabled: false  # 是否启用阿里云短信服务（默认不启用）
    access-key-id: ${ALIYUN_ACCESS_KEY_ID:your_access_key_id}
    access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET:your_access_key_secret}
    sign-name: ${ALIYUN_SIGN_NAME:your_sign_name}
    template-code: ${ALIYUN_TEMPLATE_CODE:SMS_319371136}
    region-id: cn-chengdu
    endpoint: dysmsapi.aliyuncs.com


# 阿里云SMS配置
aliyun-sms:
  enabled: true  # 是否启用阿里云短信服务（默认不启用）
  access-key-id: ${ALIYUN_ACCESS_KEY_ID:your_access_key_id}
  access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET:your_access_key_secret}
  sign-name: ${ALIYUN_SIGN_NAME:your_sign_name}
  template-code: ${ALIYUN_TEMPLATE_CODE:SMS_319371136}
  region-id: cn-chengdu
  endpoint: dysmsapi.aliyuncs.com


# 日志开发环境配置
logging:
  file:
    path: logs
  level:
    root: INFO
    com.repair: INFO
    com.repair.mapper: DEBUG
    com.repair.utils.WechatUtil: DEBUG
    org.springframework: INFO