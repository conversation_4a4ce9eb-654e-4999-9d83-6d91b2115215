package com.repair.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * Redis工具类
 */
@Slf4j
@Component
public class RedisUtil {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 存储数据
     *
     * @param key   键
     * @param value 值
     */
    public void set(String key, Object value) {
        redisTemplate.opsForValue().set(key, value);
    }

    /**
     * 存储数据并设置过期时间
     *
     * @param key     键
     * @param value   值
     * @param timeout 过期时间
     * @param unit    时间单位
     */
    public void set(String key, Object value, long timeout, TimeUnit unit) {
        redisTemplate.opsForValue().set(key, value, timeout, unit);
    }

    /**
     * 获取数据
     *
     * @param key 键
     * @return 值
     */
    public Object get(String key) {
        try {
            return redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            log.error("获取数据失败", e);
            return null;
        }
    }

    /**
     * 删除数据
     *
     * @param key 键
     * @return 是否成功
     */
    public Boolean delete(String key) {
        return redisTemplate.delete(key);
    }

    /**
     * 判断key是否存在
     *
     * @param key 键
     * @return 是否存在
     */
    public Boolean hasKey(String key) {
        return redisTemplate.hasKey(key);
    }

    /**
     * 设置过期时间
     *
     * @param key     键
     * @param timeout 过期时间
     * @param unit    时间单位
     * @return 是否成功
     */
    public Boolean expire(String key, long timeout, TimeUnit unit) {
        return redisTemplate.expire(key, timeout, unit);
    }

    /**
     * 获取指定key的过期时间
     *
     * @param key 键
     * @return 过期时间（秒）
     */
    public Long getExpire(String key) {
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    /**
     * 保存验证码
     *
     * @param phone      手机号
     * @param verifyCode 验证码
     * @param expireTime 过期时间（秒）
     */
    public void saveVerifyCode(String phone, String verifyCode, long expireTime) {
        String key = "verify_code:" + phone;
        set(key, verifyCode, expireTime, TimeUnit.SECONDS);
    }

    /**
     * 获取验证码
     *
     * @param phone 手机号
     * @return 验证码
     */
    public String getVerifyCode(String phone) {
        String key = "verify_code:" + phone;
        Object code = get(key);
        return code != null ? code.toString() : null;
    }

    /**
     * 验证验证码
     *
     * @param phone      手机号
     * @param verifyCode 验证码
     * @return 是否有效
     */
    public boolean validateVerifyCode(String phone, String verifyCode) {
        String key = "verify_code:" + phone;
        Object savedCode = get(key);
        return savedCode != null && savedCode.toString().equals(verifyCode);
    }

    /**
     * 删除验证码
     *
     * @param phone 手机号
     */
    public void deleteVerifyCode(String phone) {
        String key = "verify_code:" + phone;
        delete(key);
    }

    /**
     * 检查验证码请求是否过于频繁
     *
     * @param phone 手机号
     * @param minInterval 最小间隔时间（秒）
     * @return 是否过于频繁
     */
    public boolean isVerifyCodeRequestTooFrequent(String phone, long minInterval) {
        try {
            String key = "verify_code_last_request:" + phone;
            Object lastRequestTime = get(key);
            
            if (lastRequestTime == null) {
                // 第一次请求
                return false;
            }
            
            // 计算上次请求到现在的时间差
            long now = System.currentTimeMillis();
            long last = Long.parseLong(lastRequestTime.toString());
            long diff = (now - last) / 1000; // 转换为秒
            
            return diff < minInterval;
        } catch (Exception e) {
            log.error("检查验证码请求频率失败", e);
            // 发生错误时，不阻止用户请求
            return false;
        }
    }
    
    /**
     * 记录验证码请求时间
     *
     * @param phone 手机号
     */
    public void recordVerifyCodeRequestTime(String phone) {
        String key = "verify_code_last_request:" + phone;
        set(key, String.valueOf(System.currentTimeMillis()), 24, TimeUnit.HOURS);
    }
} 