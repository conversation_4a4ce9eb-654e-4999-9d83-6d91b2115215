/**
 * 拼图滑块验证码服务
 * 统一管理拼图滑块验证的状态和逻辑
 */

import { ref } from 'vue';
import { sendVerifyCode } from './verifycode';

// 验证状态
const showCaptcha = ref(false);
const verifyCaptchaSuccess = ref(false);
const currentPhone = ref('');
const currentUserType = ref<'user' | 'repairer'>('user');
const currentForLogin = ref(false);
const verifyCallback = ref<null | ((success: boolean) => void)>(null);

/**
 * 显示滑动验证码
 * @param phone 手机号
 * @param userType 用户类型
 * @param forLogin 是否为登录场景
 * @param callback 验证回调
 */
function showSliderCaptcha(
  phone: string,
  userType: 'user' | 'repairer',
  forLogin = false,
  callback?: (success: boolean) => void
) {
  // 存储当前参数
  currentPhone.value = phone;
  currentUserType.value = userType;
  currentForLogin.value = forLogin;
  
  // 重置验证状态
  verifyCaptchaSuccess.value = false;
  
  // 设置验证回调
  verifyCallback.value = callback || null;
  
  // 显示验证码
  showCaptcha.value = true;
  
  console.log(`显示滑动验证码：手机号=${phone}, 用户类型=${userType}, 登录场景=${forLogin}`);
}

/**
 * 处理验证成功
 * 验证成功后发送验证码
 */
async function handleCaptchaSuccess() {
  console.log('滑动验证成功');
  
  verifyCaptchaSuccess.value = true;
  
  try {
    // 调用验证码发送函数
    const result = await sendVerifyCode(
      currentPhone.value,
      currentUserType.value,
      currentForLogin.value,
      true // 标记已通过滑动验证
    );
    
    // 调用回调函数
    if (verifyCallback.value) {
      verifyCallback.value(result);
    }
    
    return result;
  } catch (error) {
    console.error('验证成功后发送验证码失败:', error);
    
    // 调用回调函数
    if (verifyCallback.value) {
      verifyCallback.value(false);
    }
    
    return false;
  } finally {
    // 隐藏验证码
    hideCaptcha();
  }
}

/**
 * 处理验证失败
 */
function handleCaptchaFail() {
  console.log('滑动验证失败');
  
  verifyCaptchaSuccess.value = false;
  
  // 调用回调函数
  if (verifyCallback.value) {
    verifyCallback.value(false);
  }
}

/**
 * 处理取消验证
 */
function handleCaptchaCancel() {
  console.log('取消滑动验证');
  
  verifyCaptchaSuccess.value = false;
  
  // 调用回调函数
  if (verifyCallback.value) {
    verifyCallback.value(false);
  }
  
  // 隐藏验证码
  hideCaptcha();
}

/**
 * 隐藏验证码
 */
function hideCaptcha() {
  showCaptcha.value = false;
}

/**
 * 统一的验证码发送函数，包含滑动验证
 * @param phone 手机号
 * @param userType 用户类型
 * @param forLogin 是否为登录场景
 * @returns Promise<boolean> 是否发送成功
 */
function sendVerifyCodeWithCaptcha(
  phone: string,
  userType: 'user' | 'repairer',
  forLogin = false
): Promise<boolean> {
  return new Promise((resolve) => {
    // 显示滑动验证码
    showSliderCaptcha(phone, userType, forLogin, (success) => {
      resolve(success);
    });
  });
}

// 导出验证服务
export const captchaService = {
  showCaptcha,
  verifyCaptchaSuccess,
  showSliderCaptcha,
  handleCaptchaSuccess,
  handleCaptchaFail,
  handleCaptchaCancel,
  hideCaptcha,
  sendVerifyCodeWithCaptcha
}; 