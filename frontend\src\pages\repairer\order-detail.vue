<template>
  <view class="container">
    <!-- 使用统一的返回按钮组件 -->
    <common-back-button></common-back-button>
    
    <!-- 头部导航 -->
    <view class="header">
      <text class="title">订单详情</text>
    </view>

    <!-- 加载提示 -->
    <view class="loading-container" v-if="loading">
      <view class="loading">加载中...</view>
    </view>

    <!-- 错误提示 -->
    <view v-else-if="error" class="error">
      <text>{{ error }}</text>
      <button @click="fetchOrderDetail" class="retry-btn">重试</button>
    </view>

    <!-- 订单详情内容 -->
    <scroll-view class="detail-container" scroll-y v-else-if="orderDetail">
      <!-- 订单状态条 -->
      <view class="status-bar" :class="getStatusClass(orderDetail.status)">
        <text class="status-text">{{ getStatusText(orderDetail.status) }}</text>
      </view>

      <!-- 订单基本信息 -->
      <view class="info-card">
        <view class="card-title">基本信息</view>
        <view class="info-item">
          <text class="info-label">订单号</text>
          <text class="info-value">{{ orderDetail.orderId || orderDetail.id }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">创建时间</text>
          <text class="info-value">{{ formatDateTime(orderDetail.createTime) }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">预约时间</text>
          <text class="info-value">{{ formatDateTime(orderDetail.appointmentTime) }}</text>
        </view>
        <view class="info-item" v-if="orderDetail.urgent">
          <text class="info-label">紧急程度</text>
          <text class="info-value urgent-tag">紧急</text>
        </view>
      </view>

      <!-- 维修信息 -->
      <view class="info-card">
        <view class="card-title">维修信息</view>
        <view class="info-item">
          <text class="info-label">联系人</text>
          <text class="info-value">{{ orderDetail.contactName }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">联系电话</text>
          <text class="info-value">{{ orderDetail.contactPhone }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">维修地址</text>
          <view class="address-container">
            <text
              class="info-value address-clickable"
              @click="handleAddressClick"
            >
              {{ formatAddress(orderDetail.addressInfo, orderDetail.address) }}
            </text>
            <text v-if="orderDistance" class="distance-text">(约{{ orderDistance }})</text>
          </view>
        </view>
        <view class="info-item">
          <text class="info-label">维修描述</text>
          <text class="info-value description">{{ orderDetail.description }}</text>
        </view>
      </view>

      <!-- 订单进度 -->
      <view class="info-card" v-if="orderDetail.status > 0 && orderDetail.status < 4">
        <view class="card-title">订单进度</view>
        <view class="timeline">
          <view class="timeline-item" :class="{ active: orderDetail.status >= 1 }">
            <view class="timeline-dot"></view>
            <view class="timeline-content">
              <view class="timeline-title">已接单</view>
              <view class="timeline-time">{{ formatDateTime(orderDetail.updateTime) }}</view>
            </view>
          </view>
          <view class="timeline-item" :class="{ active: orderDetail.status >= 2 }">
            <view class="timeline-dot"></view>
            <view class="timeline-content">
              <view class="timeline-title">处理中</view>
              <view class="timeline-time" v-if="orderDetail.status >= 2">{{ formatDateTime(orderDetail.updateTime) }}</view>
            </view>
          </view>
          <view class="timeline-item" :class="{ active: orderDetail.status >= 3 }">
            <view class="timeline-dot"></view>
            <view class="timeline-content">
              <view class="timeline-title">已完成</view>
              <view class="timeline-time" v-if="orderDetail.status >= 3">{{ formatDateTime(orderDetail.completeTime) }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 评价信息 -->
      <view class="info-card" v-if="orderDetail.status === 3 && orderDetail.rating">
        <view class="card-title">用户评价</view>
        <view>
          <view class="rating">
            <text class="star" v-for="index in 5" :key="index">
              {{ index <= (orderDetail.rating || 0) ? '★' : '☆' }}
            </text>
            <text class="rating-text">{{ orderDetail.rating }}分</text>
          </view>
          <view class="comment" v-if="orderDetail.comment">
            {{ orderDetail.comment }}
          </view>
          <view class="no-comment" v-else>
            用户未留下评价内容
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons" v-if="orderDetail.status === 1">
        <button class="cancel-btn" @click="cancelOrder">取消接单</button>
        <button class="process-btn" @click="processOrder">开始处理</button>
      </view>
      
      <!-- 处理中状态的操作按钮 -->
      <view class="action-buttons" v-if="orderDetail.status === 2">
        <button class="cancel-btn" @click="cancelOrder">取消接单</button>
        <button class="complete-btn" @click="completeOrder">完成订单</button>
      </view>
    </scroll-view>
    
    <!-- 空状态 -->
    <view v-else class="empty">
      <text>暂无订单详情</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, getCurrentInstance } from 'vue';
import { useRepairerStore } from '@/store/repairer';
import { repairerApi, type OrderInfo } from '@/api/repairer';
import { formatAddress } from '@/utils/addressFormatter';
import { quickNavigate } from '@/utils/mapNavigation';
import { useDistance } from '@/composables/useDistance';
import CommonBackButton from '@/components/common/BackButton.vue';

const repairerStore = useRepairerStore();
const orderId = ref<string>('');
const orderDetail = ref<OrderInfo | null>(null);
const loading = ref<boolean>(false);
const error = ref<string>('');
const orderDistance = ref<string>('');

// 使用距离计算组合式函数
const { calculateOrderDistance } = useDistance();

// 获取页面实例，用于访问小程序特有的生命周期
const instance = getCurrentInstance();

// 同时使用Vue和小程序的生命周期
onMounted(() => {
  console.log('Vue生命周期onMounted执行');
  tryGetIdFromRouteParams();
});

// 使用小程序的onLoad生命周期钩子获取参数
// @ts-ignore - onLoad是小程序特有的生命周期
if (instance?.proxy?.$scope) {
  console.log('注册小程序onLoad生命周期');
  // @ts-ignore
  instance.proxy.$scope.onLoad = function(options) {
    console.log('小程序onLoad生命周期触发，参数:', options);
    if (options && options.id) {
      console.log('onLoad获取到的订单ID:', options.id);
      orderId.value = options.id;
      fetchOrderDetail();
    } else {
      showErrorAndGoBack('订单ID不存在');
    }
  };
}

// 尝试从路由参数中获取订单ID
function tryGetIdFromRouteParams() {
  // 获取当前页面路由
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  // @ts-ignore
  const options = currentPage.options || {};
  
  console.log('从路由参数中获取订单ID:', options);
  
  if (options.id) {
    orderId.value = options.id;
    fetchOrderDetail();
  } else if (!orderId.value) {
    showErrorAndGoBack('未找到订单ID');
  }
}

// 获取订单详情
async function fetchOrderDetail() {
  loading.value = true;
  error.value = '';
  
  try {
    const id = orderId.value;
    if (!id) {
      throw new Error('订单ID不能为空');
    }
    
    console.log('请求订单详情，ID:', id);
    const res = await repairerApi.getOrderDetail(parseInt(id));
    console.log('订单详情响应:', res);
    
    if (res.code === 0 || res.code === 200) {
      orderDetail.value = res.data;

      // 计算距离
      if (orderDetail.value?.addressInfo) {
        try {
          const distance = await calculateOrderDistance(orderDetail.value.addressInfo);
          if (distance) {
            orderDistance.value = distance;
          }
        } catch (distanceError) {
          console.warn('计算距离失败:', distanceError);
        }
      }
    } else {
      throw new Error(res.message || '获取订单详情失败');
    }
  } catch (err: any) {
    console.error('获取订单详情失败:', err);
    error.value = err.message || '获取订单详情失败，请稍后重试';
    
    // 处理401错误，跳转到登录页
    if (err.code === 401) {
      repairerStore.clearRepairerInfo();
      uni.redirectTo({ url: '/pages/repairer/login' });
      return;
    }
    
    uni.showToast({
      title: error.value,
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
}

// 完成订单
async function completeOrder() {
  uni.showModal({
    title: '确认完成',
    content: '确认已完成维修工作？',
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({ title: '处理中...' });
          // 调用完成订单接口
          const result = await repairerApi.completeOrder(parseInt(orderId.value));
          
          uni.hideLoading();
          if (result.code === 200) {
            uni.showToast({
              title: '订单已完成',
              icon: 'success'
            });
            
            // 延迟1.5秒后刷新订单详情
            setTimeout(() => {
              fetchOrderDetail();
            }, 1500);
          } else {
            uni.showToast({
              title: result.message || '操作失败',
              icon: 'none'
            });
          }
        } catch (error) {
          uni.hideLoading();
          console.error('操作失败', error);
          uni.showToast({
            title: '操作失败',
            icon: 'none'
          });
        }
      }
    }
  });
}

// 取消接单
async function cancelOrder() {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消接单吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({ title: '处理中...' });
          const result = await repairerApi.cancelOrder(parseInt(orderId.value));
          
          uni.hideLoading();
          if (result.code === 200) {
            uni.showToast({
              title: '已取消接单',
              icon: 'success'
            });
            
            // 延迟1.5秒后返回
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          } else {
            uni.showToast({
              title: result.message || '取消失败',
              icon: 'none'
            });
          }
        } catch (error) {
          uni.hideLoading();
          console.error('取消失败', error);
          uni.showToast({
            title: '取消失败',
            icon: 'none'
          });
        }
      }
    }
  });
}

// 开始处理订单
async function processOrder() {
  uni.showModal({
    title: '确认开始处理',
    content: '确认开始处理此订单？',
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({ title: '处理中...' });
          // 调用开始处理订单接口
          const result = await repairerApi.processOrder(parseInt(orderId.value));
          
          uni.hideLoading();
          if (result.code === 200) {
            uni.showToast({
              title: '已开始处理',
              icon: 'success'
            });
            
            // 延迟1.5秒后刷新订单详情
            setTimeout(() => {
              fetchOrderDetail();
            }, 1500);
          } else {
            uni.showToast({
              title: result.message || '操作失败',
              icon: 'none'
            });
          }
        } catch (error) {
          uni.hideLoading();
          console.error('操作失败', error);
          uni.showToast({
            title: '操作失败',
            icon: 'none'
          });
        }
      }
    }
  });
}

// 处理地址点击事件
function handleAddressClick() {
  if (!orderDetail.value) {
    uni.showToast({
      title: '订单信息不完整',
      icon: 'none',
      duration: 2000
    });
    return;
  }

  console.log('点击地址导航:', {
    addressInfo: orderDetail.value.addressInfo,
    address: orderDetail.value.address
  });

  // 使用快速导航功能
  quickNavigate(
    orderDetail.value.addressInfo,
    orderDetail.value.address
  );
}

// 格式化时间
function formatDateTime(dateString?: string): string {
  if (!dateString) return '未设置';
  try {
    const date = new Date(dateString);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  } catch (error) {
    console.error('时间格式化错误:', error);
    return dateString;
  }
}

// 获取状态文本
function getStatusText(status: number): string {
  const statusMap: Record<number, string> = {
    0: '待处理',
    1: '已接单',
    2: '处理中',
    3: '已完成',
    4: '已取消'
  };
  return statusMap[status] || '未知状态';
}

// 获取状态样式类
function getStatusClass(status: number): string {
  const classMap: Record<number, string> = {
    0: 'status-pending',
    1: 'status-accepted',
    2: 'status-processing',
    3: 'status-completed',
    4: 'status-canceled'
  };
  return classMap[status] || '';
}

// 显示错误并返回
function showErrorAndGoBack(message: string) {
  uni.showToast({
    title: message,
    icon: 'none',
    duration: 2000
  });
  
  setTimeout(() => {
    uni.navigateBack();
  }, 2000);
}
</script>

<style>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}

.header {
  height: 96rpx;
  margin-top: 160rpx; /* 为返回按钮留出空间 */
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #f0f0f0;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

.loading-container {
  height: calc(100vh - 256rpx);
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading {
  font-size: 28rpx;
  color: #999;
}

.detail-container {
  height: calc(100vh - 256rpx);
  padding: 24rpx;
  box-sizing: border-box;
}

.status-bar {
  height: 100rpx;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.status-pending {
  background: linear-gradient(to right, #ff9800, #ff7100);
}

.status-accepted {
  background: linear-gradient(to right, #2196f3, #1976d2);
}

.status-processing {
  background: linear-gradient(to right, #2196f3, #1976d2);
}

.status-completed {
  background: linear-gradient(to right, #4caf50, #388e3c);
}

.status-canceled {
  background: linear-gradient(to right, #9e9e9e, #757575);
}

.status-text {
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
}

.info-card {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16rpx;
}

.info-item {
  display: flex;
  margin-bottom: 16rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  min-width: 160rpx;
  font-size: 28rpx;
  color: #666;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
}

.address-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.address-clickable {
  color: #2196f3 !important;
  text-decoration: underline;
  cursor: pointer;
  transition: all 0.3s ease;
}

.address-clickable:active {
  opacity: 0.7;
  transform: scale(0.98);
}

.distance-text {
  font-size: 24rpx;
  color: #666;
  font-style: italic;
}

.description {
  white-space: pre-wrap;
}

.urgent-tag {
  color: #f44336;
  font-weight: 500;
  background-color: rgba(244, 67, 54, 0.1);
  padding: 2rpx 10rpx;
  border-radius: 4rpx;
}

.timeline {
  padding: 16rpx 0;
}

.timeline-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
  position: relative;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: 8rpx;
  top: 24rpx;
  width: 2rpx;
  height: calc(100% + 24rpx);
  background-color: #e0e0e0;
  z-index: 1;
}

.timeline-item:last-child::before {
  display: none;
}

.timeline-dot {
  width: 18rpx;
  height: 18rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
  margin-right: 16rpx;
  margin-top: 8rpx;
  position: relative;
  z-index: 2;
}

.timeline-item.active .timeline-dot {
  background-color: #2196f3;
}

.timeline-content {
  flex: 1;
}

.timeline-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.timeline-item.active .timeline-title {
  color: #2196f3;
  font-weight: 500;
}

.timeline-time {
  font-size: 24rpx;
  color: #999;
}

.rating {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.star {
  color: #ff9800;
  font-size: 36rpx;
  margin-right: 4rpx;
}

.rating-text {
  font-size: 28rpx;
  color: #333;
  margin-left: 16rpx;
}

.comment {
  font-size: 28rpx;
  color: #333;
  background-color: #f9f9f9;
  padding: 16rpx;
  border-radius: 8rpx;
}

.no-comment {
  font-size: 28rpx;
  color: #999;
  padding: 16rpx 0;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  padding: 24rpx 0;
}

.cancel-btn, .complete-btn {
  width: 48%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 30rpx;
  font-weight: 500;
}

.cancel-btn {
  background-color: #fff;
  color: #666;
  border: 1px solid #ddd;
}

.complete-btn {
  width: 48%;
  background: linear-gradient(to right, #2196f3, #1976d2);
  color: #fff;
}

.process-btn {
  width: 100%;
  background: linear-gradient(to right, #ff9800, #f57c00);
  color: #fff;
  margin-bottom: 16rpx;
}

.error {
  text-align: center;
  color: #ff4d4f;
  
  .retry-btn {
    margin-top: 20rpx;
    padding: 10rpx 30rpx;
    background-color: #1890ff;
    color: #fff;
    border-radius: 4rpx;
    font-size: 28rpx;
  }
}
</style> 