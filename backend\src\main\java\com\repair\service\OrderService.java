package com.repair.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.repair.dto.OrderDTO;
import com.repair.entity.RepairOrder;
import com.repair.vo.OrderVO;

/**
 * 订单服务接口
 */
public interface OrderService extends IService<RepairOrder> {
    
    /**
     * 创建订单
     * @param userId 用户ID
     * @param orderDTO 订单信息
     * @return 订单视图对象
     */
    OrderVO createOrder(Long userId, OrderDTO orderDTO);
    
    /**
     * 获取用户订单列表
     * @param userId 用户ID
     * @param status 订单状态(-1表示全部)
     * @param page 分页参数
     * @return 分页结果
     */
    Page<OrderVO> getUserOrders(Long userId, Integer status, Page<RepairOrder> page);
    
    /**
     * 获取订单详情
     * @param orderId 订单ID
     * @return 订单详情
     */
    OrderVO getOrderDetail(Long orderId);
    
    /**
     * 取消订单
     * @param userId 用户ID
     * @param orderId 订单ID
     * @return 取消后的订单
     */
    OrderVO cancelOrder(Long userId, Long orderId);
    
    /**
     * 评价订单
     * @param userId 用户ID
     * @param orderId 订单ID
     * @param rating 评分
     * @param comment 评价内容
     * @return 评价后的订单
     */
    OrderVO rateOrder(Long userId, Long orderId, Integer rating, String comment);
} 