// 地图服务配置
export const MAP_CONFIG = {
  // 高德地图Web服务API密钥
  // 请在高德开放平台申请：https://console.amap.com/dev/key/app
  // 从环境变量中获取，格式：VITE_AMAP_KEY
  AMAP_KEY: import.meta.env.VITE_AMAP_KEY || 'your_amap_key_here',
  
  // 地理编码API配置
  GEOCODING: {
    // API基础URL
    BASE_URL: 'https://restapi.amap.com/v3/geocode/geo',
    // 请求超时时间（毫秒）
    TIMEOUT: 10000,
    // 输出格式
    OUTPUT: 'json'
  },
  
  // 地图导航配置
  NAVIGATION: {
    // 默认地图缩放级别
    DEFAULT_SCALE: 18,
    // 是否显示加载提示
    SHOW_LOADING: true,
    // 缓存过期时间（毫秒）
    CACHE_EXPIRE_TIME: 24 * 60 * 60 * 1000 // 24小时
  }
};

// 检查配置是否有效
export function isMapConfigValid(): boolean {
  return MAP_CONFIG.AMAP_KEY !== 'your_amap_key_here' &&
         MAP_CONFIG.AMAP_KEY &&
         MAP_CONFIG.AMAP_KEY.length > 0;
}

// 获取配置状态信息
export function getConfigStatus(): {
  isValid: boolean;
  message: string;
} {
  if (!MAP_CONFIG.AMAP_KEY || MAP_CONFIG.AMAP_KEY === 'your_amap_key_here') {
    return {
      isValid: false,
      message: '请在环境变量中配置高德地图API密钥 (VITE_AMAP_KEY)'
    };
  }
  
  if (MAP_CONFIG.AMAP_KEY.length < 10) {
    return {
      isValid: false,
      message: 'API密钥格式不正确'
    };
  }
  
  return {
    isValid: true,
    message: '配置正常'
  };
}
