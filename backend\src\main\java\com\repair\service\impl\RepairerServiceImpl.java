package com.repair.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.repair.dto.RepairerLoginDTO;
import com.repair.entity.Repairer;
import com.repair.entity.RepairOrder;
import com.repair.mapper.RepairerMapper;
import com.repair.mapper.RepairOrderMapper;
import com.repair.service.RepairerService;
import com.repair.utils.JwtUtil;
import com.repair.utils.AddressUtil;
import com.repair.vo.RepairerInfoVO;
import com.repair.vo.RepairerLoginVO;
import com.repair.vo.PageVO;
import com.repair.vo.OrderVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;
import com.repair.exception.BusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.repair.enums.OrderStatusEnum;
import com.repair.dto.RepairerInfoUpdateDTO;
import com.repair.dto.PasswordChangeDTO;
import com.repair.dto.ResetPasswordDTO;
import com.repair.service.VerifyCodeService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import javax.servlet.http.HttpServletRequest;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;

@Service
@RequiredArgsConstructor
public class RepairerServiceImpl extends ServiceImpl<RepairerMapper, Repairer> implements RepairerService {

    private static final Logger log = LoggerFactory.getLogger(RepairerServiceImpl.class);
    
    // 注入订单Mapper
    private final RepairOrderMapper repairOrderMapper;
    
    // 注入验证码服务
    private final VerifyCodeService verifyCodeService;
    
    @Override
    public RepairerLoginVO login(RepairerLoginDTO loginDTO) {
        // 根据手机号查询维修师
        LambdaQueryWrapper<Repairer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Repairer::getPhone, loginDTO.getPhone());
        Repairer repairer = getOne(queryWrapper);

        // 校验维修师是否存在
        if (repairer == null) {
            log.info("维修师登录失败，维修师不存在，手机号：{}", loginDTO.getPhone());
            throw new BusinessException("账号或密码错误");
        }

        // 密码比对
        String password = DigestUtils.md5DigestAsHex(loginDTO.getPassword().getBytes());
        if (!password.equals(repairer.getPassword())) {
            log.info("维修师登录失败，密码错误，手机号：{}", loginDTO.getPhone());
            throw new BusinessException("账号或密码错误");
        }

        // 生成Token
        String token = JwtUtil.createToken(repairer.getId(), repairer.getUsername(), "repairer");
        log.info("生成token成功,token:{}", token);

        // 构造返回数据
        RepairerInfoVO repairerInfoVO = new RepairerInfoVO();
        BeanUtils.copyProperties(repairer, repairerInfoVO);

        return RepairerLoginVO.builder()
                .token(token)
                .repairerInfo(repairerInfoVO)
                .build();
    }
    
    @Override
    public RepairerInfoVO getRepairerInfo() {
        // 从当前请求上下文中获取用户ID
        Long repairerId = JwtUtil.getCurrentUserId();
        
        // 查询维修师信息
        Repairer repairer = getById(repairerId);
        if (repairer == null) {
            log.error("获取维修师信息失败，维修师不存在，ID：{}", repairerId);
            throw new BusinessException("维修师不存在");
        }
        
        // 构造返回数据
        RepairerInfoVO repairerInfoVO = new RepairerInfoVO();
        BeanUtils.copyProperties(repairer, repairerInfoVO);
        
        return repairerInfoVO;
    }
    
    @Override
    public PageVO<OrderVO> getPendingOrders(Integer page, Integer size) {
        // 创建分页对象
        Page<RepairOrder> pageParam = new Page<>(page, size);
        
        // 构建查询条件
        LambdaQueryWrapper<RepairOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RepairOrder::getStatus, OrderStatusEnum.PENDING.getCode()); // 待处理状态
        queryWrapper.orderByDesc(RepairOrder::getCreateTime);
        
        // 查询数据
        Page<RepairOrder> pageResult = repairOrderMapper.selectPage(pageParam, queryWrapper);
        
        // 构造分页VO对象
        PageVO<OrderVO> pageVO = new PageVO<>();
        pageVO.setTotal(pageResult.getTotal());
        pageVO.setCurrent(page);
        pageVO.setSize(size);
        pageVO.setPages((int) pageResult.getPages());
        
        // 转换订单对象
        List<OrderVO> orderVOList = new ArrayList<>();
        for (RepairOrder order : pageResult.getRecords()) {
            OrderVO orderVO = new OrderVO();
            BeanUtils.copyProperties(order, orderVO);

            // 转换地址信息
            orderVO.setAddressInfo(AddressUtil.convertEntityToAddressVO(order));

            orderVOList.add(orderVO);
        }
        pageVO.setRecords(orderVOList);
        
        return pageVO;
    }
    
    @Override
    public PageVO<OrderVO> getAcceptedOrders(Integer page, Integer size) {
        // 获取当前维修师ID
        Long repairerId = JwtUtil.getCurrentUserId();
        
        // 创建分页对象
        Page<RepairOrder> pageParam = new Page<>(page, size);
        
        // 构建查询条件
        LambdaQueryWrapper<RepairOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RepairOrder::getRepairerId, repairerId);
        // 已接单、处理中或已完成的订单
        queryWrapper.in(RepairOrder::getStatus, 
            Arrays.asList(OrderStatusEnum.ACCEPTED.getCode(), OrderStatusEnum.PROCESSING.getCode(), OrderStatusEnum.COMPLETED.getCode()));
        queryWrapper.orderByDesc(RepairOrder::getUpdateTime);
        
        // 查询数据
        Page<RepairOrder> pageResult = repairOrderMapper.selectPage(pageParam, queryWrapper);
        
        // 构造分页VO对象
        PageVO<OrderVO> pageVO = new PageVO<>();
        pageVO.setTotal(pageResult.getTotal());
        pageVO.setCurrent(page);
        pageVO.setSize(size);
        pageVO.setPages((int) pageResult.getPages());
        
        // 转换订单对象
        List<OrderVO> orderVOList = new ArrayList<>();
        for (RepairOrder order : pageResult.getRecords()) {
            OrderVO orderVO = new OrderVO();
            BeanUtils.copyProperties(order, orderVO);

            // 转换地址信息
            orderVO.setAddressInfo(AddressUtil.convertEntityToAddressVO(order));

            // 设置状态描述
            orderVO.setStatusDesc(OrderStatusEnum.getDescByCode(orderVO.getStatus()));

            orderVOList.add(orderVO);
        }
        pageVO.setRecords(orderVOList);
        
        return pageVO;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void acceptOrder(Long orderId) {
        // 获取当前维修师ID
        Long repairerId = JwtUtil.getCurrentUserId();
        
        // 查询订单
        RepairOrder order = repairOrderMapper.selectById(orderId);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        
        // 检查订单状态
        if (order.getStatus() != OrderStatusEnum.PENDING.getCode()) {
            throw new BusinessException("订单状态不允许接单");
        }
        
        // 更新订单信息
        order.setRepairerId(repairerId);
        order.setStatus(OrderStatusEnum.ACCEPTED.getCode());  // 更新为已接单状态
        order.setUpdateTime(LocalDateTime.now());
        
        // 保存更新
        int rows = repairOrderMapper.updateById(order);
        if (rows != 1) {
            throw new BusinessException("接单失败，请稍后重试");
        }
        
        log.info("维修师[{}]接单成功，订单ID：{}", repairerId, orderId);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelOrder(Long orderId) {
        // 使用通用方法检查权限
        RepairOrder order = checkOrderPermission(orderId);
        
        // 检查订单状态 - 允许取消已接单和处理中的订单
        if (order.getStatus() != OrderStatusEnum.ACCEPTED.getCode() && order.getStatus() != OrderStatusEnum.PROCESSING.getCode()) {
            throw new BusinessException("订单状态不允许取消");
        }
        
        // 更新订单信息
        order.setRepairerId(null);
        order.setStatus(OrderStatusEnum.PENDING.getCode());  // 重置为待处理状态
        order.setUpdateTime(LocalDateTime.now());
        
        // 保存更新
        int rows = repairOrderMapper.updateById(order);
        if (rows != 1) {
            throw new BusinessException("取消接单失败，请稍后重试");
        }
        
        log.info("维修师[{}]取消接单成功，订单ID：{}", JwtUtil.getCurrentUserId(), orderId);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void completeOrder(Long orderId) {
        // 使用通用方法检查权限
        RepairOrder order = checkOrderPermission(orderId);
        
        // 检查订单状态
        if (order.getStatus() != OrderStatusEnum.PROCESSING.getCode()) {
            throw new BusinessException("订单状态不允许完成操作");
        }
        
        // 更新订单信息
        order.setStatus(OrderStatusEnum.COMPLETED.getCode());  // 更新为已完成状态
        order.setUpdateTime(LocalDateTime.now());
        order.setCompleteTime(LocalDateTime.now());
        
        // 保存更新
        int rows = repairOrderMapper.updateById(order);
        if (rows != 1) {
            throw new BusinessException("完成订单失败，请稍后重试");
        }
        
        log.info("维修师[{}]完成订单成功，订单ID：{}", JwtUtil.getCurrentUserId(), orderId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processOrder(Long orderId) {
        // 使用通用方法检查权限
        RepairOrder order = checkOrderPermission(orderId);
        
        // 检查订单状态
        if (order.getStatus() != OrderStatusEnum.ACCEPTED.getCode()) {
            throw new BusinessException("订单状态不允许开始处理");
        }
        
        // 更新订单信息
        order.setStatus(OrderStatusEnum.PROCESSING.getCode());  // 更新为处理中状态
        order.setUpdateTime(LocalDateTime.now());
        
        // 保存更新
        int rows = repairOrderMapper.updateById(order);
        if (rows != 1) {
            throw new BusinessException("开始处理订单失败，请稍后重试");
        }
        
        log.info("维修师[{}]开始处理订单，订单ID：{}", JwtUtil.getCurrentUserId(), orderId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RepairerInfoVO updateRepairerInfo(RepairerInfoUpdateDTO updateDTO) {
        // 获取当前维修师ID
        Long repairerId = JwtUtil.getCurrentUserId();
        
        // 查询维修师信息
        Repairer repairer = getById(repairerId);
        if (repairer == null) {
            log.error("更新维修师信息失败，维修师不存在，ID：{}", repairerId);
            throw new BusinessException("维修师不存在");
        }
        
        // 更新维修师信息
        repairer.setUsername(updateDTO.getUsername());
        if (updateDTO.getSkillTags() != null) {
            // 处理中文逗号，统一替换为英文逗号
            String skillTags = updateDTO.getSkillTags().replace("，", ",");
            repairer.setSkillTags(skillTags);
        }
        repairer.setUpdateTime(LocalDateTime.now());
        
        // 保存更新
        boolean result = updateById(repairer);
        if (!result) {
            log.error("更新维修师信息失败，ID：{}", repairerId);
            throw new BusinessException("更新信息失败，请稍后重试");
        }
        
        // 构造返回数据
        RepairerInfoVO repairerInfoVO = new RepairerInfoVO();
        BeanUtils.copyProperties(repairer, repairerInfoVO);
        
        log.info("维修师[{}]更新信息成功", repairerId);
        return repairerInfoVO;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changePassword(PasswordChangeDTO passwordChangeDTO) {
        // 获取当前维修师ID
        Long repairerId = JwtUtil.getCurrentUserId();
        
        // 查询维修师信息
        Repairer repairer = getById(repairerId);
        if (repairer == null) {
            log.error("修改密码失败，维修师不存在，ID：{}", repairerId);
            throw new BusinessException("维修师不存在");
        }
        
        // 直接设置新密码，不校验旧密码
        String newPasswordMD5 = DigestUtils.md5DigestAsHex(passwordChangeDTO.getNewPassword().getBytes());
        repairer.setPassword(newPasswordMD5);
        repairer.setUpdateTime(LocalDateTime.now());
        
        // 保存更新
        boolean result = updateById(repairer);
        if (!result) {
            log.error("修改密码失败，ID：{}", repairerId);
            throw new BusinessException("修改密码失败，请稍后重试");
        }
        
        log.info("维修师[{}]修改密码成功", repairerId);
    }

    @Override
    public void sendVerifyCode(String phone) {
        // 使用统一验证码服务，直接转发调用
        verifyCodeService.sendVerifyCode(phone, "repairer");
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetPassword(ResetPasswordDTO resetPasswordDTO) {
        log.info("维修师傅开始处理重置密码请求，手机号: {}", resetPasswordDTO.getPhone());
        
        try {
            // 验证验证码
            String phone = resetPasswordDTO.getPhone();
            String verifyCode = resetPasswordDTO.getVerifyCode();
            
            // 使用统一验证码服务验证
            if (!verifyCodeService.validateVerifyCode(phone, verifyCode)) {
                log.warn("{}的验证码错误或已过期: {}", phone, verifyCode);
                throw new BusinessException("验证码错误或已过期");
            }
            
            // 根据手机号查询维修师
            LambdaQueryWrapper<Repairer> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Repairer::getPhone, phone);
            Repairer repairer = getOne(queryWrapper);
            
            if (repairer == null) {
                log.warn("维修师不存在: {}", phone);
                throw new BusinessException("维修师不存在");
            }
            
            log.info("维修师信息查询成功: {}", repairer.getUsername());
            
            // 检查维修师状态
            if (repairer.getStatus() == null || repairer.getStatus() != 1) {
                log.warn("账号已被禁用: {}", repairer.getUsername());
                throw new BusinessException("账号已被禁用");
            }
            
            // 密码加密
            String encryptedPassword = DigestUtils.md5DigestAsHex(resetPasswordDTO.getNewPassword().getBytes());
            repairer.setPassword(encryptedPassword);
            repairer.setUpdateTime(LocalDateTime.now());
            
            // 更新维修师
            updateById(repairer);
            
            // 验证码使用后删除
            verifyCodeService.deleteVerifyCode(phone);
            
            log.info("维修师密码重置成功: {}", repairer.getUsername());
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("维修师重置密码失败", e);
            throw new BusinessException("重置密码失败: " + e.getMessage());
        }
    }

    @Override
    public Long getProcessingOrderCount() {
        // 获取当前维修师ID
        Long repairerId = JwtUtil.getCurrentUserId();
        
        // 构建查询条件
        LambdaQueryWrapper<RepairOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RepairOrder::getRepairerId, repairerId);
        // 已接单和处理中状态的订单
        queryWrapper.in(RepairOrder::getStatus, 
            Arrays.asList(OrderStatusEnum.ACCEPTED.getCode(), OrderStatusEnum.PROCESSING.getCode()));
        
        // 查询数量
        return repairOrderMapper.selectCount(queryWrapper);
    }

    @Override
    public Long getPendingOrderCount() {
        // 构建查询条件
        LambdaQueryWrapper<RepairOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RepairOrder::getStatus, OrderStatusEnum.PENDING.getCode()); // 待处理状态
        
        // 查询数量
        return repairOrderMapper.selectCount(queryWrapper);
    }

    /**
     * 检查维修师是否有权限操作订单
     * @param orderId 订单ID
     * @return 订单对象
     */
    private RepairOrder checkOrderPermission(Long orderId) {
        // 获取当前维修师ID
        Long repairerId = JwtUtil.getCurrentUserId();
        
        // 查询订单
        RepairOrder order = repairOrderMapper.selectById(orderId);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        
        // 获取当前用户角色
        String currentRole = getCurrentUserRole();
        
        // 检查是否是该维修师的订单，管理员除外
        if (!"admin".equals(currentRole) && !repairerId.equals(order.getRepairerId())) {
            log.warn("维修师 {} 尝试操作不属于自己的订单 {}", repairerId, orderId);
            throw new BusinessException("无权限操作他人接的订单");
        }
        
        return order;
    }

    /**
     * 获取当前用户角色
     */
    private String getCurrentUserRole() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                return (String) request.getAttribute("userRole");
            }
        } catch (Exception e) {
            log.error("获取用户角色失败", e);
        }
        return "";
    }
} 