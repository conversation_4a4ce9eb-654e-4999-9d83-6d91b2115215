name: repair-system  # 指定项目名称
version: '3.8'

services:
  # 后端服务
  backend:  # 服务名称，用于Docker Compose内部引用
    build:
      context: .  # 指定构建上下文为当前目录
      dockerfile: Dockerfile  # 指定Dockerfile文件
    image: repair-system-backend:latest  # 镜像名称
    container_name: repair-system-backend  # 容器名称，用于Docker命令引用
    restart: always  # 容器崩溃时自动重启
    ports:
      - "${BACKEND_PORT:-18080}:18080"  # 端口映射，将主机的BACKEND_PORT(默认8080)映射到容器的8080端口
    environment:  # 环境变量配置，用于Spring Boot应用的配置
      - SPRING_PROFILES_ACTIVE=prod  # 激活生产环境配置
      - SPRING_DATASOURCE_URL=${SPRING_DATASOURCE_URL}  # 数据库连接URL
      - SPRING_DATASOURCE_USERNAME=${SPRING_DATASOURCE_USERNAME}  # 数据库用户名
      - SPRING_DATASOURCE_PASSWORD=${SPRING_DATASOURCE_PASSWORD}  # 数据库密码
      - SPRING_REDIS_HOST=${SPRING_REDIS_HOST}  # Redis主机地址
      - SPRING_REDIS_PORT=${SPRING_REDIS_PORT}  # Redis端口
      - SPRING_REDIS_DATABASE=${SPRING_REDIS_DATABASE}  # Redis数据库
      - SPRING_REDIS_PASSWORD=${SPRING_REDIS_PASSWORD}  # Redis密码
      - JWT_SECRET=${JWT_SECRET}  # JWT签名密钥
      - JWT_EXPIRATION=${JWT_EXPIRATION}  # JWT过期时间
      - WECHAT_MINIAPP_APPID=${WECHAT_MINIAPP_APPID}  # 微信小程序AppID
      - WECHAT_MINIAPP_SECRET=${WECHAT_MINIAPP_SECRET}  # 微信小程序Secret
      - APP_MYSQL_CHECK_ON_STARTUP=${APP_MYSQL_CHECK_ON_STARTUP}  # MySQL连接检查开关
      - APP_MYSQL_FAIL_ON_ERROR=${APP_MYSQL_FAIL_ON_ERROR}  # MySQL连接失败时是否终止应用
      - ALIYUN_ACCESS_KEY_ID=${ALIYUN_ACCESS_KEY_ID}  # 阿里云AccessKey ID
      - ALIYUN_ACCESS_KEY_SECRET=${ALIYUN_ACCESS_KEY_SECRET}  # 阿里云AccessKey Secret
      - ALIYUN_SIGN_NAME=${ALIYUN_SIGN_NAME}  # 阿里云短信签名名称
      - ALIYUN_TEMPLATE_CODE=${ALIYUN_TEMPLATE_CODE}  # 阿里云短信模板代码
      - APP_ALIYUN_SMS_ENABLED=${APP_ALIYUN_SMS_ENABLED:-false}  # 是否启用阿里云短信服务
    volumes:
      - ./logs:/app/logs  # 卷挂载，将容器内的日志目录映射到主机
    depends_on:  # 依赖关系，确保MySQL和Redis在后端服务之前启动
      - mysql
      - redis
    networks:
      - repair-network  # 连接到指定网络
    healthcheck:  # 健康检查配置，用于监控容器健康状态
      test: ["CMD", "curl", "-f", "http://localhost:18080/api/health"]  # 健康检查命令
      interval: 30s  # 检查间隔
      timeout: 10s  # 超时时间
      retries: 3  # 重试次数
      start_period: 40s  # 启动后等待时间


  # MySQL数据库
  mysql:
    image: mysql:8.0  # 使用MySQL 8.0版本镜像
    container_name: repair-system-mysql
    restart: always
    ports:
      - "${MYSQL_PORT:-3306}:3306"  # 将主机的MYSQL_PORT映射到容器的3306端口
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}  # MySQL root用户密码
      - MYSQL_DATABASE=${MYSQL_DATABASE}  # 要创建的数据库名
      - MYSQL_USER=${MYSQL_USER}  # 要创建的MySQL用户
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}  # MySQL用户密码
    volumes:
      - mysql-data:/var/lib/mysql  # 持久化MySQL数据
      - ./src/main/resources/db/schema.sql:/docker-entrypoint-initdb.d/1-schema.sql  # 数据库初始化脚本，创建表结构
      - ./src/main/resources/db/data.sql:/docker-entrypoint-initdb.d/2-data.sql  # 数据库初始化脚本，插入初始数据
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci  # MySQL服务器配置，支持完整的UTF-8字符集
    networks:
      - repair-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Redis缓存
  redis:
    image: redis:6.2-alpine  # 使用轻量级的Alpine版Redis 6.2
    container_name: repair-system-redis
    restart: always
    ports:
      - "${REDIS_PORT:-6379}:6379"  # 将主机的REDIS_PORT(默认6379)映射到容器的6379端口
    command: redis-server --requirepass ${REDIS_PASSWORD}  # 启动Redis服务器并设置密码
    volumes:
      - redis-data:/data  # 持久化Redis数据
    networks:
      - repair-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]  # 使用密码验证的健康检查
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  # Nginx反向代理（可选，没有单独的nginx服务就取消注释）
  # nginx:
  #   image: nginx:1.21-alpine  # 使用轻量级的Alpine版Nginx 1.21
  #   container_name: repair-system-nginx
  #   restart: always
  #   ports:
  #     - "${NGINX_HTTP_PORT:-80}:80"  # HTTP端口映射
  #     - "${NGINX_HTTPS_PORT:-443}:443"  # HTTPS端口映射
  #   volumes:
  #     - ./nginx/conf.d:/etc/nginx/conf.d  # Nginx配置文件
  #     - ./nginx/ssl:/etc/nginx/ssl  # SSL证书
  #     - ./nginx/logs:/var/log/nginx  # Nginx日志
  #   depends_on:
  #     - backend  # 依赖后端服务
  #   networks:
  #     - repair-network
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost/nginx-health"]  # 检查Nginx健康状态
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 10s

# 定义持久化卷
volumes:
  mysql-data:  # MySQL数据持久化卷
    name: repair-system-mysql-data  # 自定义卷名称，便于识别
  redis-data:  # Redis数据持久化卷
    name: repair-system-redis-data  # 自定义卷名称，便于识别

# 定义网络
networks:
  repair-network:  # 自定义网络，用于容器间通信
    driver: bridge  # 使用bridge驱动，适用于单主机部署
    name: repair-system-network  # 自定义网络名称，便于识别 