# 维修管理系统

一个基于现代技术栈构建的维修管理系统，支持多端部署（H5、微信小程序和PC端），实现用户报修、维修师傅接单和管理员管理等功能。

## 项目架构

项目采用前后端分离架构：

- **前端**：基于uni-app(v3.0.0+) + Vue3(v3.4.21) + TypeScript + Pinia(v2.1.6) + Vite(v5.2.8) + TailwindCSS(v3.3.3)构建，支持H5和小程序多端
- **后端**：基于Spring Boot(v2.7.9) + MyBatis-Plus(v3.5.3) + MySQL(v8.0.28)构建RESTful API

![系统架构图](./frontend/static/images/architecture.png)

## 主要功能

- **用户端**：注册/登录、提交维修订单、查看订单状态、更新个人信息
- **维修师傅端**：注册/登录、查看订单池、接单/取消接单、完成订单
- **管理员端**：用户管理、维修师傅管理、订单管理、统计分析

## 项目结构

```
repair-system/
├── frontend/            # 前端项目 (uni-app)
│   ├── src/             # 源代码
│   ├── README.md        # 前端项目说明
│   └── ...
├── backend/             # 后端项目 (Spring Boot)
│   ├── src/             # 源代码
│   ├── nginx/           # Nginx配置文件
│   │   ├── conf.d/      # Nginx站点配置
│   │   ├── ssl/         # SSL证书
│   │   └── logs/        # Nginx日志
│   ├── README.md        # 后端项目说明
│   └── ...
├── README.md            # 项目总体说明
├── TODO.md              # 项目开发计划和进度
└── DEPLOYMENT.md        # 详细部署指南
```

## 快速开始

### 前置条件

- Node.js 16+
- JDK 17+
- MySQL 8.0+
- pnpm 7+ (推荐)

### 安装和运行

#### 1. 克隆仓库

```bash
git clone <仓库地址>
cd repair-system
```

#### 2. 后端服务启动

```bash
# 导入数据库脚本
# 执行repair-system/backend/src/main/resources/db目录下的SQL脚本

# 编译后端项目
cd backend
mvn clean package -DskipTests

# 启动后端服务
java -jar target/repair-system-0.0.1-SNAPSHOT.jar
```

> **注意**: 数据库初始化脚本(schema.sql和data.sql)已针对MySQL 8.0进行了优化，确保表结构在CREATE TABLE阶段完整定义，避免使用不兼容的ALTER TABLE语法。
>
> **重要提醒**: 如果你自定义了 Docker 启动脚本（如 `docker-entrypoint.sh`），请务必确保该文件为 UTF-8 无 BOM 编码且 LF 行尾格式，否则容器启动时会报 `no such file or directory` 或 `Illegal option -` 等错误。可用 VSCode 右下角切换编码和行尾格式。

后端服务将在 http://localhost:18080 上运行。

#### 3. 前端项目启动

```bash
# 安装依赖
cd frontend
pnpm install

# 启动开发服务器
pnpm dev
```

前端开发服务器将在 http://localhost:5173 上运行。

## 部署

详细的部署说明请参考 [部署指南文档](./DEPLOYMENT.md)，其中包含：

- 前端微信小程序的构建、上传与发布流程
- 后端服务的Docker部署方案
- HTTPS配置与证书管理
- 系统监控与维护建议

### 开发环境

- 前端：使用Vite开发服务器
- 后端：直接运行Spring Boot应用
- 数据库：本地MySQL实例

### 生产环境

- 前端：构建静态资源，部署到Nginx或云服务
- 后端：打包为JAR文件，部署到服务器或容器
- 数据库：生产级MySQL服务器或云数据库

## 接口文档

后端API文档基于Swagger生成，可在后端服务启动后访问：
http://localhost:18080/swagger-ui/index.html

## 开发规范

- **Git提交规范**：使用Angular提交规范
- **代码风格**：前端使用ESLint+Prettier，后端使用阿里Java规范
- **API设计**：遵循RESTful风格
- **文档要求**：代码必须包含完整注释，关键功能需编写文档

## 贡献指南

1. Fork项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 提交Pull Request

## 技术特点

- 多端适配：同一套代码支持H5、微信小程序、PC端
- 响应式设计：适应不同尺寸的设备
- 主题定制：支持深色模式和自定义主题
- 组件抽象：高度可复用的业务组件
- JWT认证：安全的用户认证机制
- 数据校验：前后端双重验证保证数据安全

## 许可证

[MIT License](LICENSE)

## 联系方式

- 项目负责人：[姓名](mailto:<EMAIL>)
- 项目仓库：[GitHub地址] 