<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useAdminStore } from '@/store/admin';
import { adminApi } from '@/api/admin';
import type { UserInfo } from '@/api/user';
import CommonBackButton from '@/components/common/BackButton.vue';

const adminStore = useAdminStore();

// 页面数据
const loading = ref(false);
const refreshing = ref(false);
const keyword = ref('');
const userList = ref<UserInfo[]>([]);
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0,
  hasMore: true
});

// 页面初始化
onMounted(() => {
  // 验证登录状态
  if (!adminStore.token) {
    uni.redirectTo({ url: './login' });
    return;
  }
  
  // 加载用户列表
  loadUserList();
});

// 加载用户列表
async function loadUserList(reset = true) {
  if (reset) {
    pagination.page = 1;
    pagination.hasMore = true;
  }
  
  if (!pagination.hasMore || loading.value) return;
  
  try {
    loading.value = true;
    
    const params: any = {
      page: pagination.page,
      size: pagination.size
    };
    
    // 只有当关键词有效时才添加到查询参数
    if (keyword.value && keyword.value.trim() !== '') {
      params.keyword = keyword.value.trim();
    }
    
    const { data } = await adminApi.getUserList(params);
    
    if (reset) {
      userList.value = data.records;
    } else {
      userList.value = [...userList.value, ...data.records];
    }
    
    pagination.total = data.total;
    pagination.hasMore = userList.value.length < pagination.total;
    pagination.page++;
  } catch (error) {
    console.error('获取用户列表失败', error);
    uni.showToast({
      title: '获取用户列表失败',
      icon: 'none'
    });
  } finally {
    loading.value = false;
    if (refreshing.value) {
      uni.stopPullDownRefresh();
      refreshing.value = false;
    }
  }
}

// 搜索用户
function searchUsers() {
  loadUserList(true);
}

// 清空搜索
function clearSearch() {
  keyword.value = '';
  loadUserList(true);
}

// 下拉刷新
function onPullDownRefresh() {
  refreshing.value = true;
  loadUserList(true);
}

// 上拉加载更多
function onReachBottom() {
  if (pagination.hasMore && !loading.value) {
    loadUserList(false);
  }
}

// 修改用户状态
async function toggleUserStatus(user: UserInfo) {
  try {
    const newStatus = user.status === 1 ? 0 : 1;
    
    await adminApi.updateUserStatus(user.id, newStatus);
    
    // 更新本地数据
    user.status = newStatus;
    
    uni.showToast({
      title: newStatus === 1 ? '已启用用户' : '已禁用用户',
      icon: 'success'
    });
  } catch (error) {
    console.error('修改用户状态失败', error);
    uni.showToast({
      title: '操作失败',
      icon: 'none'
    });
  }
}

// 查看用户详情
const showUserDetail = ref(false);
const currentUser = ref<UserInfo | null>(null);

async function viewUserDetail(user: UserInfo) {
  try {
    loading.value = true;
    // 获取用户详情
    const { data } = await adminApi.getUserDetail(user.id);
    currentUser.value = data;
    showUserDetail.value = true;
  } catch (error) {
    console.error('获取用户详情失败', error);
    uni.showToast({
      title: '获取用户详情失败',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
}

function closeUserDetail() {
  showUserDetail.value = false;
  setTimeout(() => {
    currentUser.value = null;
  }, 300);
}

// 格式化日期
function formatDate(dateString?: string) {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
}
</script>

<template>
  <view class="container">
    <!-- 使用统一的返回按钮组件 -->
    <common-back-button></common-back-button>
    
    <!-- 顶部标题栏 -->
    <view class="header">
      <text class="title">用户管理</text>
      <view class="placeholder"></view>
    </view>
    
    <!-- 搜索区域 -->
    <view class="search-container">
      <view class="search-box">
        <view class="search-icon">🔍</view>
        <input 
          type="text" 
          v-model="keyword" 
          class="search-input" 
          placeholder="搜索用户名/手机号" 
          confirm-type="search"
          @confirm="searchUsers"
        />
        <text v-if="keyword" class="clear-icon" @click="clearSearch">✕</text>
      </view>
      <view class="search-button" @click="searchUsers">搜索</view>
    </view>
    
    <!-- 用户列表 -->
    <view class="user-list" v-if="userList.length > 0">
      <view v-for="user in userList" :key="user.id" class="user-card">
        <view class="user-info">
          <view class="user-avatar">
            <text class="avatar-text">{{ user.username?.substring(0, 1) || '用' }}</text>
          </view>
          <view class="user-detail">
            <view class="user-name-row">
              <text class="user-name">{{ user.username }}</text>
              <view :class="['status-tag', user.status === 1 ? 'status-active' : 'status-disabled']">
                {{ user.status === 1 ? '正常' : '禁用' }}
              </view>
            </view>
            <text class="user-phone">{{ user.phone || '未设置手机号' }}</text>
            <text class="user-date">注册时间：{{ formatDate(user.createTime) }}</text>
          </view>
        </view>
        <view class="user-actions">
          <view 
            :class="['action-button', user.status === 1 ? 'disable-button' : 'enable-button']"
            @click="toggleUserStatus(user)"
          >
            {{ user.status === 1 ? '禁用' : '启用' }}
          </view>
          <view 
            class="action-button detail-button"
            @click="viewUserDetail(user)"
          >
            详情
          </view>
        </view>
      </view>
      
      <!-- 加载更多提示 -->
      <view class="loading-more" v-if="pagination.hasMore">
        <text class="loading-text">加载更多...</text>
      </view>
      <view class="no-more" v-else>
        <text class="no-more-text">没有更多数据了</text>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" v-else-if="!loading">
      <text class="empty-icon">📭</text>
      <text class="empty-text">暂无用户数据</text>
    </view>
    
    <!-- 加载中 -->
    <view class="loading-container" v-if="loading && userList.length === 0">
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 用户详情弹窗 -->
    <view class="user-detail-modal" v-if="showUserDetail" @click="closeUserDetail">
      <view class="detail-content" @click.stop>
        <view class="detail-header">
          <text class="detail-title">用户详情</text>
          <text class="close-icon" @click="closeUserDetail">✕</text>
        </view>
        
        <view class="detail-body" v-if="currentUser">
          <view class="detail-avatar">
            <text class="avatar-text">{{ currentUser.username?.substring(0, 1) || '用' }}</text>
          </view>
          
          <view class="detail-info">
            <view class="info-item">
              <text class="info-label">用户ID</text>
              <text class="info-value">{{ currentUser.id }}</text>
            </view>
            
            <view class="info-item">
              <text class="info-label">用户名</text>
              <text class="info-value">{{ currentUser.username || '-' }}</text>
            </view>
            
            <view class="info-item">
              <text class="info-label">手机号</text>
              <text class="info-value">{{ currentUser.phone || '-' }}</text>
            </view>
            
            <view class="info-item">
              <text class="info-label">状态</text>
              <view class="info-value">
                <view :class="['status-tag', currentUser.status === 1 ? 'status-active' : 'status-disabled']">
                  {{ currentUser.status === 1 ? '正常' : '禁用' }}
                </view>
              </view>
            </view>
            
            <view class="info-item">
              <text class="info-label">地址</text>
              <text class="info-value">{{ currentUser.address || '-' }}</text>
            </view>
            
            <view class="info-item">
              <text class="info-label">注册时间</text>
              <text class="info-value">{{ formatDate(currentUser.createTime) }}</text>
            </view>
            
            <view class="info-item">
              <text class="info-label">更新时间</text>
              <text class="info-value">{{ formatDate(currentUser.updateTime) }}</text>
            </view>
            
            <!-- 用户来源渠道 -->
            <view class="info-item" v-if="currentUser.source !== undefined">
              <text class="info-label">来源渠道</text>
              <text class="info-value">{{ 
                currentUser.source === 1 ? '小程序' : 
                currentUser.source === 2 ? 'APP' : 
                currentUser.source === 3 ? '公众号' : '其他'
              }}</text>
            </view>
            
            <!-- 微信OpenID -->
            <view class="info-item" v-if="currentUser.openid">
              <text class="info-label">微信OpenID</text>
              <text class="info-value ellipsis-text">{{ currentUser.openid }}</text>
            </view>
          </view>
        </view>
        
        <view class="detail-footer">
          <view class="footer-button" @click="closeUserDetail">关闭</view>
        </view>
      </view>
    </view>
  </view>
</template>

<style>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

.header {
  height: 96rpx;
  margin-top: 160rpx; /* 为返回按钮留出空间 */
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
  position: sticky;
  top: 0;
  z-index: 100;
  border-bottom: 1px solid #f0f0f0;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

.placeholder {
  width: 60rpx;
}

.search-container {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.search-box {
  flex: 1;
  height: 72rpx;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  margin-right: 20rpx;
}

.search-icon {
  font-size: 28rpx;
  color: #999;
  margin-right: 12rpx;
}

.search-input {
  flex: 1;
  height: 72rpx;
  font-size: 28rpx;
  color: #333;
}

.clear-icon {
  font-size: 28rpx;
  color: #999;
  padding: 10rpx;
}

.search-button {
  font-size: 28rpx;
  color: #409eff;
}

.user-list {
  padding: 24rpx;
}

.user-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.user-info {
  display: flex;
  margin-bottom: 24rpx;
}

.user-avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  background-color: #409eff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.avatar-text {
  font-size: 40rpx;
  color: #ffffff;
  font-weight: bold;
}

.user-detail {
  flex: 1;
}

.user-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.user-name {
  font-size: 32rpx;
  color: #333;
  margin-right: 16rpx;
}

.status-tag {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.status-active {
  background-color: #e8f5e9;
  color: #4caf50;
}

.status-disabled {
  background-color: #ffebee;
  color: #f44336;
}

.user-phone {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.user-date {
  font-size: 24rpx;
  color: #999;
}

.user-actions {
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #f0f0f0;
  padding-top: 24rpx;
}

.action-button {
  font-size: 28rpx;
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
}

.disable-button {
  background-color: #ffebee;
  color: #f44336;
}

.enable-button {
  background-color: #e8f5e9;
  color: #4caf50;
}

.detail-button {
  background-color: #e8f4fd;
  color: #409eff;
  margin-left: 20rpx;
}

.loading-more, .no-more {
  text-align: center;
  padding: 32rpx 0;
}

.loading-text, .no-more-text {
  font-size: 28rpx;
  color: #999;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.loading-container {
  padding: 80rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.user-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.detail-content {
  background-color: #fff;
  border-radius: 16rpx;
  width: 85%;
  max-width: 650rpx;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-bottom: 1px solid #f0f0f0;
}

.detail-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.close-icon {
  font-size: 32rpx;
  color: #999;
  padding: 10rpx;
}

.detail-body {
  padding: 24rpx;
  flex: 1;
  overflow-y: auto;
}

.detail-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background-color: #409eff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 32rpx;
}

.detail-avatar .avatar-text {
  font-size: 48rpx;
  color: #ffffff;
  font-weight: bold;
}

.detail-info {
  width: 100%;
}

.info-item {
  display: flex;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1px solid #f5f5f5;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}

.detail-footer {
  padding: 24rpx;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
}

.footer-button {
  font-size: 28rpx;
  background-color: #409eff;
  color: #ffffff;
  padding: 12rpx 36rpx;
  border-radius: 8rpx;
}

.ellipsis-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}
</style> 