import { createSSRApp } from "vue";
import App from "./App.vue";
import store from './store';
import PuzzleVerify from './components/common/PuzzleVerify.vue'
import VerifyCodeButton from './components/common/VerifyCodeButton.vue'
import SlideVerify from './components/common/SlideVerify.vue'
import { logConfigStatus } from './utils/configValidator';

export function createApp() {
  const app = createSSRApp(App);
  app.use(store);
  app.component('PuzzleVerify', PuzzleVerify)
  app.component('VerifyCodeButton', VerifyCodeButton)
  app.component('SlideVerify', SlideVerify)

  // 在开发环境下检查并输出配置状态
  logConfigStatus();

  return {
    app,
  };
}
