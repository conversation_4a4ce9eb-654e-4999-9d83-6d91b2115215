// 导航工具函数
export const navigationUtils = {
  // 智能导航函数，自动判断页面类型并使用正确的导航方法
  navigateTo(url: string, options: { fail?: (err: any) => void } = {}) {
    // tabBar页面列表
    const tabBarPages = [
      '/pages/index/index',
      '/pages/user/order-list', 
      '/pages/user/home'
    ];
    
    const isTabBarPage = tabBarPages.includes(url);
    
    if (isTabBarPage) {
      // 使用switchTab跳转tabBar页面
      uni.switchTab({
        url,
        fail: (err) => {
          console.error('switchTab失败:', err);
          if (options.fail) {
            options.fail(err);
          } else {
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        }
      });
    } else {
      // 使用navigateTo跳转普通页面
      uni.navigateTo({
        url,
        fail: (err) => {
          console.error('navigateTo失败:', err);
          // 尝试redirectTo
          uni.redirectTo({
            url,
            fail: (redirectErr) => {
              console.error('redirectTo也失败:', redirectErr);
              if (options.fail) {
                options.fail(redirectErr);
              } else {
                uni.showToast({
                  title: '页面跳转失败',
                  icon: 'none'
                });
              }
            }
          });
        }
      });
    }
  },
  
  // 强制重新加载页面
  reLaunch(url: string, options: { fail?: (err: any) => void } = {}) {
    uni.reLaunch({
      url,
      fail: (err) => {
        console.error('reLaunch失败:', err);
        if (options.fail) {
          options.fail(err);
        } else {
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      }
    });
  }
};
