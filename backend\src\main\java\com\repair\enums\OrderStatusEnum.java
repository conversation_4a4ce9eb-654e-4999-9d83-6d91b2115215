package com.repair.enums;

import lombok.Getter;

/**
 * 订单状态枚举
 */
@Getter
public enum OrderStatusEnum {

    PENDING(0, "待处理"),
    ACCEPTED(1, "已接单"),
    PROCESSING(2, "处理中"),
    COMPLETED(3, "已完成"),
    CANCELED(4, "已取消");

    private final Integer code;
    private final String desc;

    OrderStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据状态码获取枚举
     * @param code 状态码
     * @return 枚举实例
     */
    public static OrderStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OrderStatusEnum status : OrderStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据状态码获取描述
     * @param code 状态码
     * @return 状态描述
     */
    public static String getDescByCode(Integer code) {
        OrderStatusEnum status = getByCode(code);
        return status != null ? status.getDesc() : "未知状态";
    }
} 