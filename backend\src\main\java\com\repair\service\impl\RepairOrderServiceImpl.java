package com.repair.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.repair.entity.RepairOrder;
import com.repair.mapper.RepairOrderMapper;
import com.repair.service.RepairOrderService;
import com.repair.vo.OrderDetailVO;
import com.repair.exception.BusinessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.time.LocalDateTime;

@Service
public class RepairOrderServiceImpl extends ServiceImpl<RepairOrderMapper, RepairOrder> implements RepairOrderService {

    @Override
    public OrderDetailVO getOrderDetail(Long orderId, Long repairerId) {
        RepairOrder order = getById(orderId);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        
        // 检查是否是该维修师的订单
        if (order.getRepairerId() != null && !order.getRepairerId().equals(repairerId)) {
            throw new BusinessException("无权查看该订单");
        }
        
        // 转换为VO对象
        OrderDetailVO vo = new OrderDetailVO();
        vo.setId(order.getId());
        vo.setOrderId(order.getOrderId());
        vo.setUserId(order.getUserId());
        vo.setRepairerId(order.getRepairerId());
        vo.setDescription(order.getDescription());
        vo.setContactName(order.getContactName());
        vo.setContactPhone(order.getContactPhone());
        // 地址字段已移除，使用结构化地址
        // vo.setAddress(order.getAddress());
        vo.setStatus(order.getStatus());
        vo.setUrgent(order.getUrgent());
        vo.setRating(order.getRating());
        vo.setComment(order.getComment());
        vo.setAppointmentTime(order.getAppointmentTime());
        vo.setCreateTime(order.getCreateTime());
        vo.setUpdateTime(order.getUpdateTime());
        vo.setCompleteTime(order.getCompleteTime());
        
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void acceptOrder(Long orderId, Long repairerId) {
        RepairOrder order = getById(orderId);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        
        if (order.getStatus() != 0) {
            throw new BusinessException("订单状态不正确");
        }
        
        // 更新订单状态为已接单
        order.setStatus(1);
        order.setRepairerId(repairerId);
        order.setUpdateTime(LocalDateTime.now());
        
        if (!updateById(order)) {
            throw new BusinessException("接单失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelOrder(Long orderId, Long repairerId) {
        RepairOrder order = getById(orderId);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        
        // 检查是否是该维修师的订单
        if (!order.getRepairerId().equals(repairerId)) {
            throw new BusinessException("无权操作该订单");
        }
        
        if (order.getStatus() != 1 && order.getStatus() != 2) {
            throw new BusinessException("订单状态不正确");
        }
        
        // 更新订单状态为待处理
        order.setStatus(0);
        order.setRepairerId(null);
        order.setUpdateTime(LocalDateTime.now());
        
        if (!updateById(order)) {
            throw new BusinessException("取消接单失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processOrder(Long orderId, Long repairerId) {
        RepairOrder order = getById(orderId);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        
        // 检查是否是该维修师的订单
        if (!order.getRepairerId().equals(repairerId)) {
            throw new BusinessException("无权操作该订单");
        }
        
        if (order.getStatus() != 1) {
            throw new BusinessException("订单状态不正确");
        }
        
        // 更新订单状态为处理中
        order.setStatus(2);
        order.setUpdateTime(LocalDateTime.now());
        
        if (!updateById(order)) {
            throw new BusinessException("开始处理失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void completeOrder(Long orderId, Long repairerId) {
        RepairOrder order = getById(orderId);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        
        // 检查是否是该维修师的订单
        if (!order.getRepairerId().equals(repairerId)) {
            throw new BusinessException("无权操作该订单");
        }
        
        if (order.getStatus() != 2) {
            throw new BusinessException("订单状态不正确");
        }
        
        // 更新订单状态为已完成
        order.setStatus(3);
        order.setUpdateTime(LocalDateTime.now());
        order.setCompleteTime(LocalDateTime.now());
        
        if (!updateById(order)) {
            throw new BusinessException("完成订单失败");
        }
    }
} 