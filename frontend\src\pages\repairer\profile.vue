<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRepairerStore } from '@/store/repairer';
import { repairerApi, type RepairerInfo } from '@/api/repairer';
import CommonBackButton from '@/components/common/BackButton.vue';

const repairerStore = useRepairerStore();
const submitting = ref(false);
const loading = ref(true);

// 表单数据
const formData = reactive({
  username: '',
  phone: '',
  skillTags: ''
});

// 页面加载时检查登录状态并获取信息
onMounted(() => {
  // 验证登录状态
  if (!repairerStore.token) {
    uni.redirectTo({ url: './login' });
    return;
  }
  
  // 加载维修师信息
  fetchRepairerInfo();
});

// 获取维修师信息
async function fetchRepairerInfo() {
  try {
    loading.value = true;
    const response = await repairerApi.getRepairerInfo();
    
    // 填充表单数据
    if (response && response.data) {
      const repairerInfo = response.data;
      formData.username = repairerInfo.username;
      formData.phone = repairerInfo.phone;
      formData.skillTags = repairerInfo.skillTags;
    }
  } catch (error) {
    console.error('获取维修师信息失败', error);
    uni.showToast({
      title: '获取信息失败',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
}

// 提交表单
async function submitForm() {
  // 表单验证
  if (!formData.username) {
    uni.showToast({
      title: '请输入用户名',
      icon: 'none'
    });
    return;
  }
  
  // 技能标签处理 - 同时处理中文和英文逗号，去除空格
  if (formData.skillTags) {
    formData.skillTags = formData.skillTags
      .replace(/，/g, ',') // 先将中文逗号替换为英文逗号
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag)
      .join(',');
  }
  
  try {
    submitting.value = true;
    
    // 调用API更新维修师信息
    const response = await repairerApi.updateRepairerInfo({
      username: formData.username,
      skillTags: formData.skillTags
    });
    
    // 更新本地存储
    if (response && response.data) {
      repairerStore.setRepairerInfo(response.data);
    }
    
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    });
    
    // 延迟返回上一页
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  } catch (error) {
    console.error('更新信息失败', error);
    uni.showToast({
      title: '保存失败，请稍后重试',
      icon: 'none'
    });
  } finally {
    submitting.value = false;
  }
}
</script>

<template>
  <view class="container">
    <!-- 使用统一的返回按钮组件 -->
    <common-back-button></common-back-button>
    
    <!-- 顶部标题栏 -->
    <view class="header">
      <text class="title">个人信息</text>
    </view>
    
    <!-- 加载中 -->
    <view class="loading-container" v-if="loading">
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 个人信息表单 -->
    <view class="form-container" v-else>
      <view class="form-group">
        <view class="form-item">
          <text class="form-label">用户名</text>
          <input 
            class="form-input" 
            type="text" 
            v-model="formData.username" 
            placeholder="请输入用户名"
          />
        </view>
        
        <view class="form-item">
          <text class="form-label">手机号码</text>
          <input 
            class="form-input" 
            type="text" 
            v-model="formData.phone" 
            disabled
            placeholder="手机号码"
          />
          <text class="form-tip">手机号码不可修改</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">技能标签</text>
          <input 
            class="form-input" 
            type="text" 
            v-model="formData.skillTags" 
            placeholder="请输入技能标签，用逗号分隔"
          />
          <text class="form-tip">例如：水电维修,家具安装,管道疏通</text>
        </view>
      </view>
      
      <!-- 提交按钮 -->
      <view class="form-actions">
        <button 
          class="action-button primary" 
          @click="submitForm" 
          :disabled="submitting"
        >
          {{ submitting ? '保存中...' : '保存修改' }}
        </button>
      </view>
    </view>
  </view>
</template>

<style>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

.header {
  position: relative;
  height: 96rpx;
  margin-top: 160rpx; /* 为返回按钮留出空间 */
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #f0f0f0;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 200rpx;
}

.loading-text {
  font-size: 32rpx;
  color: #999;
}

.form-container {
  padding: 32rpx 24rpx;
}

.form-group {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
}

.form-item {
  padding: 24rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}

.form-input {
  font-size: 32rpx;
  color: #333;
  width: 100%;
  height: 72rpx;
}

.form-input[disabled] {
  color: #999;
}

.form-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
  display: block;
}

.form-actions {
  display: flex;
  justify-content: center;
}

.action-button {
  width: 100%;
  height: 88rpx;
  font-size: 32rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 8rpx;
  margin: 0;
}

.primary {
  background-color: #ff6b00;
  color: #ffffff;
}

.action-button[disabled] {
  background-color: #ffc08f;
}
</style> 