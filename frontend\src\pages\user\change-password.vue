<template>
  <view class="container">
    <!-- 引入统一的返回按钮组件 -->
    <common-back-button></common-back-button>

    <!-- 页面内容 -->
    <view class="content">
      <!-- 页面标题 -->
      <view class="header">
        <text class="title">修改密码</text>
      </view>

      <!-- 表单区域 -->
      <view class="form-container">
        <!-- 新密码 -->
        <view class="form-item">
          <text class="label">新密码<text class="required">*</text></text>
          <password-input
            v-model="formData.newPassword"
            placeholder="请输入6-20位新密码"
            placeholder-class="placeholder"
          />
        </view>

        <!-- 确认密码 -->
        <view class="form-item">
          <text class="label">确认密码<text class="required">*</text></text>
          <password-input
            v-model="confirmPassword"
            placeholder="请再次输入新密码"
            placeholder-class="placeholder"
          />
        </view>

        <!-- 提交按钮 -->
        <button
          class="submit-btn"
          :class="{'btn-loading': loading}"
          :disabled="loading"
          @click="changePassword"
        >
          <text v-if="loading">提交中...</text>
          <text v-else>确认修改</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { userApi } from '@/api/user';
import CommonBackButton from '@/components/common/BackButton.vue';
import PasswordInput from '@/components/common/PasswordInput.vue';

const loading = ref(false);
const confirmPassword = ref('');

// 表单数据
const formData = reactive({
  newPassword: ''
});

// 表单验证
function validateForm(): boolean {
  if (!formData.newPassword) {
    uni.showToast({
      title: '请输入新密码',
      icon: 'none'
    });
    return false;
  }
  
  if (formData.newPassword.length < 6 || formData.newPassword.length > 20) {
    uni.showToast({
      title: '新密码长度应在6-20位之间',
      icon: 'none'
    });
    return false;
  }
  
  if (formData.newPassword !== confirmPassword.value) {
    uni.showToast({
      title: '两次密码输入不一致',
      icon: 'none'
    });
    return false;
  }
  
  return true;
}

// 修改密码
async function changePassword() {
  if (!validateForm()) {
    return;
  }

  loading.value = true;
  try {
    await userApi.changePassword({
      newPassword: formData.newPassword
    });

    uni.showToast({
      title: '密码修改成功',
      icon: 'success',
      duration: 1500
    });

    // 1.5秒后返回上一页
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  } catch (error: any) {
    console.error('修改密码失败:', error);
    uni.showToast({
      title: error.message || '修改失败，请重试',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
}
</script>

<style>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

.content {
  padding-top: 20rpx;
}

.header {
  position: relative;
  height: 96rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #f0f0f0;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

.form-container {
  margin: 24rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
}

.form-item {
  margin-bottom: 32rpx;
}

.label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.required {
  color: #ff4d4f;
  margin-left: 4rpx;
}

.input-wrapper {
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;
  overflow: hidden;
}

.input {
  height: 88rpx;
  font-size: 28rpx;
  color: #333;
  width: 100%;
  padding: 0 20rpx;
  box-sizing: border-box;
}

.placeholder {
  color: #c0c0c0;
}

.submit-btn {
  margin-top: 40rpx;
  height: 88rpx;
  line-height: 88rpx;
  background-color: #07C160;
  color: #ffffff;
  font-size: 32rpx;
  border-radius: 44rpx;
  text-align: center;
}

.btn-loading {
  opacity: 0.7;
}
</style> 