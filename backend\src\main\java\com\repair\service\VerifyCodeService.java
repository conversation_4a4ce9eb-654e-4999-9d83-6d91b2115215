package com.repair.service;

/**
 * 验证码服务接口
 * 统一处理验证码生成、发送、校验等通用逻辑
 */
public interface VerifyCodeService {
    
    /**
     * 生成验证码
     * @param length 验证码长度
     * @return 生成的验证码
     */
    String generateVerifyCode(int length);
    
    /**
     * 发送验证码
     * @param phone 手机号
     * @param userType 用户类型: user(普通用户), repairer(维修师), admin(管理员)
     * @return 生成的验证码(用于测试环境或开发调试)
     */
    String sendVerifyCode(String phone, String userType);
    
    /**
     * 验证验证码是否正确
     * @param phone 手机号
     * @param verifyCode 验证码
     * @return 是否正确
     */
    boolean validateVerifyCode(String phone, String verifyCode);
    
    /**
     * 验证码使用后删除
     * @param phone 手机号
     */
    void deleteVerifyCode(String phone);
    
    /**
     * 检查用户手机号是否存在
     * @param phone 手机号
     * @param userType 用户类型: user(普通用户), repairer(维修师), admin(管理员)
     * @return 是否存在
     */
    boolean checkPhoneExist(String phone, String userType);
} 