{"compilerOptions": {"target": "esnext", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "lib": ["esnext", "dom"], "types": ["@dcloudio/types"], "verbatimModuleSyntax": true, "ignoreDeprecations": "5.0"}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"]}