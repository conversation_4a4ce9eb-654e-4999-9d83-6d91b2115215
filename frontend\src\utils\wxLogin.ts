/**
 * 微信小程序登录工具函数
 */
import { request } from './request';

// 设置API基础URL
let WX_API_BASE = '';

// #ifdef MP-WEIXIN
WX_API_BASE = 'http://127.0.0.1:18080'; 
// #endif

// 定义响应数据类型
interface WxLoginResponseData {
  token?: string;
  userInfo?: any;
  isNewUser?: boolean;
  needBindPhone?: boolean;
  sessionKey?: string;
  openid?: string;
}

// 定义API响应类型
interface ApiResponse<T> {
  data: T;
  code: number;
  message?: string;
}

/**
 * 获取微信登录状态
 * @returns Promise resolving to LoginRes
 */
export function getWxLoginStatus(): Promise<UniApp.LoginRes> {
  return new Promise((resolve, reject) => {
    uni.login({
      provider: 'weixin',
      success: (res) => {
        console.log('微信登录成功:', res);
        resolve(res);
      },
      fail: (err) => {
        console.error('微信登录失败:', err);
        reject(err);
      }
    });
  });
}

/**
 * 获取用户微信信息
 * @returns Promise resolving to UserInfo
 */
export function getWxUserInfo(): Promise<UniApp.GetUserInfoRes> {
  return new Promise((resolve, reject) => {
    uni.getUserInfo({
      provider: 'weixin',
      success: (res) => {
        console.log('获取用户信息成功:', res);
        resolve(res);
      },
      fail: (err) => {
        console.error('获取用户信息失败:', err);
        reject(err);
      }
    });
  });
}

/**
 * 获取微信小程序加密手机号信息
 * 仅在用户点击授权手机号按钮后可以获取
 * @returns Promise<string> 获取手机号的临时登录凭证
 */
export function getWxPhoneNumber(): Promise<string> {
  return new Promise((resolve, reject) => {
    // #ifdef MP-WEIXIN
    // 由于uni-app类型定义不完整，这里使用any类型
    // @ts-ignore
    uni.getPhoneNumber({
      success: (res: any) => {
        console.log('获取手机号成功', res);
        resolve(res.code); // 微信小程序返回临时登录凭证code
      },
      fail: (err: any) => {
        console.error('获取手机号失败', err);
        reject(err);
      }
    });
    // #endif
    
    // #ifndef MP-WEIXIN
    reject(new Error('当前环境不支持获取微信手机号'));
    // #endif
  });
}

/**
 * 使用微信小程序登录
 * @param code 微信登录凭证
 * @returns 后端接口返回的数据
 */
export function wxMiniLogin(code: string) {
  // 调用真实的后端登录接口
  console.log('微信登录请求，code:', code);
  
  // 确保请求URL正确设置
  const url = '/api/user/wxLogin';
  console.log('请求URL:', url);
  
  return request({
    url,
    method: 'POST',
    data: { code },
    auth: false, // 不需要认证头
    timeout: 30000 // 30秒超时
  });
}

/**
 * 判断当前环境是否为微信小程序
 * @returns 是否为微信小程序
 */
export function isWxMiniProgram(): boolean {
  // #ifdef MP-WEIXIN
  return true;
  // #endif
  
  // #ifndef MP-WEIXIN
  return false;
  // #endif
}

/**
 * 获取微信小程序用户信息
 * @returns Promise<{userInfo, isNewUser, needBindPhone}> 用户信息和状态标志
 */
export async function getWxMiniUserInfo() {
  try {
    // 1. 获取微信登录凭证
    const loginRes = await getWxLoginStatus();
    if (!loginRes.code) {
      throw new Error('获取微信登录凭证失败');
    }

    console.log('获取到微信登录凭证:', loginRes.code);

    // 2. 获取微信用户信息
    let wxUserInfo = null;
    
    try {
      const userInfoRes = await getWxUserInfo();
      wxUserInfo = userInfoRes.userInfo;
      console.log('获取到微信用户信息:', wxUserInfo?.nickName);
    } catch (e) {
      console.warn('获取微信用户信息失败，将使用默认值', e);
    }

    // 3. 调用后端登录API
    console.log('开始调用后端登录API...');
    
    // 显示登录中的提示
    uni.showLoading({
      title: '登录中...',
      mask: true
    });
    
    try {
      // 使用封装的request函数发送请求
      const response = await request({
        url: '/api/user/wxLogin',
        method: 'POST',
        data: { 
          code: loginRes.code,
          userInfo: wxUserInfo // 可选，如果后端需要微信用户信息
        },
        auth: false, // 不需要认证头
        timeout: 30000 // 30秒超时
      });
      
      // 隐藏加载提示
      uni.hideLoading();
      
      if (!response?.data) {
        throw new Error('登录失败：服务器响应异常');
      }
      
      // 获取响应数据，使用类型断言
      const responseData = response.data as any;
      
      // 确保先清理旧的登录状态
      uni.removeStorageSync('token');
      uni.removeStorageSync('userInfo');
      uni.removeStorageSync('wxLoginResult');
      
      // 保存微信登录结果到storage
      const loginResult = {
        code: loginRes.code,
        userInfo: wxUserInfo,
        openid: responseData.openid,
        session_key: responseData.sessionKey
      };
      
      uni.setStorageSync('wxLoginResult', loginResult);
      
      // 如果返回了token和用户信息
      if (responseData.token) {
        // 保存新的token，注意不要添加额外的空格
        const token = responseData.token.trim();
        
        return {
          token,
          userInfo: responseData.userInfo,
          isNewUser: responseData.isNewUser || false,
          needBindPhone: responseData.needBindPhone || false,
          sessionKey: responseData.sessionKey,
          openid: responseData.openid
        };
      } else if (responseData.needBindPhone) {
        // 如果需要绑定手机号但没有返回token
        return {
          token: null,
          userInfo: null,
          isNewUser: responseData.isNewUser || true,
          needBindPhone: true,
          sessionKey: responseData.sessionKey,
          openid: responseData.openid
        };
      } else {
        throw new Error('登录失败：服务器响应不完整');
      }
    } catch (error: any) {
      uni.hideLoading();
      console.error('后端登录API调用失败:', error);
      
      // 清理所有登录相关的存储
      uni.removeStorageSync('token');
      uni.removeStorageSync('userInfo');
      uni.removeStorageSync('wxLoginResult');
      
      const errorMessage = error instanceof Error 
        ? error.message 
        : getErrorMessage(error?.data) || '登录失败，请重试';
      
      uni.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 2000
      });
      
      throw new Error(errorMessage);
    }
  } catch (error) {
    uni.hideLoading();
    console.error('微信登录流程失败:', error);
    
    // 清理所有登录相关的存储
    uni.removeStorageSync('token');
    uni.removeStorageSync('userInfo');
    uni.removeStorageSync('wxLoginResult');
    
    throw error;
  }
}

/**
 * 从响应中提取错误信息
 */
function getErrorMessage(data: any): string {
  if (!data) return '未知错误';
  
  if (typeof data === 'string') {
    return data;
  }
  
  if (data.message) {
    return data.message;
  }
  
  if (data.msg) {
    return data.msg;
  }
  
  if (data.error) {
    return data.error;
  }
  
  return '服务器响应异常';
} 