// 封装uni-app的常用API，提供更简洁的调用方式

/**
 * 显示消息提示框
 * @param title 提示的内容
 * @param icon 图标类型，默认none
 * @param duration 提示的延迟时间，默认1500ms
 */
export function showToast(title: string, icon: 'success' | 'loading' | 'error' | 'none' = 'none', duration: number = 1500): void {
  uni.showToast({
    title,
    icon,
    duration
  });
}

/**
 * 显示加载中提示框
 * @param title 提示的内容，默认为"加载中"
 * @param mask 是否显示透明蒙层，防止触摸穿透，默认为true
 */
export function showLoading(title: string = '加载中', mask: boolean = true): void {
  uni.showLoading({
    title,
    mask
  });
}

/**
 * 隐藏加载中提示框
 */
export function hideLoading(): void {
  uni.hideLoading();
}

/**
 * 显示模态对话框
 * @param content 提示的内容
 * @param title 提示的标题，默认为"提示"
 * @param showCancel 是否显示取消按钮，默认为true
 * @returns Promise，确认返回true，取消返回false
 */
export function showModal(content: string, title: string = '提示', showCancel: boolean = true): Promise<boolean> {
  return new Promise((resolve) => {
    uni.showModal({
      title,
      content,
      showCancel,
      success: (res) => {
        if (res.confirm) {
          resolve(true);
        } else if (res.cancel) {
          resolve(false);
        }
      }
    });
  });
}

/**
 * 保存数据到本地存储
 * @param key 键名
 * @param data 需要存储的数据
 */
export function setStorageSync(key: string, data: any): void {
  try {
    uni.setStorageSync(key, data);
  } catch (e) {
    console.error(`存储数据失败: ${key}`, e);
  }
}

/**
 * 从本地存储中获取数据
 * @param key 键名
 * @returns 存储的数据，不存在则返回null
 */
export function getStorageSync<T>(key: string): T | null {
  try {
    const value = uni.getStorageSync(key);
    return value ? value : null;
  } catch (e) {
    console.error(`获取存储数据失败: ${key}`, e);
    return null;
  }
}

/**
 * 从本地存储中移除数据
 * @param key 键名
 */
export function removeStorageSync(key: string): void {
  try {
    uni.removeStorageSync(key);
  } catch (e) {
    console.error(`移除存储数据失败: ${key}`, e);
  }
}

/**
 * 获取系统信息
 * @returns Promise 包含系统信息的Promise
 */
export function getSystemInfo(): Promise<UniApp.GetSystemInfoResult> {
  return new Promise((resolve, reject) => {
    uni.getSystemInfo({
      success: (result) => {
        resolve(result);
      },
      fail: (error) => {
        reject(error);
      }
    });
  });
}

/**
 * 获取当前的位置
 * @param type 获取位置的类型，默认为wgs84
 * @returns Promise 包含位置信息的Promise
 */
export function getLocation(type: 'wgs84' | 'gcj02' = 'wgs84'): Promise<UniApp.GetLocationSuccess> {
  return new Promise((resolve, reject) => {
    uni.getLocation({
      type,
      success: (result) => {
        resolve(result);
      },
      fail: (error) => {
        reject(error);
      }
    });
  });
}

/**
 * 选择图片
 * @param count 最多可以选择的图片张数，默认9
 * @param sizeType 所选的图片的尺寸，默认['original', 'compressed']
 * @param sourceType 选择图片的来源，默认['album', 'camera']
 * @returns Promise 包含选择的图片文件路径的Promise
 */
export function chooseImage(
  count: number = 9,
  sizeType: Array<'original' | 'compressed'> = ['original', 'compressed'],
  sourceType: Array<'album' | 'camera'> = ['album', 'camera']
): Promise<UniApp.ChooseImageSuccessCallbackResult> {
  return new Promise((resolve, reject) => {
    uni.chooseImage({
      count,
      sizeType,
      sourceType,
      success: (result) => {
        resolve(result);
      },
      fail: (error) => {
        reject(error);
      }
    });
  });
}

/**
 * 上传文件
 * @param url 开发者服务器地址
 * @param filePath 要上传文件资源的路径
 * @param name 文件对应的key，开发者在服务端可以通过这个key获取文件的二进制内容
 * @param formData HTTP请求中其他额外的form data
 * @param header HTTP请求Header
 * @returns Promise 包含上传结果的Promise
 */
export function uploadFile(
  url: string,
  filePath: string,
  name: string,
  formData?: Record<string, any>,
  header?: Record<string, string>
): Promise<UniApp.UploadFileSuccessCallbackResult> {
  return new Promise((resolve, reject) => {
    const uploadTask = uni.uploadFile({
      url,
      filePath,
      name,
      formData,
      header,
      success: (result) => {
        resolve(result);
      },
      fail: (error) => {
        reject(error);
      }
    });
    
    // 返回上传进度
    uploadTask.onProgressUpdate((res) => {
      console.log('上传进度', res.progress);
    });
  });
}

/**
 * 复制内容到剪贴板
 * @param data 需要复制的内容
 * @param showToast 是否显示提示，默认为true
 * @returns Promise
 */
export function copyToClipboard(data: string, showToast: boolean = true): Promise<void> {
  return new Promise((resolve, reject) => {
    uni.setClipboardData({
      data,
      success: () => {
        if (showToast) {
          uni.showToast({
            title: '复制成功',
            icon: 'success',
            duration: 1500
          });
        }
        resolve();
      },
      fail: (error) => {
        reject(error);
      }
    });
  });
}

/**
 * 从剪贴板获取内容
 * @returns Promise 包含剪贴板内容的Promise
 */
export function getClipboardData(): Promise<string> {
  return new Promise((resolve, reject) => {
    uni.getClipboardData({
      success: (res) => {
        resolve(res.data);
      },
      fail: (error) => {
        reject(error);
      }
    });
  });
}

/**
 * 打电话
 * @param phoneNumber 需要拨打的电话号码
 */
export function makePhoneCall(phoneNumber: string): void {
  uni.makePhoneCall({
    phoneNumber
  });
}

/**
 * 调用扫码功能
 * @returns Promise 包含扫码结果的Promise
 */
export function scanCode(): Promise<UniApp.ScanCodeSuccessRes> {
  return new Promise((resolve, reject) => {
    uni.scanCode({
      success: (result) => {
        resolve(result);
      },
      fail: (error) => {
        reject(error);
      }
    });
  });
} 