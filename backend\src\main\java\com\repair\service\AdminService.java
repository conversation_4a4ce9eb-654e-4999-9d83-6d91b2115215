package com.repair.service;

import com.repair.dto.AdminLoginDTO;
import com.repair.vo.AdminLoginVO;
import com.repair.vo.AdminInfoVO;
import com.repair.vo.PageVO;
import com.repair.vo.UserVO;
import com.repair.vo.RepairerVO;
import com.repair.vo.OrderVO;
import com.repair.dto.RepairerAddDTO;

public interface AdminService {
    /**
     * 管理员登录
     */
    AdminLoginVO login(AdminLoginDTO loginDTO);
    
    /**
     * 获取管理员信息
     */
    AdminInfoVO getAdminInfo();
    
    /**
     * 获取用户列表
     * @param page 页码
     * @param size 每页大小
     * @param keyword 搜索关键词
     * @return 分页数据
     */
    PageVO<UserVO> getUserList(Integer page, Integer size, String keyword);
    
    /**
     * 更新用户状态
     * @param userId 用户ID
     * @param status 状态值
     */
    void updateUserStatus(Long userId, Integer status);
    
    /**
     * 获取维修师列表
     * @param page 页码
     * @param size 每页大小
     * @param keyword 搜索关键词
     * @return 分页数据
     */
    PageVO<RepairerVO> getRepairerList(Integer page, Integer size, String keyword);
    
    /**
     * 更新维修师状态
     * @param repairerId 维修师ID
     * @param status 状态值
     */
    void updateRepairerStatus(Long repairerId, Integer status);
    
    /**
     * 获取订单列表
     * @param page 页码
     * @param size 每页大小
     * @param status 订单状态，-1表示全部
     * @param keyword 搜索关键词
     * @return 分页数据
     */
    PageVO<OrderVO> getOrderList(Integer page, Integer size, Integer status, String keyword);
    
    /**
     * 获取订单详情
     * @param orderId 订单ID
     * @return 订单详情
     */
    OrderVO getOrderDetail(Long orderId);
    
    /**
     * 更新订单状态
     * @param orderId 订单ID
     * @param status 状态值
     */
    void updateOrderStatus(Long orderId, Integer status);
    
    /**
     * 分配订单给维修师
     * @param orderId 订单ID
     * @param repairerId 维修师ID
     */
    void assignOrder(Long orderId, Long repairerId);
    
    /**
     * 获取用户详情
     * @param userId 用户ID
     * @return 用户详情
     */
    UserVO getUserDetail(Long userId);
    
    /**
     * 新增维修师
     * @param repairerAddDTO 维修师新增信息
     * @return 新增的维修师ID
     */
    Long addRepairer(RepairerAddDTO repairerAddDTO);
    
    /**
     * 修改维修师密码
     * @param repairerId 维修师ID
     * @param password 新密码
     */
    void updateRepairerPassword(Long repairerId, String password);
} 