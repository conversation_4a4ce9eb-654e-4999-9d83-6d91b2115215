#! 创建全新的分配订单页面
<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useAdminStore } from '@/store/admin';
import { adminApi } from '@/api/admin';
import CommonBackButton from '@/components/common/BackButton.vue';

// 创建日期格式化函数
function formatDate(date: string | Date | null | undefined): string {
  if (!date) return '-';
  
  const d = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(d.getTime())) return '-';
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}`;
}

// 订单ID
const orderId = ref<string | number>('');
// 订单详情
const orderDetail = ref<any>(null);
// 订单加载状态
const loadingOrder = ref(false);

// 维修师列表
const repairerList = ref<any[]>([]);
// 维修师加载状态
const loadingRepairers = ref(false);
// 分页参数
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
});

// 搜索关键词
const keyword = ref('');
// 选中的维修师ID
const selectedRepairerId = ref<string | number>('');
// 分配状态
const assigning = ref(false);

// 获取订单详情
function getOrderDetail() {
  loadingOrder.value = true;
  
  adminApi.getOrderDetail(orderId.value)
    .then(res => {
      if (res.code === 200) {
        orderDetail.value = res.data;
        // 如果已分配，预选中当前维修师
        if (orderDetail.value.repairerId) {
          selectedRepairerId.value = orderDetail.value.repairerId;
        }
      } else {
        uni.showToast({
          title: res.message || '获取订单详情失败',
          icon: 'none'
        });
      }
    })
    .catch(err => {
      console.error('获取订单详情失败', err);
      uni.showToast({
        title: '获取订单详情失败',
        icon: 'none'
      });
    })
    .finally(() => {
      loadingOrder.value = false;
    });
}

// 获取维修师列表
function getRepairerList() {
  loadingRepairers.value = true;
  
  adminApi.getRepairerList({
    page: pagination.current,
    size: pagination.pageSize,
    keyword: keyword.value
  })
    .then(res => {
      if (res.code === 200) {
        repairerList.value = res.data.records.filter((repairer: any) => repairer.status === 1);
        pagination.total = res.data.total;
      } else {
        uni.showToast({
          title: res.message || '获取维修师列表失败',
          icon: 'none'
        });
      }
    })
    .catch(err => {
      console.error('获取维修师列表失败', err);
      uni.showToast({
        title: '获取维修师列表失败',
        icon: 'none'
      });
    })
    .finally(() => {
      loadingRepairers.value = false;
    });
}

// 分配订单
function assignOrder() {
  if (!selectedRepairerId.value) {
    uni.showToast({
      title: '请选择维修师',
      icon: 'none'
    });
    return;
  }
  
  uni.showModal({
    title: '确认分配',
    content: '确定将该订单分配给选中的维修师吗？',
    success: (res) => {
      if (res.confirm) {
        executeAssign();
      }
    }
  });
}

// 执行分配
function executeAssign() {
  assigning.value = true;
  
  adminApi.assignOrder(orderId.value, selectedRepairerId.value)
    .then(res => {
      if (res.code === 200) {
        uni.showToast({
          title: '分配成功',
          icon: 'success'
        });
        
        // 发送事件通知订单列表页面刷新数据
        uni.$emit('orderUpdated');
        
        // 返回订单列表
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      } else {
        uni.showToast({
          title: res.message || '分配失败',
          icon: 'none'
        });
      }
    })
    .catch(err => {
      console.error('分配订单失败', err);
      uni.showToast({
        title: '分配失败',
        icon: 'none'
      });
    })
    .finally(() => {
      assigning.value = false;
    });
}

// 搜索维修师
function searchRepairers() {
  pagination.current = 1;
  getRepairerList();
}

// 选择维修师
function selectRepairer(repairer: any) {
  selectedRepairerId.value = repairer.id;
}

// 判断是否是管理员
const adminStore = useAdminStore();
function checkAdmin() {
  if (!adminStore.isLoggedIn) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    });
    
    setTimeout(() => {
      uni.redirectTo({
        url: '/pages/admin/login'
      });
    }, 1500);
    
    return false;
  }
  return true;
}

onMounted(() => {
  if (checkAdmin()) {
    // 获取订单ID
    const pages = getCurrentPages();
    const page = pages[pages.length - 1];
    // @ts-ignore - 页面对象类型问题
    const query = page?.options || {};
    
    if (query.id) {
      orderId.value = query.id;
      getOrderDetail();
      getRepairerList();
    } else {
      uni.showToast({
        title: '订单ID不存在',
        icon: 'none'
      });
      
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  }
});
</script>

<template>
  <view class="assign-page">
    <!-- 使用统一的返回按钮组件 -->
    <common-back-button></common-back-button>
    
    <!-- 顶部标题栏 -->
    <view class="page-header">
      <text class="page-title">分配维修师</text>
    </view>
    
    <!-- 订单信息 -->
    <view v-if="loadingOrder" class="loading-container">
      <text class="loading-text">加载订单信息中...</text>
    </view>
    <view v-else-if="orderDetail" class="order-info">
      <view class="order-info-title">订单信息</view>
      <view class="info-grid">
        <view class="info-item full-width">
          <text class="info-label">订单号：</text>
          <text class="info-value">{{ orderDetail.orderId }}</text>
        </view>
        <view class="info-item contact-row">
          <view class="contact-item">
            <text class="info-label">联系人：</text>
            <text class="info-value">{{ orderDetail.contactName }}</text>
          </view>
          <view class="contact-item">
            <text class="info-label">联系电话：</text>
            <text class="info-value">{{ orderDetail.contactPhone }}</text>
          </view>
          <view v-if="orderDetail.urgent" class="urgent-wrapper">
            <text class="urgent-tag">紧急</text>
          </view>
        </view>
        <view class="info-item full-width">
          <text class="info-label">预约时间：</text>
          <text class="info-value">{{ formatDate(orderDetail.appointmentTime) }}</text>
        </view>
        <view class="info-item full-width">
          <text class="info-label">地址：</text>
          <text class="info-value">{{ orderDetail.address }}</text>
        </view>
        <view class="info-item full-width">
          <text class="info-label">维修内容：</text>
          <text class="info-value">{{ orderDetail.description }}</text>
        </view>
      </view>
    </view>
    
    <!-- 维修师选择 -->
    <view class="repairer-selection">
      <view class="section-title">选择维修师</view>
      
      <!-- 搜索框 -->
      <view class="search-box">
        <input 
          type="text" 
          v-model="keyword" 
          placeholder="搜索维修师" 
          class="search-input"
          @confirm="searchRepairers"
        />
        <button class="search-button" @click="searchRepairers">
          <text class="search-icon iconfont icon-search"></text>
          <text class="search-text">搜索</text>
        </button>
      </view>
      
      <!-- 维修师列表 -->
      <view v-if="loadingRepairers" class="loading-container">
        <text class="loading-text">加载维修师列表中...</text>
      </view>
      <view v-else-if="repairerList.length === 0" class="empty-container">
        <text class="empty-text">暂无可分配的维修师</text>
      </view>
      <view v-else class="repairer-list">
        <view 
          v-for="(repairer, index) in repairerList" 
          :key="repairer.id"
          class="repairer-card"
          :class="{ selected: selectedRepairerId == repairer.id }"
          @click="selectRepairer(repairer)"
        >
          <view class="repairer-header">
            <view class="repairer-name">{{ repairer.username }}</view>
            <view class="repairer-phone">{{ repairer.phone }}</view>
          </view>
          <view class="skill-tags">
            <text v-for="(tag, tagIndex) in repairer.skillTags.split(',')" :key="tagIndex" class="skill-tag">
              {{ tag }}
            </text>
          </view>
          <view class="assign-btn" v-if="selectedRepairerId == repairer.id">
            <text class="iconfont icon-check"></text>
          </view>
        </view>
      </view>
      
      <!-- 分页 -->
      <view v-if="repairerList.length > 0" class="pagination">
        <view class="page-info">
          共 {{ pagination.total }} 名维修师，当前 {{ pagination.current }}/{{ Math.ceil(pagination.total / pagination.pageSize) }} 页
        </view>
        <view class="page-controls">
          <button 
            class="page-btn" 
            :disabled="pagination.current <= 1"
            @click="pagination.current--; getRepairerList();"
          >
            上一页
          </button>
          <button 
            class="page-btn" 
            :disabled="pagination.current >= Math.ceil(pagination.total / pagination.pageSize)"
            @click="pagination.current++; getRepairerList();"
          >
            下一页
          </button>
        </view>
      </view>
    </view>
    
    <!-- 确认按钮 -->
    <view class="submit-section">
      <button 
        class="submit-btn" 
        :disabled="assigning || !selectedRepairerId" 
        @click="assignOrder"
      >
        {{ assigning ? '分配中...' : '确认分配' }}
      </button>
    </view>
  </view>
</template>

<style>
.assign-page {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 40rpx;
}

.page-header {
  background-color: #ffffff;
  padding: calc(var(--status-bar-height) + 120rpx) 30rpx 20rpx;
  position: relative;
  border-bottom: 1rpx solid #ebeef5;
}

.page-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  text-align: center;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 20rpx;
  width: 60%;
}

/* 订单信息卡片 */
.order-info {
  background-color: #ffffff;
  margin: 24rpx;
  padding: 24rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.order-info-title {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin: 10rpx 0 20rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.order-info-title::before {
  content: '';
  width: 4rpx;
  height: 20rpx;
  background: #409eff;
  border-radius: 2rpx;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 8rpx;
}

.info-item.full-width {
  width: 100%;
}

.contact-row {
  display: flex;
  align-items: center;
  gap: 24rpx;
  flex-wrap: wrap;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.urgent-wrapper {
  margin-left: auto;
}

.info-label {
  color: #666666;
  font-size: 26rpx;
  min-width: 120rpx;
  flex-shrink: 0;
}

.info-value {
  color: #333333;
  font-size: 26rpx;
  flex: 1;
  word-break: break-all;
}

.urgent-tag {
  padding: 4rpx 16rpx;
  background: linear-gradient(45deg, #ff4d4f, #ff7875);
  color: #ffffff;
  border-radius: 4rpx;
  font-size: 24rpx;
  font-weight: 500;
}

/* 搜索框 */
.search-box {
  margin: 24rpx;
  position: relative;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.search-input {
  flex: 1;
  height: 80rpx;
  background-color: #ffffff;
  border-radius: 40rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.search-button {
  width: 160rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(45deg, #409eff, #1890ff);
  border-radius: 40rpx;
  padding: 0;
  margin: 0;
  box-shadow: 0 4rpx 12rpx rgba(64, 158, 255, 0.2);
}

.search-button:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.search-icon {
  font-size: 32rpx;
  color: #ffffff;
  margin-right: 8rpx;
}

.search-text {
  color: #ffffff;
  font-size: 28rpx;
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
  .assign-page {
    background-color: #1a1a1a;
  }

  .page-header,
  .order-info,
  .repairer-card,
  .search-input {
    background-color: #2a2a2a;
  }

  .page-title,
  .order-info-title,
  .repairer-name {
    color: #e0e0e0;
  }

  .info-label,
  .repairer-phone {
    color: #909399;
  }

  .info-value {
    color: #e0e0e0;
  }

  .skill-tag {
    background-color: rgba(64, 158, 255, 0.1);
  }

  .search-input {
    background-color: #2a2a2a;
    color: #e0e0e0;
    border: 1rpx solid #4c4c4c;
  }
}

/* 加载和空状态 */
.loading-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
  margin: 24rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
}

.loading-text,
.empty-text {
  color: #909399;
  font-size: 28rpx;
}

.repairer-selection {
  margin: 24rpx;
}

.section-title {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin: 20rpx 0;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.section-title::before {
  content: '';
  width: 4rpx;
  height: 20rpx;
  background: #409eff;
  border-radius: 2rpx;
}

/* 维修师列表 */
.repairer-list {
  margin-top: 24rpx;
}

.repairer-card {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.repairer-card.selected {
  background: #f0f7ff;
  border: 2rpx solid #409eff;
}

.repairer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.repairer-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

.repairer-phone {
  font-size: 26rpx;
  color: #666666;
}

.skill-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-top: 12rpx;
}

.skill-tag {
  padding: 4rpx 16rpx;
  background-color: #f0f7ff;
  color: #409eff;
  border-radius: 4rpx;
  font-size: 24rpx;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 24rpx 0;
  padding: 0 24rpx;
}

.page-info {
  font-size: 26rpx;
  color: #666666;
}

.page-controls {
  display: flex;
  gap: 16rpx;
}

.page-btn {
  min-width: 140rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  background: #ffffff;
  color: #409eff;
  border: 2rpx solid #409eff;
  border-radius: 30rpx;
  font-size: 26rpx;
  padding: 0 24rpx;
}

.page-btn:active {
  opacity: 0.8;
}

.page-btn[disabled] {
  background-color: #f5f7fa;
  border-color: #dcdfe6;
  color: #c0c4cc;
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
  .section-title {
    color: #e0e0e0;
  }

  .repairer-card {
    background-color: #2a2a2a;
  }

  .repairer-card.selected {
    background: rgba(64, 158, 255, 0.1);
    border-color: #409eff;
  }

  .repairer-name {
    color: #e0e0e0;
  }

  .repairer-phone {
    color: #909399;
  }

  .skill-tag {
    background-color: rgba(64, 158, 255, 0.1);
  }

  .page-info {
    color: #909399;
  }

  .page-btn {
    background: #2a2a2a;
    border-color: #409eff;
  }

  .page-btn[disabled] {
    background-color: #2a2a2a;
    border-color: #4c4c4c;
    color: #606266;
  }
}

/* 确认按钮 */
.submit-section {
  position: fixed;
  bottom: 40rpx;
  left: 0;
  right: 0;
  padding: 0 40rpx;
  background: transparent;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background: linear-gradient(45deg, #409eff, #1890ff);
  color: #ffffff;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(64, 158, 255, 0.2);
}

.submit-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.submit-btn[disabled] {
  background: #a0cfff;
  opacity: 0.7;
  box-shadow: none;
}
</style> 