package com.repair.service.impl;

import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.dysmsapi20170525.AsyncClient;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendSmsResponse;
import com.repair.config.AliyunSmsProperties;
import com.repair.exception.BusinessException;
import darabonba.core.client.ClientOverrideConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/**
 * 阿里云短信服务
 */
@Service
@Slf4j
public class AliyunSmsService {

    @Autowired
    private AliyunSmsProperties smsProperties;

    /**
     * 发送短信验证码
     *
     * @param phone 手机号
     * @param code  验证码
     * @return 是否发送成功
     */
    public boolean sendSms(String phone, String code) {
        // 如果未启用短信服务，直接返回false
        if (!smsProperties.isEnabled()) {
            log.info("阿里云短信服务未启用，跳过发送短信操作。");
            return false;
        }

        log.info("开始发送短信验证码到 {}, 验证码: {}", phone, code);

        // 配置凭证
        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                .accessKeyId(smsProperties.getAccessKeyId())
                .accessKeySecret(smsProperties.getAccessKeySecret())
                .build());

        // 配置客户端
        AsyncClient client = null;
        try {
            client = AsyncClient.builder()
                    .region(smsProperties.getRegionId())
                    .credentialsProvider(provider)
                    .overrideConfiguration(
                            ClientOverrideConfiguration.create()
                                    .setEndpointOverride(smsProperties.getEndpoint()))
                    .build();

            // 构建请求
            SendSmsRequest sendSmsRequest = SendSmsRequest.builder()
                    .signName(smsProperties.getSignName())
                    .templateCode(smsProperties.getTemplateCode())
                    .phoneNumbers(phone)
                    .templateParam("{\"code\":\"" + code + "\"}")
                    .build();

            // 发送请求
            CompletableFuture<SendSmsResponse> response = client.sendSms(sendSmsRequest);
            SendSmsResponse resp = response.get();

            // 处理响应
            if (resp != null && "OK".equals(resp.getBody().getCode())) {
                log.info("短信发送成功，手机号: {}, 请求ID: {}", phone, resp.getBody().getRequestId());
                return true;
            } else {
                log.error("短信发送失败，手机号: {}, 错误码: {}, 错误信息: {}",
                        phone,
                        resp != null ? resp.getBody().getCode() : "unknown",
                        resp != null ? resp.getBody().getMessage() : "unknown");
                return false;
            }
        } catch (Exception e) {
            log.error("短信发送异常，手机号: {}, 异常信息: {}", phone, e.getMessage(), e);
            throw new BusinessException("短信发送失败: " + e.getMessage());
        } finally {
            // 关闭客户端
            if (client != null) {
                client.close();
            }
        }
    }
}