// 地图导航工具
import type { AddressInfo } from './addressFormatter';
import { formatAddress } from './addressFormatter';
import { geocodeAddress, hasValidCoordinates, type GeocodingResult } from './geocoding';

/**
 * 地址导航选项
 */
export interface NavigationOptions {
  name?: string; // 地点名称
  scale?: number; // 地图缩放级别，默认18
  showLoading?: boolean; // 是否显示加载提示，默认true
}

/**
 * 地图应用信息
 */
export interface MapApp {
  name: string;
  scheme: string;
  icon?: string;
}

/**
 * 常用地图应用列表
 */
export const MAP_APPS: MapApp[] = [
  {
    name: '高德地图',
    scheme: 'amapuri://route/plan/?dlat={lat}&dlon={lon}&dname={name}&dev=0&t=0'
  },
  {
    name: '百度地图', 
    scheme: 'baidumap://map/direction?destination={name}&mode=driving'
  },
  {
    name: '腾讯地图',
    scheme: 'qqmap://map/routeplan?type=drive&to={name}&tocoord={lat},{lon}'
  },
  {
    name: '苹果地图',
    scheme: 'maps://maps.apple.com/?daddr={lat},{lon}'
  }
];

/**
 * 主要的地址导航函数
 * @param addressInfo 地址信息对象
 * @param fallbackAddress 备用地址字符串
 * @param options 导航选项
 */
export async function navigateToAddress(
  addressInfo?: AddressInfo, 
  fallbackAddress?: string,
  options: NavigationOptions = {}
): Promise<void> {
  const { name, scale = 18, showLoading = true } = options;
  
  if (showLoading) {
    uni.showLoading({
      title: '正在打开地图...',
      mask: true
    });
  }

  try {
    // 1. 检查是否已有有效坐标
    if (hasValidCoordinates(addressInfo)) {
      console.log('使用现有坐标打开地图');
      await openLocationWithCoords(
        addressInfo!.latitude!,
        addressInfo!.longitude!,
        name || formatAddress(addressInfo, fallbackAddress),
        formatAddress(addressInfo, fallbackAddress),
        scale
      );
      return;
    }

    // 2. 尝试地理编码
    const address = formatAddress(addressInfo, fallbackAddress);
    if (!address || address === '-') {
      throw new Error('地址信息不完整');
    }

    console.log('开始地理编码:', address);
    const geocodingResult = await geocodeAddress(address);
    
    if (geocodingResult) {
      console.log('地理编码成功，打开地图');
      await openLocationWithCoords(
        geocodingResult.latitude,
        geocodingResult.longitude,
        name || geocodingResult.formattedAddress,
        geocodingResult.formattedAddress,
        scale
      );
    } else {
      // 3. 地理编码失败，降级到地图应用选择
      console.log('地理编码失败，显示地图应用选择');
      await showMapAppSelector(address);
    }
  } catch (error) {
    console.error('地址导航失败:', error);
    
    // 错误处理：显示地图应用选择或错误提示
    const address = formatAddress(addressInfo, fallbackAddress);
    if (address && address !== '-') {
      await showMapAppSelector(address);
    } else {
      uni.showToast({
        title: '地址信息不完整',
        icon: 'none',
        duration: 2000
      });
    }
  } finally {
    if (showLoading) {
      uni.hideLoading();
    }
  }
}

/**
 * 使用坐标打开地图
 */
async function openLocationWithCoords(
  latitude: number,
  longitude: number,
  name: string,
  address: string,
  scale: number = 18
): Promise<void> {
  return new Promise((resolve, reject) => {
    uni.openLocation({
      latitude,
      longitude,
      name,
      address,
      scale,
      success: () => {
        console.log('地图打开成功');
        resolve();
      },
      fail: (error) => {
        console.error('地图打开失败:', error);
        reject(error);
      }
    });
  });
}

/**
 * 显示地图应用选择器
 */
async function showMapAppSelector(address: string): Promise<void> {
  return new Promise((resolve) => {
    const actionSheetItems = MAP_APPS.map(app => app.name);
    
    uni.showActionSheet({
      itemList: actionSheetItems,
      success: (res) => {
        const selectedApp = MAP_APPS[res.tapIndex];
        openMapApp(selectedApp, address);
        resolve();
      },
      fail: () => {
        console.log('用户取消选择地图应用');
        resolve();
      }
    });
  });
}

/**
 * 打开指定的地图应用
 */
function openMapApp(mapApp: MapApp, address: string, lat?: number, lon?: number): void {
  let scheme = mapApp.scheme
    .replace('{name}', encodeURIComponent(address))
    .replace('{lat}', lat?.toString() || '0')
    .replace('{lon}', lon?.toString() || '0');

  console.log('尝试打开地图应用:', mapApp.name, scheme);

  // 尝试打开地图应用
  // #ifdef APP-PLUS
  plus.runtime.openURL(scheme, (error) => {
    console.error('打开地图应用失败:', error);
    uni.showToast({
      title: `请先安装${mapApp.name}`,
      icon: 'none',
      duration: 2000
    });
  });
  // #endif

  // #ifdef H5
  window.open(scheme, '_blank');
  // #endif

  // #ifdef MP
  // 小程序环境下的处理
  uni.showToast({
    title: '请在App中使用此功能',
    icon: 'none',
    duration: 2000
  });
  // #endif
}

/**
 * 快速导航函数（简化版）
 * @param addressInfo 地址信息
 * @param fallbackAddress 备用地址
 */
export function quickNavigate(addressInfo?: AddressInfo, fallbackAddress?: string): void {
  navigateToAddress(addressInfo, fallbackAddress, {
    showLoading: true,
    scale: 18
  }).catch(error => {
    console.error('快速导航失败:', error);
  });
}
