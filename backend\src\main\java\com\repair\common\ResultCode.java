package com.repair.common;

/**
 * 结果状态码常量类
 */
public class ResultCode {
    /**
     * 成功状态码
     */
    public static final int SUCCESS = 200;
    
    /**
     * 客户端错误状态码
     */
    public static final int CLIENT_ERROR = 400;
    
    /**
     * 服务端错误状态码
     */
    public static final int SERVER_ERROR = 500;
    
    /**
     * 认证错误状态码
     */
    public static final int UNAUTHORIZED = 401;
    
    /**
     * 权限不足状态码
     */
    public static final int FORBIDDEN = 403;
    
    /**
     * 资源不存在状态码
     */
    public static final int NOT_FOUND = 404;
    
    /**
     * 用户已存在错误
     */
    public static final String USERNAME_EXISTS = "用户名已存在";
    
    /**
     * 手机号已存在错误
     */
    public static final String PHONE_EXISTS = "手机号已被使用";
    
    /**
     * 操作失败错误
     */
    public static final String OPERATION_FAILED = "操作失败，请稍后重试";
} 