import { getLocation as uniGetLocation } from './uniapi';
import { ADDRESS_CONFIG } from './config';
import type { LocationResult, PermissionResult } from '@/types/location';

/**
 * 检查地理位置权限
 */
export async function checkLocationPermission(): Promise<PermissionResult> {
  return new Promise((resolve) => {
    uni.getSetting({
      success: (res) => {
        const authSetting = res.authSetting;
        resolve({
          authorized: authSetting['scope.userLocation'] === true,
          denied: authSetting['scope.userLocation'] === false,
          restricted: authSetting['scope.userLocation'] === undefined
        });
      },
      fail: () => {
        resolve({ authorized: false, denied: false, restricted: true });
      }
    });
  });
}

/**
 * 请求地理位置权限
 */
export async function requestLocationPermission(): Promise<boolean> {
  return new Promise((resolve) => {
    uni.authorize({
      scope: 'scope.userLocation',
      success: () => resolve(true),
      fail: () => resolve(false)
    });
  });
}

/**
 * 获取当前位置
 */
export async function getCurrentLocation(): Promise<LocationResult> {
  const permission = await checkLocationPermission();
  
  if (!permission.authorized) {
    if (permission.denied) {
      throw new Error('PERMISSION_DENIED');
    }
    
    const granted = await requestLocationPermission();
    if (!granted) {
      throw new Error('PERMISSION_DENIED');
    }
  }
  
  try {
    const result = await uniGetLocation('gcj02');
    return {
      latitude: result.latitude,
      longitude: result.longitude,
      accuracy: result.accuracy,
      altitude: result.altitude,
      speed: result.speed
    };
  } catch (error: any) {
    console.error('获取位置失败:', error);
    if (error.errMsg?.includes('timeout')) {
      throw new Error('TIMEOUT');
    }
    if (error.errMsg?.includes('fail')) {
      throw new Error('POSITION_UNAVAILABLE');
    }
    throw error;
  }
}

/**
 * 带超时的位置获取
 */
export async function getLocationWithTimeout(timeout = ADDRESS_CONFIG.LOCATION_TIMEOUT): Promise<LocationResult> {
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => reject(new Error('TIMEOUT')), timeout);
  });
  
  const locationPromise = getCurrentLocation();
  
  try {
    return await Promise.race([locationPromise, timeoutPromise]);
  } catch (error) {
    if (error instanceof Error && error.message === 'TIMEOUT') {
      uni.showToast({
        title: '定位超时，请重试或手动输入',
        icon: 'none',
        duration: 3000
      });
    }
    throw error;
  }
}

/**
 * 显示位置权限用途说明弹窗
 */
export function showLocationPurposeDialog(): Promise<boolean> {
  return new Promise((resolve) => {
    uni.showModal({
      title: '位置服务说明',
      content: '为了提供准确的维修地址服务，需要获取您的位置信息。您的位置信息仅用于地址解析，不会用于其他用途。',
      confirmText: '同意',
      cancelText: '拒绝',
      success: (res) => {
        resolve(res.confirm);
      }
    });
  });
}

/**
 * 显示设置页面引导
 */
export function showSettingsGuide(): Promise<boolean> {
  return new Promise((resolve) => {
    uni.showModal({
      title: '开启位置权限',
      content: '请在设置页面中开启位置权限：\n\n1. 点击"去设置"\n2. 找到"位置信息"开关\n3. 开启权限后返回',
      confirmText: '去设置',
      cancelText: '手动输入',
      success: (res) => {
        if (res.confirm) {
          // 打开设置页面
          uni.openSetting({
            success: (settingRes) => {
              // 检查权限是否已开启
              const hasLocationPermission = settingRes.authSetting['scope.userLocation'] === true;

              if (hasLocationPermission) {
                uni.showToast({
                  title: '权限开启成功',
                  icon: 'success',
                  duration: 2000
                });
                resolve(true);
              } else {
                // 权限仍未开启，提供进一步指导
                showDetailedPermissionGuide().then(resolve);
              }
            },
            fail: (error) => {
              console.error('打开设置页面失败:', error);
              uni.showToast({
                title: '无法打开设置页面',
                icon: 'none',
                duration: 2000
              });
              resolve(false);
            }
          });
        } else {
          resolve(false);
        }
      },
      fail: () => resolve(false)
    });
  });
}

/**
 * 显示详细的权限引导说明
 */
export function showDetailedPermissionGuide(): Promise<boolean> {
  return new Promise((resolve) => {
    uni.showModal({
      title: '权限设置帮助',
      content: '如果设置页面显示不完整或找不到位置权限选项，请尝试以下方法：\n\n方法1：重启小程序\n• 完全关闭小程序\n• 重新打开小程序\n• 再次尝试获取位置\n\n方法2：检查微信版本\n• 确保微信版本为最新\n• 重启微信后重试\n\n方法3：手动输入地址\n• 选择手动输入\n• 输入详细地址信息',
      confirmText: '重新尝试',
      cancelText: '手动输入',
      success: (res) => {
        if (res.confirm) {
          // 再次尝试打开设置
          uni.openSetting({
            success: (settingRes) => {
              const hasLocationPermission = settingRes.authSetting['scope.userLocation'] === true;
              if (hasLocationPermission) {
                uni.showToast({
                  title: '权限开启成功',
                  icon: 'success',
                  duration: 2000
                });
              }
              resolve(hasLocationPermission);
            },
            fail: () => resolve(false)
          });
        } else {
          resolve(false);
        }
      },
      fail: () => resolve(false)
    });
  });
}

/**
 * 智能权限引导 - 根据不同情况提供不同的引导方案
 */
export async function smartPermissionGuide(): Promise<boolean> {
  const permission = await checkLocationPermission();

  if (permission.authorized) {
    return true;
  }

  if (permission.restricted) {
    // 首次请求权限
    const granted = await requestLocationPermission();
    if (granted) {
      return true;
    }
  }

  // 权限被拒绝，引导用户到设置页面
  return await showSettingsGuide();
}

/**
 * 检查微信版本是否支持完整的设置页面
 */
export function checkWechatVersionSupport(): boolean {
  try {
    const systemInfo = uni.getSystemInfoSync();
    const wechatVersion = systemInfo.version;

    // 微信版本 8.0.0 以上支持完整的设置页面
    const versionParts = wechatVersion.split('.').map(Number);
    const majorVersion = versionParts[0] || 0;
    const minorVersion = versionParts[1] || 0;

    return majorVersion > 8 || (majorVersion === 8 && minorVersion >= 0);
  } catch (error) {
    console.warn('无法获取微信版本信息:', error);
    return true; // 默认认为支持
  }
}

/**
 * 限制坐标精度
 */
export function limitCoordinatePrecision(coordinate: number): number {
  return Math.round(coordinate * Math.pow(10, ADDRESS_CONFIG.COORDINATE_PRECISION)) / Math.pow(10, ADDRESS_CONFIG.COORDINATE_PRECISION);
}

/**
 * 验证坐标范围
 */
export function validateCoordinateRange(latitude: number, longitude: number): boolean {
  return latitude >= -90 && latitude <= 90 && longitude >= -180 && longitude <= 180;
}
