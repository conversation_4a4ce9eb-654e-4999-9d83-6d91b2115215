#!/bin/sh
set -e

# 定义必需的环境变量列表
REQUIRED_VARS="SPRING_DATASOURCE_URL SPRING_DATASOURCE_USERNAME SPRING_DATASOURCE_PASSWORD SPRING_REDIS_HOST SPRING_REDIS_PASSWORD JWT_SECRET APP_MYSQL_CHECK_ON_STARTUP APP_MYSQL_FAIL_ON_ERROR"

# 检查必需的环境变量
for var in $REQUIRED_VARS; do
  if [ -z "$(eval echo \$$var)" ]; then
    echo "错误: 必需的环境变量 $var 未设置"
    exit 1
  else
    # 打印环境变量值（敏感信息用星号替代）
    if echo "$var" | grep -q "PASSWORD\|SECRET"; then
      echo "$var = ********"
    else
      echo "$var = $(eval echo \$$var)"
    fi
  fi
done

echo "所有必需的环境变量已设置，开始启动应用..."

# 执行原始的启动命令
exec java -jar /app/app.jar 