package com.repair.utils;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.springframework.stereotype.Component;

/**
 * 雪花算法工具类
 * 用于生成分布式唯一ID
 */
@Component
public class SnowflakeUtil {

    // 使用 IdUtil.getSnowflake 替代已废弃的 createSnowflake 方法
    private static final Snowflake SNOWFLAKE = IdUtil.getSnowflake(1, 1);

    /**
     * 获取雪花算法生成的ID
     * @return ID值
     */
    public static long nextId() {
        return SNOWFLAKE.nextId();
    }

    /**
     * 获取雪花算法生成的ID（字符串形式）
     * @return ID字符串
     */
    public static String nextIdStr() {
        return SNOWFLAKE.nextIdStr();
    }
}
