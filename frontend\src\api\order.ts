import { request } from '@/utils/request';
import type { AddressData } from '@/types/location';

// 订单状态枚举
export enum OrderStatus {
  PENDING = 0,     // 待处理
  ACCEPTED = 1,    // 已接单
  PROCESSING = 2,  // 处理中
  COMPLETED = 3,   // 已完成
  CANCELED = 4     // 已取消
}

// 地址信息接口（用于API传输）
export interface AddressInfo {
  province: string;
  city: string;
  district: string;
  street: string;
  streetNumber?: string;
  detailAddress?: string;
  formattedAddress: string;
  latitude: number;
  longitude: number;
  accuracy?: number;
}

// 订单信息接口
export interface OrderInfo {
  id: number;
  orderId: string; // 雪花算法生成的订单号
  userId: number;
  username?: string;
  repairerId?: number;
  repairerName?: string;
  repairerPhone?: string; // 维修师手机号
  description: string;
  contactName: string;
  contactPhone: string;
  status: OrderStatus;
  statusDesc?: string;
  urgent: boolean;
  rating?: number;
  comment?: string;
  appointmentTime: string;
  createTime: string;
  updateTime?: string;
  completeTime?: string;
  // 结构化地址字段
  addressInfo: AddressInfo;
}

// 分页响应结果
export interface PageResult<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

// 创建订单参数
export interface CreateOrderParams {
  description: string;
  contactName: string;
  contactPhone: string;
  appointmentTime: string;
  urgent: number;
  // 结构化地址字段
  addressInfo: AddressInfo;
}

// 查询订单列表参数
export interface OrderQueryParams {
  page?: number;
  pageSize?: number;
  status?: OrderStatus;
}

// 订单API接口
export const orderApi = {
  // 创建订单
  createOrder(params: CreateOrderParams) {
    return request<number>({
      url: '/api/order/create',
      method: 'POST',
      data: params,
      auth: true
    });
  },

  // 获取用户订单列表
  getOrderList(params: OrderQueryParams) {
    return request<PageResult<OrderInfo>>({
      url: '/api/order/list',
      method: 'GET',
      data: params,
      auth: true
    });
  },

  // 获取订单详情
  getOrderDetail(id: number) {
    console.log('调用getOrderDetail API，参数ID:', id);
    return request<OrderInfo>({
      url: `/api/order/detail/${id}`,
      method: 'GET',
      auth: true
    });
  },

  // 取消订单
  cancelOrder(id: number) {
    return request<boolean>({
      url: `/api/order/cancel/${id}`,
      method: 'POST',
      auth: true
    });
  },

  // 评价订单
  rateOrder(id: number, rating: number, comment: string) {
    return request<boolean>({
      url: `/api/order/rate/${id}`,
      method: 'POST',
      data: { rating, comment },
      auth: true
    });
  }
}; 