// 地理编码服务
import type { AddressInfo } from './addressFormatter';
import { MAP_CONFIG, isMapConfigValid } from '@/config/map';
import { geocodeAddress as tencentGeocode, checkTencentMapAvailable } from './tencentMap';
import type { AddressData } from '@/types/location';

// 地理编码结果接口
export interface GeocodingResult {
  latitude: number;
  longitude: number;
  formattedAddress: string;
  province?: string;
  city?: string;
  district?: string;
  street?: string;
}

// 地理编码缓存
const geocodingCache = new Map<string, GeocodingResult>();

/**
 * 地理编码服务 - 将地址转换为坐标
 * @param address 地址字符串
 * @returns Promise<GeocodingResult | null>
 */
export async function geocodeAddress(address: string): Promise<GeocodingResult | null> {
  if (!address || address === '-') {
    return null;
  }

  // 检查缓存
  const cacheKey = address.trim();
  if (geocodingCache.has(cacheKey)) {
    console.log('使用地理编码缓存:', cacheKey);
    return geocodingCache.get(cacheKey)!;
  }

  console.log('开始地理编码:', address);

  // 优先使用腾讯地图API（如果已配置）
  if (checkTencentMapAvailable()) {
    try {
      console.log('使用腾讯地图进行地理编码');
      const tencentResult = await tencentGeocode(address);

      if (tencentResult) {
        const result: GeocodingResult = {
          latitude: tencentResult.latitude,
          longitude: tencentResult.longitude,
          formattedAddress: tencentResult.formattedAddress,
          province: tencentResult.province,
          city: tencentResult.city,
          district: tencentResult.district,
          street: tencentResult.street
        };

        // 缓存结果
        geocodingCache.set(cacheKey, result);
        console.log('腾讯地图地理编码成功:', result);

        return result;
      }
    } catch (error) {
      console.warn('腾讯地图地理编码失败，尝试高德地图:', error);
    }
  }

  // 降级到高德地图API
  if (isMapConfigValid()) {
    try {
      console.log('使用高德地图进行地理编码');

      // 调用高德地图地理编码API
      const response = await uni.request({
        url: MAP_CONFIG.GEOCODING.BASE_URL,
        method: 'GET',
        data: {
          key: MAP_CONFIG.AMAP_KEY,
          address: address,
          output: MAP_CONFIG.GEOCODING.OUTPUT
        },
        timeout: MAP_CONFIG.GEOCODING.TIMEOUT
      });

      if (response.statusCode === 200 && response.data) {
        const data = response.data as any;

        if (data.status === '1' && data.geocodes && data.geocodes.length > 0) {
          const geocode = data.geocodes[0];
          const [longitude, latitude] = geocode.location.split(',').map(Number);

          const result: GeocodingResult = {
            latitude,
            longitude,
            formattedAddress: geocode.formatted_address || address,
            province: geocode.province,
            city: geocode.city,
            district: geocode.district,
            street: geocode.street
          };

          // 缓存结果
          geocodingCache.set(cacheKey, result);
          console.log('高德地图地理编码成功:', result);

          return result;
        } else {
          console.warn('高德地图地理编码失败:', data.info || '未知错误');
        }
      } else {
        console.error('高德地图地理编码请求失败:', response);
      }
    } catch (error) {
      console.error('高德地图地理编码异常:', error);
    }
  }

  console.warn('所有地理编码服务均不可用');
  return null;
}

/**
 * 检查地址信息是否包含有效坐标
 * @param addressInfo 地址信息对象
 * @returns boolean
 */
export function hasValidCoordinates(addressInfo?: AddressInfo): boolean {
  return !!(
    addressInfo?.latitude && 
    addressInfo?.longitude && 
    addressInfo.latitude !== 0 && 
    addressInfo.longitude !== 0
  );
}

/**
 * 清除地理编码缓存
 */
export function clearGeocodingCache(): void {
  geocodingCache.clear();
  console.log('地理编码缓存已清除');
}

/**
 * 获取缓存统计信息
 */
export function getCacheStats(): { size: number; keys: string[] } {
  return {
    size: geocodingCache.size,
    keys: Array.from(geocodingCache.keys())
  };
}
