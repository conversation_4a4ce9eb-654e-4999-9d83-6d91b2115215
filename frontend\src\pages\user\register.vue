<template>
  <view class="container">
    <!-- 使用统一的返回按钮组件 -->
    <common-back-button></common-back-button>
    
    <view class="register-container">
      <!-- 欢迎文本 -->
      <view class="welcome-text">
        <text class="title">注册账号</text>
        <text class="subtitle">创建您的用户账号</text>
      </view>
      
      <!-- 注册表单 -->
      <view class="form-container">
        <!-- 用户名输入 -->
        <view class="form-item">
          <text class="label">用户名</text>
          <view class="input-wrapper">
            <input 
              type="text" 
              v-model="form.username" 
              class="input" 
              placeholder="请输入用户名"
              placeholder-class="placeholder"
            />
          </view>
        </view>
        
        <!-- 手机号输入 -->
        <view class="form-item">
          <text class="label">手机号码</text>
          <view class="input-wrapper">
            <input 
              type="number" 
              v-model="form.phone" 
              class="input" 
              placeholder="请输入手机号"
              placeholder-class="placeholder"
              maxlength="11"
            />
          </view>
        </view>
        
        <!-- 验证码输入 -->
        <view class="form-item">
          <text class="label">验证码</text>
          <view class="input-wrapper code-wrapper">
            <input 
              type="number" 
              v-model="form.verifyCode" 
              class="input" 
              placeholder="请输入验证码"
              placeholder-class="placeholder"
              maxlength="6"
            />
            <view 
              class="send-code-btn" 
              :class="{ 'disabled': countdownValue > 0 || isSending }"
              @click="sendVerificationCode"
            >
              <text v-if="isSending">发送中...</text>
              <text v-else-if="countdownValue > 0">{{ countdownValue }}秒</text>
              <text v-else>获取验证码</text>
            </view>
          </view>
        </view>
        
        <!-- 密码输入 -->
        <view class="form-item">
          <text class="label">密码</text>
          <password-input
            v-model="form.password"
            placeholder="请输入6-20位密码"
            placeholder-class="placeholder"
          />
        </view>
        
        <!-- 确认密码输入 -->
        <view class="form-item">
          <text class="label">确认密码</text>
          <password-input
            v-model="form.confirmPassword"
            placeholder="请再次输入密码"
            placeholder-class="placeholder"
          />
        </view>
        
        <!-- 地址输入 -->
        <view class="form-item">
          <text class="label">地址</text>
          <address-input
            v-model="form.addressInfo"
            @addressChange="handleAddressChange"
            :config="{
              required: false,
              placeholder: '请输入地址信息',
              showCurrentLocation: true,
              showMapPicker: false,
              maxDetailLength: 200
            }"
          />
        </view>
        
        <!-- 注册按钮 -->
        <button 
          class="register-btn"
          :class="{'btn-loading': loading}"
          :disabled="loading"
          @click="handleRegister"
        >
          <text v-if="loading">注册中...</text>
          <text v-else>注册</text>
        </button>
        
        <!-- 返回登录 -->
        <view class="back-to-login" @click="navigateToLogin">
          <text>返回登录</text>
        </view>
      </view>
    </view>
    
    <!-- 滑块验证组件 -->
    <puzzle-verify
      :visible="showCaptcha"
      @success="captchaService.handleCaptchaSuccess"
      @fail="captchaService.handleCaptchaFail"
      @cancel="captchaService.handleCaptchaCancel"
      @update:visible="showCaptcha = $event"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onUnmounted } from 'vue';
import { userApi, type RegisterParams } from '@/api/user';
import { showToast, showLoading, hideLoading } from '@/utils/uniapi';
import CommonBackButton from '@/components/common/BackButton.vue';
import { isValidPhone, sendVerifyCode, useCountdown } from '@/utils/verifycode';
import PasswordInput from '@/components/common/PasswordInput.vue';
import { captchaService } from '@/utils/captchaService';
import PuzzleVerify from '@/components/common/PuzzleVerify.vue';
import AddressInput from '@/components/address/AddressInput.vue';
import type { AddressData } from '@/types/location';

const loading = ref(false);

const form = reactive({
  username: '',
  phone: '',
  password: '',
  confirmPassword: '',
  verifyCode: '',
  // 结构化地址字段
  addressInfo: null as AddressData | null
});

// 使用统一的倒计时工具，这里使用一个计算属性来获取phone
const phone = computed(() => form.phone);
const countdownState = useCountdown(phone, 'user');
const countdownValue = computed(() => countdownState.countdown.value);
const isSending = computed(() => countdownState.isSending.value);

// 滑块验证状态
const showCaptcha = computed({
  get: () => captchaService.showCaptcha.value,
  set: (value) => captchaService.showCaptcha.value = value
});

// 组件卸载时清理倒计时
onUnmounted(() => {
  if (countdownState.cleanup) {
    countdownState.cleanup();
  }
});

// 处理地址变化
function handleAddressChange(addressData: AddressData) {
  form.addressInfo = addressData;
}

// 发送验证码
async function sendVerificationCode() {
  if (countdownValue.value > 0 || isSending.value) {
    return; // 防止重复点击
  }

  // 执行发送逻辑前再次验证手机号
  if (!isValidPhone(form.phone)) {
    showToast('请输入正确的手机号码');
    return;
  }

  // 使用滑块验证后发送验证码
  await captchaService.sendVerifyCodeWithCaptcha(form.phone, 'user', false);
}

function validateForm() {
  if (!form.username) {
    showToast('请输入用户名');
    return false;
  }

  if (!isValidPhone(form.phone)) {
    showToast('请输入正确的手机号');
    return false;
  }
  
  if (!form.verifyCode) {
    showToast('请输入验证码');
    return false;
  }

  if (!form.password) {
    showToast('请输入密码');
    return false;
  }

  if (form.password.length < 6 || form.password.length > 20) {
    showToast('密码长度应为6-20位');
    return false;
  }

  if (form.password !== form.confirmPassword) {
    showToast('两次输入的密码不一致');
    return false;
  }

  if (!form.address) {
    showToast('请输入地址');
    return false;
  }

  return true;
}

async function handleRegister() {
  if (!validateForm()) return;

  loading.value = true;
  
  try {
    showLoading('注册中...');
    
    // 调用注册接口
    const registerParams: RegisterParams = {
      username: form.username,
      phone: form.phone,
      password: form.password,
      verifyCode: form.verifyCode,
      // 结构化地址信息（可选）
      addressInfo: form.addressInfo ? {
        province: form.addressInfo.province,
        city: form.addressInfo.city,
        district: form.addressInfo.district,
        street: form.addressInfo.street,
        streetNumber: form.addressInfo.streetNumber,
        detailAddress: form.addressInfo.detailAddress,
        formattedAddress: form.addressInfo.formattedAddress,
        latitude: form.addressInfo.latitude,
        longitude: form.addressInfo.longitude,
        accuracy: form.addressInfo.accuracy
      } : undefined
    };
    
    console.log('注册提交数据:', JSON.stringify(registerParams));
    
    await userApi.register(registerParams);
    
    hideLoading();
    showToast('注册成功', 'success');
    
    // 注册成功后跳转到登录页
    setTimeout(() => {
      uni.redirectTo({
        url: './login'
      });
    }, 1500);
  } catch (error: any) {
    hideLoading();
    console.error('注册失败:', error);
    // 显示具体的错误信息，包括验证码错误
    const errorMsg = error.message ? error.message : '注册失败，请重试';
    showToast(errorMsg);
  } finally {
    loading.value = false;
  }
}

function navigateToLogin() {
  uni.navigateBack();
}
</script>

<style>
.container {
  width: 100%;
  min-height: 100vh;
  background-color: #f6f7fb;
  position: relative;
  padding-bottom: 40rpx;
  box-sizing: border-box;
}

.register-container {
  padding: 40rpx 30rpx;
  box-sizing: border-box;
  padding-top: calc(var(--status-bar-height) + 140rpx); /* 增加顶部间距，避免与返回按钮重叠 */
}

/* 欢迎文本样式 */
.welcome-text {
  margin-bottom: 40rpx;
  text-align: left; /* 设置为左对齐 */
}

.title {
  font-size: 42rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  display: block;
}

/* 表单样式 */
.form-container {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.05);
}

.form-item {
  margin-bottom: 24rpx;
}

.label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
  font-weight: 500;
}

.input-wrapper {
  background-color: #f8f9fc;
  border-radius: 12rpx;
  overflow: hidden;
  border: 2rpx solid #eaedf5;
  transition: all 0.3s;
}

.input-wrapper:focus-within {
  border-color: #FF6B00;
  box-shadow: 0 0 0 2rpx rgba(255, 107, 0, 0.1);
}

.input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
}

.textarea-wrapper {
  height: 160rpx;
}

.textarea {
  width: 100%;
  height: 140rpx;
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  color: #333;
}

.placeholder {
  color: #999;
}

/* 验证码输入框 */
.code-input-wrapper {
  display: flex;
  align-items: center;
}

.code-input-wrapper .input {
  flex: 1;
}

/* 添加正确的验证码输入框样式 */
.code-wrapper {
  display: flex;
  align-items: center;
}

.code-wrapper .input {
  flex: 1;
}

.send-code-btn {
  min-width: 200rpx;
  height: 88rpx;
  background-color: #FF6B00;
  color: #fff;
  border-radius: 12rpx;
  font-size: 26rpx;
  margin-left: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  padding: 0 20rpx;
  white-space: nowrap;
  border: none;
  box-sizing: border-box;
}

.send-code-btn.disabled {
  background-color: #ccc;
  color: #fff;
  opacity: 0.8;
}

/* 注册按钮 */
.register-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #FF6B00, #FF9500);
  border-radius: 44rpx;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40rpx;
  box-shadow: 0 8rpx 16rpx rgba(255, 107, 0, 0.2);
  transition: all 0.3s;
}

.register-btn:active {
  transform: scale(0.98);
}

.btn-loading {
  opacity: 0.8;
}

/* 底部链接 */
.bottom-links {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 40rpx;
}

.link {
  color: #FF6B00;
  font-size: 28rpx;
  margin-left: 10rpx;
}
</style> 