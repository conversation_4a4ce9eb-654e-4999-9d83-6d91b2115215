package com.repair.config;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * 必需的配置属性
 * 在生产环境中，确保这些属性必须从环境变量中提供
 */
@Configuration
@Profile("prod")
@Slf4j
@Getter
public class RequiredConfigProperties {

    @Value("${APP_MYSQL_CHECK_ON_STARTUP:#{null}}")
    private String mysqlCheckOnStartup;

    @Value("${APP_MYSQL_FAIL_ON_ERROR:#{null}}")
    private String mysqlFailOnError;
    
    // 可以根据需要添加更多必需的环境变量
} 