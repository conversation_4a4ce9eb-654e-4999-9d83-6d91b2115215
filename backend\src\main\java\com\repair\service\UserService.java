package com.repair.service;

import com.repair.dto.PasswordChangeDTO;
import com.repair.dto.ResetPasswordDTO;
import com.repair.dto.UserInfoUpdateDTO;
import com.repair.dto.UserLoginDTO;
import com.repair.dto.UserRegisterDTO;
import com.repair.dto.WxBindPhoneDTO;
import com.repair.dto.WxLoginDTO;
import com.repair.vo.UserInfoVO;
import com.repair.vo.UserLoginVO;
import com.repair.vo.WxLoginVO;

public interface UserService {
    /**
     * 用户登录
     */
    UserLoginVO login(UserLoginDTO loginDTO);
    
    /**
     * 用户注册
     */
    UserInfoVO register(UserRegisterDTO registerDTO);
    
    /**
     * 获取用户信息
     */
    UserInfoVO getUserInfo(Long userId);
    
    /**
     * 更新用户信息
     */
    UserInfoVO updateUserInfo(Long userId, UserInfoUpdateDTO updateDTO);
    
    /**
     * 修改密码
     */
    void changePassword(Long userId, PasswordChangeDTO passwordDTO);
    
    /**
     * 重置密码（忘记密码）
     */
    void resetPassword(ResetPasswordDTO resetPasswordDTO);
    
    /**
     * 微信小程序登录
     */
    WxLoginVO wxLogin(WxLoginDTO wxLoginDTO);
    
    /**
     * 微信小程序绑定手机号
     */
    UserLoginVO wxBindPhone(WxBindPhoneDTO wxBindPhoneDTO);
} 