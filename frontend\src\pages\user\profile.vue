<template>
  <view class="container">
    <!-- 使用统一的返回按钮组件 -->
    <common-back-button></common-back-button>
    
    <!-- 页面标题 -->
    <view class="header">
      <text class="title">个人信息</text>
    </view>

    <!-- 表单区域 -->
    <view class="form-container">
      <!-- 用户名 -->
      <view class="form-item">
        <text class="label">用户名<text class="required">*</text></text>
        <view class="input-wrapper">
          <input
            v-model="userInfo.username"
            class="input"
            type="text"
            placeholder="请输入用户名"
            placeholder-class="placeholder"
          />
        </view>
      </view>

      <!-- 手机号 -->
      <view class="form-item">
        <text class="label">手机号</text>
        <view class="input-wrapper readonly">
          <input
            v-model="userInfo.phone"
            class="input"
            type="text"
            disabled
          />
        </view>
      </view>

      <!-- 地址 -->
      <view class="form-item">
        <text class="label">地址<text class="required">*</text></text>
        <view class="input-wrapper">
          <input
            v-model="userInfo.address"
            class="input"
            type="text"
            placeholder="请输入地址"
            placeholder-class="placeholder"
          />
        </view>
      </view>

      <!-- 提交按钮 -->
      <button
        class="submit-btn"
        :class="{'btn-loading': loading}"
        :disabled="loading"
        @click="updateUserInfo"
      >
        <text v-if="loading">保存中...</text>
        <text v-else>保存修改</text>
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useUserStore } from '@/store/user';
import { userApi, type UserInfo } from '@/api/user';
import CommonBackButton from '@/components/common/BackButton.vue';

const userStore = useUserStore();
const loading = ref(false);

// 用户信息
const userInfo = reactive<UserInfo>({
  id: 0,
  username: '',
  phone: '',
  address: ''
});

onMounted(async () => {
  // 验证登录状态
  if (!userStore.token) {
    uni.redirectTo({ url: '/pages/user/login' });
    return;
  }

  // 获取用户信息
  await fetchUserInfo();
});

// 获取用户信息
async function fetchUserInfo() {
  loading.value = true;
  try {
    const info = await userStore.getUserInfo();
    userInfo.id = info.id;
    userInfo.username = info.username;
    userInfo.phone = info.phone || '';
    userInfo.address = info.address || '';
  } catch (error: any) {
    console.error('获取用户信息失败:', error);
    uni.showToast({
      title: error.message || '获取用户信息失败',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
}

// 表单验证
function validateForm(): boolean {
  if (!userInfo.username) {
    uni.showToast({
      title: '请输入用户名',
      icon: 'none'
    });
    return false;
  }
  
  if (!userInfo.address) {
    uni.showToast({
      title: '请输入地址',
      icon: 'none'
    });
    return false;
  }
  
  return true;
}

// 更新用户信息
async function updateUserInfo() {
  if (!validateForm()) {
    return;
  }

  loading.value = true;
  try {
    await userApi.updateUserInfo({
      username: userInfo.username,
      address: userInfo.address
    });

    uni.showToast({
      title: '保存成功',
      icon: 'success',
      duration: 1500
    });

    // 更新存储的用户信息
    userStore.setUserInfo(userInfo);

    // 1.5秒后返回上一页
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  } catch (error: any) {
    console.error('更新用户信息失败:', error);
    uni.showToast({
      title: error.message || '保存失败，请重试',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
}
</script>

<style>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

.header {
  position: relative;
  height: 96rpx;
  margin-top: 160rpx; /* 为返回按钮留出空间 */
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #f0f0f0;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

.form-container {
  margin: 24rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
}

.form-item {
  margin-bottom: 32rpx;
}

.label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.required {
  color: #ff4d4f;
  margin-left: 4rpx;
}

.input-wrapper {
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;
  overflow: hidden;
}

.input-wrapper.readonly {
  background-color: #f5f5f5;
}

.input {
  height: 88rpx;
  font-size: 28rpx;
  color: #333;
  padding: 0 24rpx;
  width: 100%;
}

.submit-btn {
  margin-top: 40rpx;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  color: #fff;
  background: #07c160;
  border-radius: 44rpx;
  text-align: center;
}

.btn-loading {
  opacity: 0.7;
}

.placeholder {
  color: #999;
}
</style> 