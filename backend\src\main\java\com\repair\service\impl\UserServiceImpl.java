package com.repair.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.repair.dto.PasswordChangeDTO;
import com.repair.dto.UserInfoUpdateDTO;
import com.repair.dto.UserLoginDTO;
import com.repair.dto.UserRegisterDTO;
import com.repair.dto.WxBindPhoneDTO;
import com.repair.dto.WxLoginDTO;
import com.repair.dto.ResetPasswordDTO;
import com.repair.entity.RepairOrder;
import com.repair.entity.User;
import com.repair.entity.UserMergeLog;
import com.repair.exception.BusinessException;
import com.repair.mapper.RepairOrderMapper;
import com.repair.mapper.UserMapper;
import com.repair.mapper.UserMergeLogMapper;
import com.repair.service.UserService;
import com.repair.service.VerifyCodeService;
import com.repair.utils.AddressUtil;
import com.repair.utils.JwtUtil;
import com.repair.utils.WechatUtil;
import com.repair.vo.UserInfoVO;
import com.repair.vo.UserLoginVO;
import com.repair.vo.WxLoginVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import lombok.RequiredArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

@Service
@Slf4j
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    private final UserMapper userMapper;
    private final VerifyCodeService verifyCodeService;
    private final WechatUtil wechatUtil;
    private final RepairOrderMapper repairOrderMapper;
    private final UserMergeLogMapper userMergeLogMapper;

    @Override
    public UserLoginVO login(UserLoginDTO loginDTO) {
        log.info("开始处理用户登录请求，手机号: {}", loginDTO.getPhone());
        
        try {
            // 1. 根据手机号查询用户
            LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(User::getPhone, loginDTO.getPhone());
            User user = this.getOne(queryWrapper);
            
            if (user == null) {
                log.warn("用户不存在: {}", loginDTO.getPhone());
                throw new BusinessException("用户不存在");
            }
            
            log.info("用户信息查询成功: {}", user.getUsername());

            // 2. 检查用户状态
            if (user.getStatus() == null || user.getStatus() != 1) {
                log.warn("账号已被禁用: {}", user.getUsername());
                throw new BusinessException("账号已被禁用");
            }

            // 3. 密码校验
            String encryptedPassword = DigestUtils.md5DigestAsHex(loginDTO.getPassword().getBytes());
            log.info("输入密码加密后: {}", encryptedPassword);
            log.info("数据库存储密码: {}", user.getPassword());
            
            if (!encryptedPassword.equals(user.getPassword())) {
                log.warn("密码错误: {} != {}", encryptedPassword, user.getPassword());
                throw new BusinessException("密码错误");
            }

            // 4. 生成token
            String token = JwtUtil.createToken(user.getId(), user.getUsername(), "user");
            log.info("生成token成功,token:{}", token);

            // 5. 构建用户信息
            UserInfoVO userInfoVO = new UserInfoVO();
            BeanUtils.copyProperties(user, userInfoVO);
            log.info("用户登录成功: {}", user.getUsername());

            // 6. 构建登录响应
            return UserLoginVO.builder()
                    .token(token)
                    .userInfo(userInfoVO)
                    .build();
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("用户登录失败", e);
            throw new BusinessException("用户登录失败", 500);
        }
    }

    @Override
    @Transactional
    public UserInfoVO register(UserRegisterDTO registerDTO) {
        log.info("开始处理用户注册请求，手机号: {}", registerDTO.getPhone());
        
        try {
            // 验证验证码
            String phone = registerDTO.getPhone();
            String verifyCode = registerDTO.getVerifyCode();
            
            if (!verifyCodeService.validateVerifyCode(phone, verifyCode)) {
                log.warn("{}的验证码错误或已过期: {}", phone, verifyCode);
                throw new BusinessException("验证码错误或已过期");
            }
            
            // 校验手机号是否已存在
            LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(User::getPhone, registerDTO.getPhone());
            if (this.count(queryWrapper) > 0) {
                log.warn("手机号已被注册: {}", registerDTO.getPhone());
                throw new BusinessException("手机号已被注册");
            }
            
            // 验证地址信息（如果提供）
            if (registerDTO.getAddressInfo() != null && !AddressUtil.validateAddress(registerDTO.getAddressInfo())) {
                throw new BusinessException("地址信息不完整");
            }

            // 创建用户
            User user = buildUserEntity(registerDTO);

            // 保存用户
            this.save(user);

            // 验证码使用后删除
            verifyCodeService.deleteVerifyCode(phone);

            // 记录日志（地址脱敏）
            String maskedAddress = user.getFormattedAddress() != null ?
                AddressUtil.maskFormattedAddress(user.getFormattedAddress()) : "未提供";
            log.info("用户注册成功: {}，地址：{}", user.getUsername(), maskedAddress);

            // 构建返回对象
            UserInfoVO userInfoVO = new UserInfoVO();
            BeanUtils.copyProperties(user, userInfoVO);

            // 转换地址信息
            userInfoVO.setAddressInfo(AddressUtil.convertEntityToAddressVO(user));

            return userInfoVO;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("用户注册失败", e);
            throw new BusinessException("用户注册失败", 500);
        }
    }

    private User buildUserEntity(UserRegisterDTO registerDTO) {
        User user = new User();
        BeanUtils.copyProperties(registerDTO, user);

        // 处理地址信息（如果提供）
        if (registerDTO.getAddressInfo() != null) {
            AddressUtil.copyAddressToEntity(registerDTO.getAddressInfo(), user);
        }

        // 密码加密
        String encryptedPassword = DigestUtils.md5DigestAsHex(registerDTO.getPassword().getBytes());
        user.setPassword(encryptedPassword);

        // 设置默认状态
        user.setStatus(1);
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());

        return user;
    }

    @Override
    public UserInfoVO getUserInfo(Long userId) {
        log.info("开始处理获取用户信息请求，用户ID: {}", userId);
        
        try {
            User user = this.getById(userId);
            if (user == null) {
                log.warn("用户不存在: {}", userId);
                throw new BusinessException("用户不存在");
            }
            
            UserInfoVO userInfoVO = new UserInfoVO();
            BeanUtils.copyProperties(user, userInfoVO);

            // 转换地址信息
            userInfoVO.setAddressInfo(AddressUtil.convertEntityToAddressVO(user));

            log.info("用户信息获取成功: {}", user.getUsername());

            return userInfoVO;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            throw new BusinessException("获取用户信息失败", 500);
        }
    }

    @Override
    @Transactional
    public UserInfoVO updateUserInfo(Long userId, UserInfoUpdateDTO updateDTO) {
        log.info("开始处理更新用户信息请求，用户ID: {}", userId);
        
        try {
            User user = this.getById(userId);
            if (user == null) {
                log.warn("用户不存在: {}", userId);
                throw new BusinessException("用户不存在");
            }
            
            // 更新用户信息
            if (updateDTO.getUsername() != null) {
                user.setUsername(updateDTO.getUsername());
            }
            
            if (updateDTO.getPhone() != null) {
                // 检查手机号是否已被其他用户使用
                LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(User::getPhone, updateDTO.getPhone())
                          .ne(User::getId, userId);
                if (this.count(queryWrapper) > 0) {
                    log.warn("手机号已被其他用户使用: {}", updateDTO.getPhone());
                    throw new BusinessException("手机号已被其他用户使用");
                }
                user.setPhone(updateDTO.getPhone());
            }
            
            // 地址更新已移除，使用结构化地址
            // if (updateDTO.getAddress() != null) {
            //     user.setAddress(updateDTO.getAddress());
            // }
            
            user.setUpdateTime(LocalDateTime.now());
            this.updateById(user);
            
            // 构建返回对象
            UserInfoVO userInfoVO = new UserInfoVO();
            BeanUtils.copyProperties(user, userInfoVO);
            
            log.info("用户信息更新成功: {}", user.getUsername());
            
            return userInfoVO;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新用户信息失败", e);
            throw new BusinessException("更新用户信息失败", 500);
        }
    }

    @Override
    @Transactional
    public void changePassword(Long userId, PasswordChangeDTO passwordDTO) {
        log.info("开始处理修改密码请求，用户ID: {}", userId);
        
        try {
            User user = this.getById(userId);
            if (user == null) {
                log.warn("用户不存在: {}", userId);
                throw new BusinessException("用户不存在");
            }
            
            // 直接更新密码，不校验旧密码
            String newEncryptedPassword = DigestUtils.md5DigestAsHex(passwordDTO.getNewPassword().getBytes());
            user.setPassword(newEncryptedPassword);
            user.setUpdateTime(LocalDateTime.now());
            
            this.updateById(user);
            
            log.info("密码修改成功: {}", user.getUsername());
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("修改密码失败", e);
            throw new BusinessException("修改密码失败", 500);
        }
    }

    /**
     * 微信小程序登录
     * 功能：
     * 1. 通过code获取openid
     * 2. 查询用户是否存在
     * 3. 存在则直接返回登录信息
     * 4. 不存在则创建新用户并返回登录信息
     */
    @Override
    @Transactional
    public WxLoginVO wxLogin(WxLoginDTO wxLoginDTO) {
        log.info("开始处理微信小程序登录请求，code: {}", wxLoginDTO.getCode());
        
        try {
            // 1. 调用微信接口获取用户信息
            log.info("调用微信接口获取openid");
            Map<String, Object> wxUserInfo = wechatUtil.getWxUserInfoByCode(wxLoginDTO.getCode());
            
            String openid = (String) wxUserInfo.get("openid");
            log.info("根据code：{} 获取到openid: {}", wxLoginDTO.getCode(), openid);
            if (StringUtils.isBlank(openid)){
                log.error("根据code：{} 获取openid失败", wxLoginDTO.getCode());
                throw new BusinessException("获取openid失败");
            }
            
            String sessionKey = (String) wxUserInfo.get("sessionKey");
            String unionid = (String) wxUserInfo.getOrDefault("unionid", "");
            
            // 2. 根据openid查询用户
            User user = getUserByOpenId(openid);
            boolean isNewUser = (user == null);
            boolean needBindPhone;
            
            // 3. 如果用户不存在，创建一个临时用户
            if (isNewUser) {
                log.info("用户不存在，创建临时用户");
                user = new User();
                user.setOpenid(openid);
                if (unionid != null && !unionid.isEmpty()) {
                    user.setUnionid(unionid);
                }
                
                // 设置基本信息
                user.setUsername("微信用户");
                user.setStatus(1);
                user.setSource(1); // 小程序来源
                
                // 临时密码
                String tempPassword = DigestUtils.md5DigestAsHex(("wx" + System.currentTimeMillis()).getBytes());
                user.setPassword(tempPassword);
                
                user.setCreateTime(LocalDateTime.now());
                user.setUpdateTime(LocalDateTime.now());
                
                this.save(user);
                log.info("临时用户创建成功，userId: {}", user.getId());
                
                needBindPhone = true;
            } else {
                log.info("用户已存在，userId: {}", user.getId());
                // 判断是否需要绑定手机号
                needBindPhone = (user.getPhone() == null || user.getPhone().isEmpty());
                
                // 更新登录时间
                user.setUpdateTime(LocalDateTime.now());
                this.updateById(user);
            }
            
            // 4. 生成token
            String token = JwtUtil.createToken(user.getId(), user.getUsername(), "user");
            
            // 5. 构建返回结果
            UserInfoVO userInfoVO = new UserInfoVO();
            BeanUtils.copyProperties(user, userInfoVO);
            
            // 6. 在结果中包含额外信息（用于前端判断是否需要绑定手机号）
            WxLoginVO result = WxLoginVO.builder()
                    .token(token)
                    .userInfo(userInfoVO)
                    .isNewUser(isNewUser || needBindPhone) // 如果没有手机号，当作新用户处理
                    .needBindPhone(needBindPhone)
                    .sessionKey(sessionKey) // 将sessionKey返回给前端
                    .openid(openid) // 将openid返回给前端
                    .build();
            
            log.info("微信登录完成，isNewUser: {}, needBindPhone: {}", isNewUser, needBindPhone);
            return result;
        } catch (Exception e) {
            log.error("微信小程序登录失败", e);
            throw new BusinessException("微信小程序登录失败: " + e.getMessage());
        }
    }

    /**
     * 微信绑定手机号
     * 功能：
     * 1. 验证手机号和验证码
     * 2. 查询是否已有用户使用该手机号
     * 3. 如果有，则合并用户信息（将openid绑定到该用户）
     * 4. 如果没有，则更新用户信息
     */
    @Override
    @Transactional
    public UserLoginVO wxBindPhone(WxBindPhoneDTO wxBindPhoneDTO) {
        log.info("开始处理微信绑定手机号请求：{}", wxBindPhoneDTO);
        
        try {
            // 验证code和openid至少有一个不为空
            if (!wxBindPhoneDTO.isValid()) {
                log.warn("code和openid均为空，无法进行微信绑定");
                throw new BusinessException("微信登录信息不完整，code和openid不能同时为空");
            }
            
            // 1. 验证手机号
            String phone = wxBindPhoneDTO.getPhone();
            if (!phone.matches("^1[3-9]\\d{9}$")) {
                throw new BusinessException("手机号格式不正确");
            }
            
            // 验证验证码
            String phoneCode = wxBindPhoneDTO.getPhoneCode();
            if (StringUtils.isBlank(phoneCode)) {
                log.warn("{}未提供验证码，跳过验证", phone);
                throw new BusinessException("未提供验证码");
            }
            if (!verifyCodeService.validateVerifyCode(phone, phoneCode)) {
                log.warn("验证码错误或已过期: {}", phoneCode);
                throw new BusinessException("验证码错误或已过期");
            }
            
            // 2. 获取openid，优先使用请求中的openid
            String openid = wxBindPhoneDTO.getOpenid();
            if (openid == null || openid.isEmpty()) {
                // 如果请求中没有提供openid，则通过code获取
                Map<String, Object> wxResult = wechatUtil.getWxUserInfoByCode(wxBindPhoneDTO.getCode());
                openid = (String) wxResult.get("openid");
                if (openid == null || openid.isEmpty()) {
                    throw new BusinessException("获取微信openid失败");
                }
            }
            log.info("获取到openid: {}", openid);
            
            // 3. 查询是否存在openid对应的用户
            User wxUser = getUserByOpenId(openid);
            if (wxUser == null) {
                log.warn("未找到openid对应的用户: {}", openid);
                throw new BusinessException("微信用户不存在，请重新登录");
            }
            
            // 4. 查询是否存在手机号对应的用户
            LambdaQueryWrapper<User> phoneQuery = new LambdaQueryWrapper<>();
            phoneQuery.eq(User::getPhone, phone);
            User phoneUser = this.getOne(phoneQuery);
            
            User resultUser;
            
            // 5. 处理用户数据
            if (phoneUser != null && !phoneUser.getId().equals(wxUser.getId())) {
                // 手机号已被其他账号使用，将微信信息合并到该账号
                log.info("手机号已被账号{}使用，合并微信信息", phoneUser.getId());
                
                try {
                    // 记录原始微信账号ID和信息，用于后续迁移订单数据和记录日志
                    Long wxUserId = wxUser.getId();
                    String wxUsername = wxUser.getUsername();
                    String wxOpenid = wxUser.getOpenid();
                    
                    // 更新手机号账户的微信信息
                    phoneUser.setOpenid(openid);
                    phoneUser.setUpdateTime(LocalDateTime.now());
                    this.updateById(phoneUser);
                    
                    // 迁移订单数据 - 将原微信账号的订单关联到手机号账号
                    Long migratedOrderCount = migrateUserOrders(wxUserId, phoneUser.getId());
                    
                    // 记录账号合并日志
                    recordUserMergeLog(wxUserId, wxUsername, wxOpenid, 
                                      phoneUser.getId(), phoneUser.getUsername(), phoneUser.getPhone(),
                                      migratedOrderCount, "微信绑定手机号时合并账号", true, null);
                    
                    // 删除原微信账户（避免数据重复）
                    this.removeById(wxUser.getId());
                    
                    resultUser = phoneUser;
                    log.info("账号合并完成，订单数据已迁移");
                } catch (Exception e) {
                    log.error("账号合并过程中出错", e);
                    // 记录失败日志
                    recordUserMergeLog(wxUser.getId(), wxUser.getUsername(), wxUser.getOpenid(),
                                      phoneUser.getId(), phoneUser.getUsername(), phoneUser.getPhone(),
                                      0L, "微信绑定手机号时合并账号", false, e.getMessage());
                    throw new BusinessException("账号合并失败: " + e.getMessage());
                }
            } else {
                // 手机号未被使用或被当前微信账号使用，直接更新
                log.info("更新微信账号的手机号");
                wxUser.setPhone(phone);
                wxUser.setUpdateTime(LocalDateTime.now());
                
                // 如果用户名是默认的，可以更新为更友好的格式
                if ("微信用户".equals(wxUser.getUsername())) {
                    wxUser.setUsername("微信用户_" + phone.substring(phone.length() - 4));
                }
                
                this.updateById(wxUser);
                resultUser = wxUser;
                log.info("更新完成");
            }
            
            // 验证码使用后删除
            verifyCodeService.deleteVerifyCode(phone);
            
            // 6. 生成token
            String token = JwtUtil.createToken(resultUser.getId(), resultUser.getUsername(), "user");
            
            // 7. 构建并返回登录信息
            UserInfoVO userInfoVO = new UserInfoVO();
            BeanUtils.copyProperties(resultUser, userInfoVO);
            
            return UserLoginVO.builder()
                    .token(token)
                    .userInfo(userInfoVO)
                    .build();
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("微信绑定手机号失败", e);
            throw new BusinessException("绑定手机号失败: " + e.getMessage());
        }
    }

    /**
     * 迁移用户订单数据
     * 将源用户ID的所有订单更新为目标用户ID
     * 
     * @param sourceUserId 源用户ID（原微信账号）
     * @param targetUserId 目标用户ID（手机号账号）
     * @return 迁移的订单数量
     */
    private Long migrateUserOrders(Long sourceUserId, Long targetUserId) {
        try {
            // 查询源用户的所有订单
            LambdaQueryWrapper<RepairOrder> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(RepairOrder::getUserId, sourceUserId);
            Long orderCount = repairOrderMapper.selectCount(queryWrapper);
            
            if (orderCount > 0) {
                log.info("开始迁移用户订单数据，从用户ID {} 到用户ID {}，共 {} 条订单", 
                        sourceUserId, targetUserId, orderCount);
                
                // 批量更新订单的用户ID
                LambdaUpdateWrapper<RepairOrder> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(RepairOrder::getUserId, sourceUserId)
                           .set(RepairOrder::getUserId, targetUserId)
                           .set(RepairOrder::getUpdateTime, LocalDateTime.now());
                
                int updatedCount = repairOrderMapper.update(null, updateWrapper);
                log.info("订单数据迁移完成，成功更新 {} 条订单", updatedCount);
                return Long.valueOf(updatedCount);
            } else {
                log.info("源用户ID {} 没有订单数据需要迁移", sourceUserId);
                return 0L;
            }
        } catch (Exception e) {
            log.error("迁移用户订单数据失败", e);
            throw new BusinessException("迁移订单数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 记录用户账号合并日志
     */
    private void recordUserMergeLog(Long sourceUserId, String sourceUsername, String sourceOpenid,
                                   Long targetUserId, String targetUsername, String targetPhone,
                                   Long migratedOrderCount, String mergeReason, 
                                   boolean success, String errorMessage) {
        try {
            UserMergeLog log = new UserMergeLog()
                .setSourceUserId(sourceUserId)
                .setSourceUsername(sourceUsername)
                .setSourceOpenid(sourceOpenid)
                .setTargetUserId(targetUserId)
                .setTargetUsername(targetUsername)
                .setTargetPhone(targetPhone)
                .setMigratedOrderCount(migratedOrderCount)
                .setMergeReason(mergeReason)
                .setMergeTime(LocalDateTime.now())
                .setStatus(success ? 1 : 0);
                
            if (!success && errorMessage != null) {
                log.setErrorMessage(errorMessage);
            }
            
            userMergeLogMapper.insert(log);
        } catch (Exception e) {
            // 只记录日志，不抛出异常，避免影响主流程
            this.log.error("记录账号合并日志失败", e);
        }
    }

    /**
     * 根据微信openid获取用户
     */
    private User getUserByOpenId(String openid) {
        if (openid == null || openid.isEmpty()) {
            return null;
        }
        
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getOpenid, openid);
        return this.getOne(queryWrapper);
    }

    /**
     * 重置密码（忘记密码）
     * 通过手机验证码验证身份，然后重置密码
     */
    @Override
    @Transactional
    public void resetPassword(ResetPasswordDTO resetPasswordDTO) {
        log.info("开始处理重置密码请求，手机号: {}", resetPasswordDTO.getPhone());
        
        try {
            // 验证验证码
            String phone = resetPasswordDTO.getPhone();
            String verifyCode = resetPasswordDTO.getVerifyCode();
            
            // 使用统一验证码服务验证
            if (!verifyCodeService.validateVerifyCode(phone, verifyCode)) {
                log.warn("{}的验证码错误或已过期: {}", phone, verifyCode);
                throw new BusinessException("验证码错误或已过期");
            }
            
            // 根据手机号查询用户
            LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(User::getPhone, phone);
            User user = this.getOne(queryWrapper);
            
            if (user == null) {
                log.warn("用户不存在: {}", phone);
                throw new BusinessException("用户不存在");
            }
            
            log.info("用户信息查询成功: {}", user.getUsername());
            
            // 检查用户状态
            if (user.getStatus() == null || user.getStatus() != 1) {
                log.warn("账号已被禁用: {}", user.getUsername());
                throw new BusinessException("账号已被禁用");
            }
            
            // 密码加密
            String encryptedPassword = DigestUtils.md5DigestAsHex(resetPasswordDTO.getNewPassword().getBytes());
            user.setPassword(encryptedPassword);
            user.setUpdateTime(LocalDateTime.now());
            
            // 更新用户
            this.updateById(user);
            
            // 验证码使用后删除
            verifyCodeService.deleteVerifyCode(phone);
            
            log.info("用户密码重置成功: {}", user.getUsername());
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("用户重置密码失败", e);
            throw new BusinessException("重置密码失败: " + e.getMessage());
        }
    }
} 