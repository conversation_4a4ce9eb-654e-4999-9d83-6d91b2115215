package com.repair.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 地址信息视图对象
 */
@Data
public class AddressVO {
    /**
     * 纬度
     */
    private BigDecimal latitude;
    
    /**
     * 经度
     */
    private BigDecimal longitude;
    
    /**
     * 省份
     */
    private String province;
    
    /**
     * 城市
     */
    private String city;
    
    /**
     * 区县
     */
    private String district;
    
    /**
     * 街道
     */
    private String street;
    
    /**
     * 门牌号
     */
    private String streetNumber;
    
    /**
     * 详细地址
     */
    private String detailAddress;
    
    /**
     * 格式化地址
     */
    private String formattedAddress;
    
    /**
     * 定位精度
     */
    private BigDecimal accuracy;
}
