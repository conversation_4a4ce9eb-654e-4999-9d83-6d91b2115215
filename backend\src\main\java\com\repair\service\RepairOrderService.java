package com.repair.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.repair.entity.RepairOrder;
import com.repair.vo.OrderDetailVO;

public interface RepairOrderService extends IService<RepairOrder> {
    /**
     * 获取订单详情
     * @param orderId 订单ID
     * @param repairerId 维修师ID
     * @return 订单详情
     */
    OrderDetailVO getOrderDetail(Long orderId, Long repairerId);
    
    /**
     * 接单
     * @param orderId 订单ID
     * @param repairerId 维修师ID
     */
    void acceptOrder(Long orderId, Long repairerId);
    
    /**
     * 取消接单
     * @param orderId 订单ID
     * @param repairerId 维修师ID
     */
    void cancelOrder(Long orderId, Long repairerId);
    
    /**
     * 开始处理订单
     * @param orderId 订单ID
     * @param repairerId 维修师ID
     */
    void processOrder(Long orderId, Long repairerId);
    
    /**
     * 完成订单
     * @param orderId 订单ID
     * @param repairerId 维修师ID
     */
    void completeOrder(Long orderId, Long repairerId);
} 