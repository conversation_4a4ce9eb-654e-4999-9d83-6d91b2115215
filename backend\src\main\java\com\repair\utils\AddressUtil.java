package com.repair.utils;

import com.repair.dto.AddressDTO;
import com.repair.vo.AddressVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 地址处理工具类
 */
@Component
@Slf4j
public class AddressUtil {
    
    /**
     * 将AddressDTO转换为实体字段
     */
    public static void copyAddressToEntity(AddressDTO addressDTO, Object entity) {
        if (addressDTO == null) return;

        try {
            // 使用反射设置地址字段
            Class<?> entityClass = entity.getClass();

            setFieldValue(entity, entityClass, "latitude", addressDTO.getLatitude());
            setFieldValue(entity, entityClass, "longitude", addressDTO.getLongitude());
            setFieldValue(entity, entityClass, "province", addressDTO.getProvince());
            setFieldValue(entity, entityClass, "city", addressDTO.getCity());
            setFieldValue(entity, entityClass, "district", addressDTO.getDistrict());
            setFieldValue(entity, entityClass, "street", addressDTO.getStreet());
            setFieldValue(entity, entityClass, "detailAddress", addressDTO.getDetailAddress());
            setFieldValue(entity, entityClass, "formattedAddress", addressDTO.getFormattedAddress());
        } catch (Exception e) {
            log.error("地址信息复制失败", e);
        }
    }
    
    /**
     * 将实体字段转换为AddressVO
     */
    public static AddressVO convertEntityToAddressVO(Object entity) {
        if (entity == null) return null;
        
        AddressVO addressVO = new AddressVO();
        try {
            Class<?> entityClass = entity.getClass();
            
            addressVO.setLatitude(getFieldValue(entity, entityClass, "latitude", BigDecimal.class));
            addressVO.setLongitude(getFieldValue(entity, entityClass, "longitude", BigDecimal.class));
            addressVO.setProvince(getFieldValue(entity, entityClass, "province", String.class));
            addressVO.setCity(getFieldValue(entity, entityClass, "city", String.class));
            addressVO.setDistrict(getFieldValue(entity, entityClass, "district", String.class));
            addressVO.setStreet(getFieldValue(entity, entityClass, "street", String.class));
            addressVO.setDetailAddress(getFieldValue(entity, entityClass, "detailAddress", String.class));
            addressVO.setFormattedAddress(getFieldValue(entity, entityClass, "formattedAddress", String.class));
        } catch (Exception e) {
            log.error("地址信息转换失败", e);
        }
        return addressVO;
    }
    
    /**
     * 验证地址信息完整性
     */
    public static boolean validateAddress(AddressDTO addressDTO) {
        if (addressDTO == null) return false;

        // 检查必要字段
        return StringUtils.hasText(addressDTO.getFormattedAddress()) &&
               addressDTO.getLatitude() != null &&
               addressDTO.getLongitude() != null &&
               StringUtils.hasText(addressDTO.getProvince()) &&
               StringUtils.hasText(addressDTO.getCity()) &&
               validateCoordinateRange(addressDTO.getLatitude(), addressDTO.getLongitude());
    }
    
    /**
     * 验证坐标范围
     */
    private static boolean validateCoordinateRange(BigDecimal latitude, BigDecimal longitude) {
        if (latitude == null || longitude == null) return false;
        
        return latitude.compareTo(new BigDecimal("-90")) >= 0 &&
               latitude.compareTo(new BigDecimal("90")) <= 0 &&
               longitude.compareTo(new BigDecimal("-180")) >= 0 &&
               longitude.compareTo(new BigDecimal("180")) <= 0;
    }
    
    /**
     * 限制坐标精度（防止过于精确的位置泄露）
     */
    public static BigDecimal limitCoordinatePrecision(BigDecimal coordinate) {
        if (coordinate == null) return null;
        // 限制小数点后最多6位（约1米精度）
        return coordinate.setScale(6, RoundingMode.HALF_UP);
    }
    
    /**
     * 地址脱敏处理（用于日志等）
     */
    public static String maskFormattedAddress(String formattedAddress) {
        if (!StringUtils.hasText(formattedAddress)) return "";

        if (formattedAddress.length() <= 6) {
            return formattedAddress.substring(0, 1) + "***" + formattedAddress.substring(formattedAddress.length() - 1);
        } else {
            return formattedAddress.substring(0, 3) + "***" + formattedAddress.substring(formattedAddress.length() - 3);
        }
    }
    
    // 私有辅助方法
    private static void setFieldValue(Object entity, Class<?> entityClass, String fieldName, Object value) {
        try {
            Field field = entityClass.getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(entity, value);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            // 字段不存在或无法访问，忽略
        }
    }
    
    @SuppressWarnings("unchecked")
    private static <T> T getFieldValue(Object entity, Class<?> entityClass, String fieldName, Class<T> returnType) {
        try {
            Field field = entityClass.getDeclaredField(fieldName);
            field.setAccessible(true);
            return (T) field.get(entity);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            return null;
        }
    }
}
