package com.repair.config;

import com.repair.utils.ApplicationContextProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.data.redis.connection.RedisConnectionCommands;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Redis连接检查器
 * 在应用启动时检查Redis连接是否正常
 */
@Component
@Slf4j
public class RedisConnectionChecker implements ApplicationRunner {

    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate redisTemplate;
    
    @Value("${spring.redis.host:localhost}")
    private String redisHost;
    
    @Value("${spring.redis.port:6379}")
    private int redisPort;
    
    @Value("${app.redis.check-on-startup:true}")
    private boolean checkOnStartup;

    @Value("${app.redis.fallback-to-memory:true}")
    private boolean fallbackToMemory;
    
    @Override
    public void run(ApplicationArguments args) throws Exception {
        if (!checkOnStartup) {
            log.info("Redis连接检查已禁用");
            return;
        }
        
        log.info("正在检查Redis连接... [{}:{}]", redisHost, redisPort);
        
        try {
            // 尝试执行PING命令
            String result = redisTemplate.execute(RedisConnectionCommands::ping);
            if ("PONG".equalsIgnoreCase(result)) {
                log.info("Redis连接正常");
            } else {
                String errorMsg = String.format("Redis连接异常，响应: %s", result);
                log.error(errorMsg);
                throw new RuntimeException(errorMsg);
            }
        } catch (Exception e) {
            String errorMsg = String.format(
                "无法连接到Redis服务器 [%s:%d]，错误: %s", 
                redisHost, redisPort, e.getMessage()
            );
            log.error(errorMsg);
            log.error("=================================================================");
            log.error("Redis连接失败！请检查：");
            log.error("1. Redis服务是否已启动");
            log.error("2. application.yml中Redis配置是否正确");
            log.error("3. 如果是远程Redis，检查网络连接和防火墙设置");
            log.error("=================================================================");

            // 如果配置了内存降级，则不抛出异常，而是记录错误日志
            if(fallbackToMemory){
                log.info("redis服务不可用，使用内存降级，应用将继续启动");
                return;
            }

            // 停止应用
            SpringApplication.exit(ApplicationContextProvider.getApplicationContext(), () -> 1);
            System.exit(1);
        }
    }
} 