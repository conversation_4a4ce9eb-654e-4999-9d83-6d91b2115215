package com.repair.interceptor;

import com.repair.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
public class JwtInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 如果不是映射到方法，直接通过
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        
        // 获取token（支持多种方式）
        String token = extractToken(request);
        
        // 如果token为空，返回未认证错误
        if (!StringUtils.hasText(token)) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.getWriter().write("Unauthorized: No token provided");
            return false;
        }
        
        try {
            // 清除可能存在的空格
            token = token.trim();
            
            if (token.isEmpty()) {
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.getWriter().write("Unauthorized: Empty token");
                return false;
            }
            
            // 验证token
            if (!JwtUtil.validateToken(token)) {
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.getWriter().write("Unauthorized: Invalid token");
                return false;
            }
            
            // 获取用户ID，可以存储到request属性中供后续使用
            Long userId = JwtUtil.getUserIdFromToken(token);
            request.setAttribute("userId", userId);
            
            // 获取用户角色
            String role = JwtUtil.getRoleFromToken(token);
            if (role == null) {
                log.warn("用户 {} 的令牌中未包含角色信息", userId);
                response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                response.getWriter().write("Forbidden: No role information in token");
                return false;
            }
            request.setAttribute("userRole", role);
            
            // 基于URL路径的角色权限检查
            String requestPath = request.getRequestURI();
            
            // 管理员接口检查
            if (requestPath.startsWith("/api/admin/")) {
                if (!"admin".equals(role)) {
                    log.warn("用户 {} 尝试以 {} 角色访问管理员接口: {}", userId, role, requestPath);
                    response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                    response.getWriter().write("Forbidden: Require admin role");
                    return false;
                }
            }
            
            // 维修师接口检查
            if (requestPath.startsWith("/api/repairer/")) {
                if (!"repairer".equals(role) && !"admin".equals(role)) {
                    log.warn("用户 {} 尝试以 {} 角色访问维修师接口: {}", userId, role, requestPath);
                    response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                    response.getWriter().write("Forbidden: Require repairer role");
                    return false;
                }
            }
            
            // 用户接口检查（包括订单接口）
            if ((requestPath.startsWith("/api/user/") || requestPath.startsWith("/api/order/")) 
                    && !requestPath.endsWith("/login") && !requestPath.endsWith("/register") 
                    && !requestPath.endsWith("/wxLogin") && !requestPath.endsWith("/wxBindPhone") 
                    && !requestPath.endsWith("/verifyCode") && !requestPath.endsWith("/resetPassword")) {
                if (!"user".equals(role) && !"admin".equals(role)) {
                    log.warn("用户 {} 尝试以 {} 角色访问用户接口: {}", userId, role, requestPath);
                    response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                    response.getWriter().write("Forbidden: Require user role");
                    return false;
                }
            }
            
            log.info("用户 {} (角色: {}) 请求接口: {}", userId, role, request.getRequestURI());
            
            return true;
        } catch (Exception e) {
            log.error("Token验证异常: {}", e.getMessage(), e);
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.getWriter().write("Unauthorized: " + e.getMessage());
            return false;
        }
    }

    /**
     * 从请求中提取token，支持多种方式：
     * 1. Authorization header
     * 2. X-Token header (小程序)
     * 3. URL参数中的token
     */
    private String extractToken(HttpServletRequest request) {
        // 1. 尝试从Authorization header获取
        String token = request.getHeader("Authorization");
        if (StringUtils.hasText(token)) {
            if (token.startsWith("Bearer ")) {
                return token.substring(7);
            }
            return token;
        }
        
        // 2. 尝试从X-Token header获取（小程序）
        token = request.getHeader("X-Token");
        if (StringUtils.hasText(token)) {
            return token;
        }
        
        // 3. 尝试从URL参数获取
        token = request.getParameter("token");
        if (StringUtils.hasText(token)) {
            return token;
        }
        
        return null;
    }
} 