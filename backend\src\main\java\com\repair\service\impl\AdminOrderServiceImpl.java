package com.repair.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.repair.entity.RepairOrder;
import com.repair.entity.User;
import com.repair.entity.Repairer;
import com.repair.mapper.RepairOrderMapper;
import com.repair.mapper.UserMapper;
import com.repair.mapper.RepairerMapper;
import com.repair.service.AdminOrderService;
import com.repair.vo.OrderVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.repair.exception.BusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.repair.enums.OrderStatusEnum;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 管理员订单服务实现类
 */
@Service
public class AdminOrderServiceImpl extends ServiceImpl<RepairOrderMapper, RepairOrder> implements AdminOrderService {

    private final UserMapper userMapper;
    private final RepairerMapper repairerMapper;
    private static final Logger log = LoggerFactory.getLogger(AdminOrderServiceImpl.class);

    public AdminOrderServiceImpl(UserMapper userMapper, RepairerMapper repairerMapper) {
        this.userMapper = userMapper;
        this.repairerMapper = repairerMapper;
    }

    @Override
    public Page<OrderVO> getOrderList(Integer status, Page<RepairOrder> page) {
        // 构建查询条件
        LambdaQueryWrapper<RepairOrder> queryWrapper = new LambdaQueryWrapper<>();
        
        // 如果指定了状态且不为-1，则按状态过滤
        if (status != null && status != -1) {
            queryWrapper.eq(RepairOrder::getStatus, status);
        }
        
        // 按创建时间倒序排序
        queryWrapper.orderByDesc(RepairOrder::getCreateTime);
        
        // 分页查询
        Page<RepairOrder> orderPage = this.page(page, queryWrapper);
        
        // 转换为VO
        List<OrderVO> voList = new ArrayList<>();
        for (RepairOrder order : orderPage.getRecords()) {
            // 获取用户名
            User user = userMapper.selectById(order.getUserId());
            String username = user != null ? user.getUsername() : "";
            String userPhone = user != null ? user.getPhone() : "";
            
            // 获取维修师傅名
            String repairerName = "";
            String repairerPhone = "";
            if (order.getRepairerId() != null) {
                // 查询维修师信息
                Repairer repairer = repairerMapper.selectById(order.getRepairerId());
                if (repairer != null) {
                    repairerName = repairer.getUsername();
                    repairerPhone = repairer.getPhone();
                }
            }
            
            voList.add(convertToVO(order, username, repairerName, userPhone, repairerPhone));
        }
        
        // 组装分页结果
        Page<OrderVO> voPage = new Page<>();
        BeanUtils.copyProperties(orderPage, voPage, "records");
        voPage.setRecords(voList);
        
        return voPage;
    }

    @Override
    public OrderVO getOrderDetail(Long orderId) {
        RepairOrder order = this.getById(orderId);
        if (order == null) {
            log.warn("订单不存在: {}", orderId);
            throw new BusinessException("订单不存在");
        }
        
        // 获取用户名
        User user = userMapper.selectById(order.getUserId());
        String username = user != null ? user.getUsername() : "";
        String userPhone = user != null ? user.getPhone() : "";
        
        // 获取维修师傅名
        String repairerName = "";
        String repairerPhone = "";
        if (order.getRepairerId() != null) {
            // 查询维修师信息
            Repairer repairer = repairerMapper.selectById(order.getRepairerId());
            if (repairer != null) {
                repairerName = repairer.getUsername();
                repairerPhone = repairer.getPhone();
            }
        }
        
        return convertToVO(order, username, repairerName, userPhone, repairerPhone);
    }

    @Override
    @Transactional
    public OrderVO assignOrder(Long orderId, Long repairerId) {
        // 获取订单信息
        RepairOrder order = this.getById(orderId);
        if (order == null) {
            log.warn("订单不存在: {}", orderId);
            throw new BusinessException("订单不存在");
        }
        
        // 检查订单状态
        if (order.getStatus() != OrderStatusEnum.PENDING.getCode()) {
            log.warn("订单状态不是待处理，无法分配: 订单ID={}, 当前状态={}", orderId, order.getStatus());
            throw new BusinessException("订单状态不是待处理，无法分配");
        }
        
        // 检查维修人员是否存在
        Repairer repairer = repairerMapper.selectById(repairerId);
        if (repairer == null) {
            log.warn("维修人员不存在: {}", repairerId);
            throw new BusinessException("维修人员不存在");
        }
        
        // 更新订单状态
        order.setRepairerId(repairerId)
             .setStatus(OrderStatusEnum.ACCEPTED.getCode()) // 已接单
             .setUpdateTime(LocalDateTime.now());
        this.updateById(order);
        
        // 获取用户名
        User user = userMapper.selectById(order.getUserId());
        String username = user != null ? user.getUsername() : "";
        String userPhone = user != null ? user.getPhone() : "";
        
        // 获取维修师手机号
        String repairerPhone = "";
        if (repairer != null) {
            repairerPhone = repairer.getPhone();
        }
        
        return convertToVO(order, username, repairer.getUsername(), userPhone, repairerPhone);
    }

    @Override
    @Transactional
    public OrderVO cancelOrder(Long orderId, String reason) {
        // 获取订单信息
        RepairOrder order = this.getById(orderId);
        if (order == null) {
            log.warn("订单不存在: {}", orderId);
            throw new BusinessException("订单不存在");
        }
        
        // 检查订单状态
        if (order.getStatus() == OrderStatusEnum.COMPLETED.getCode() || order.getStatus() == OrderStatusEnum.CANCELED.getCode()) {
            log.warn("已完成或已取消的订单不能取消: 订单ID={}, 当前状态={}", orderId, order.getStatus());
            throw new BusinessException("已完成或已取消的订单不能取消");
        }
        
        // 更新订单状态
        order.setStatus(OrderStatusEnum.CANCELED.getCode()) // 已取消
             .setUpdateTime(LocalDateTime.now());
        this.updateById(order);
        
        // 可以在这里记录取消原因到日志表，此处简化不做处理
        
        // 获取用户名
        User user = userMapper.selectById(order.getUserId());
        String username = user != null ? user.getUsername() : "";
        String userPhone = user != null ? user.getPhone() : "";
        
        // 获取维修师傅名和手机号
        String repairerName = "";
        String repairerPhone = "";
        if (order.getRepairerId() != null) {
            Repairer repairer = repairerMapper.selectById(order.getRepairerId());
            if (repairer != null) {
                repairerName = repairer.getUsername();
                repairerPhone = repairer.getPhone();
            }
        }
        
        return convertToVO(order, username, repairerName, userPhone, repairerPhone);
    }
    
    /**
     * 将订单实体转换为视图对象
     */
    private OrderVO convertToVO(RepairOrder order, String username, String repairerName, String userPhone, String repairerPhone) {
        OrderVO vo = new OrderVO();
        BeanUtils.copyProperties(order, vo);
        vo.setUsername(username);
        vo.setRepairerName(repairerName);
        vo.setUserPhone(userPhone);
        vo.setRepairerPhone(repairerPhone);
        
        // 设置状态描述
        vo.setStatusDesc(OrderStatusEnum.getDescByCode(order.getStatus()));
        
        return vo;
    }
} 