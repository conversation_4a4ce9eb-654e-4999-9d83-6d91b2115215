<template>
  <view class="container">
    <!-- 顶部品牌区域 -->
    <view class="brand-header" :class="{ 'scrolled': isScrolled }">
      <view class="logo-container">
        <view class="logo-icon">
          <text class="icon-outline">🛠️</text>
        </view>
        <view class="logo-text">
          <text class="title">快修侠</text>
          <text class="subtitle">专业的维修服务平台</text>
        </view>
      </view>
      <!-- 消息通知图标 -->
      <view class="notification-icon" @click="showCommingSoon()">
        <text class="iconfont icon-notification"></text>
      </view>
    </view>

    <!-- 页面内容滚动区域 -->
    <scroll-view class="content-scroll" scroll-y @scroll="handleScroll" scroll-with-animation :scroll-top="scrollTop" @scrolltoupper="onScrollToUpper">
    <!-- 轮播图区域 -->
    <view class="banner-container">
      <!-- 加载中状态 -->
      <view v-if="bannersLoading" class="banner-loading">
        <text class="loading-text">轮播加载中...</text>
      </view>

      <!-- 加载失败状态 -->
      <view v-else-if="bannersError" class="banner-error">
        <text class="error-text">轮播加载失败</text>
      </view>

      <!-- 正常轮播 -->
      <swiper v-else-if="banners.length > 0" class="banner-swiper" indicator-dots autoplay interval="5000" duration="500" circular
        indicator-color="rgba(255, 255, 255, 0.4)" indicator-active-color="#ffffff">
        <swiper-item v-for="(banner, index) in banners" :key="banner.id">
          <view class="banner-item" :class="`banner-${(index % 3) + 1}`">
            <image :src="banner.imageUrl" mode="aspectFit" class="banner-bg-image"></image>
            <!-- 只在有文案时显示遮罩层 -->
            <view
              v-if="banner.title || banner.description"
              class="banner-overlay"
              :class="`banner-overlay-${(index % 3) + 1}`"
            ></view>
            <view class="banner-content" v-if="banner.title || banner.description">
              <text class="banner-title" v-if="banner.title">{{ banner.title }}</text>
              <text class="banner-desc" v-if="banner.description">{{ banner.description }}</text>
            </view>
          </view>
        </swiper-item>
      </swiper>

      <!-- 无数据状态 -->
      <view v-else class="banner-empty">
        <text class="empty-text">暂无轮播内容</text>
      </view>
    </view>

    <!-- 快速入口区域 -->
    <view class="quick-access">
      <view class="section-title">
        <text class="section-text">快速入口</text>
        <view class="section-line"></view>
      </view>
      <view class="quick-grid">
        <view class="quick-item" @click="navigateTo('/pages/user/create-order')">
            <view class="quick-icon repair-icon">📝</view>
          <text class="quick-text">家电维修报修</text>
        </view>
        <view class="quick-item" @click="navigateTo('/pages/user/order-list')">
          <view class="quick-icon">📋</view>
          <text class="quick-text">维修订单查询</text>
        </view>
        <view class="quick-item" @click="showCommingSoon()">
          <view class="quick-icon">📞</view>
          <text class="quick-text">联系客服</text>
        </view>
        <view class="quick-item" @click="showCommingSoon()">
          <view class="quick-icon">📊</view>
          <text class="quick-text">服务评价</text>
        </view>
      </view>
    </view>

      <!-- 服务分类区域 -->
      <view class="service-category">
      <view class="section-title">
          <text class="section-text">服务分类</text>
        <view class="section-line"></view>
      </view>
        <view class="category-grid">
          <view class="category-item" v-for="(item, index) in serviceCategories" :key="index" @click="navigateTo('/pages/user/create-order')">
            <view class="category-icon" :style="{ background: item.bgColor }">
              <text class="category-emoji">{{ item.icon }}</text>
            </view>
            <text class="category-text">{{ item.name }}</text>
        </view>
      </view>
    </view>

    <!-- 维修项目 -->
    <view class="services-section">
      <view class="section-title">
        <text class="section-text">维修项目</text>
        <view class="section-line"></view>
      </view>

      <!-- 加载中状态 -->
      <view v-if="projectsLoading" class="projects-loading">
        <text class="loading-text">维修项目加载中...</text>
      </view>

      <!-- 加载失败状态 -->
      <view v-else-if="projectsError" class="projects-error">
        <text class="error-text">维修项目加载失败</text>
      </view>

      <!-- 正常项目列表 -->
      <view v-else-if="repairProjects.length > 0" class="services-grid">
        <view
          class="service-item"
          v-for="project in repairProjects"
          :key="project.id"
          @click="navigateToProjectDetail(project.id)"
        >
          <!-- 背景图片（如果有的话） -->
          <image
            v-if="project.backgroundImage"
            :src="project.backgroundImage"
            mode="aspectFill"
            class="service-bg-image"
          ></image>

          <!-- 内容区域 -->
          <view class="service-content" :class="{ 'has-bg': project.backgroundImage }">
            <!-- 图标显示 -->
            <view class="service-icon" v-if="project.iconType === 'emoji' || !project.iconType">
              {{ project.iconValue || '🔧' }}
            </view>
            <image
              v-else-if="project.iconType === 'image' && project.iconValue"
              :src="project.iconValue"
              mode="aspectFit"
              class="service-icon-image"
            ></image>
            <view class="service-icon" v-else>🔧</view>

            <text class="service-name">{{ project.name }}</text>
            <text class="service-desc">{{ project.description || '' }}</text>
          </view>
        </view>
      </view>

      <!-- 无数据状态 -->
      <view v-else class="projects-empty">
        <text class="empty-text">暂无维修项目</text>
      </view>
    </view>

    <!-- 服务特色 -->
    <view class="features-section">
      <view class="section-title">
        <text class="section-text">服务特色</text>
        <view class="section-line"></view>
      </view>
      <view class="features-list">
        <view class="feature-item">
            <view class="feature-icon" style="color: #2e4e9e; background: rgba(46, 78, 158, 0.08);">
            <image :src="featureImages.fastResponse" mode="aspectFit" class="feature-image"></image>
          </view>
          <view class="feature-content">
            <text class="feature-title">快速响应</text>
            <text class="feature-desc">提交家电维修订单后，15分钟内安排专业师傅联系</text>
          </view>
        </view>
        <view class="feature-item">
            <view class="feature-icon" style="color: #2e4e9e; background: rgba(46, 78, 158, 0.08);">
            <image :src="featureImages.qualityAssurance" mode="aspectFit" class="feature-image"></image>
          </view>
          <view class="feature-content">
            <text class="feature-title">品质保障</text>
            <text class="feature-desc">严格筛选维修师傅，保证维修质量</text>
          </view>
        </view>
        <view class="feature-item">
            <view class="feature-icon" style="color: #2e4e9e; background: rgba(46, 78, 158, 0.08);">
            <image :src="featureImages.afterSales" mode="aspectFit" class="feature-image"></image>
          </view>
          <view class="feature-content">
            <text class="feature-title">售后无忧</text>
            <text class="feature-desc">服务30天内免费返修，让您无后顾之忧</text>
          </view>
        </view>
      </view>
    </view>

      <!-- 维修师傅展示区 -->
      <view class="repairers-section">
        <view class="section-title">
          <text class="section-text">优质师傅</text>
          <view class="section-line"></view>
        </view>
        <view class="repairers-list">
          <view class="repairer-item" v-for="(repairer, index) in topRepairers" :key="index" @click="showCommingSoon()">
            <view class="repairer-avatar">{{ repairer.avatar }}</view>
            <view class="repairer-info">
              <text class="repairer-name">{{ repairer.name }}</text>
              <text class="repairer-skill">{{ repairer.skill }}</text>
              <view class="repairer-rating">
                <text class="rating-star" v-for="n in 5" :key="n">⭐</text>
                <text class="rating-score">{{ repairer.rating }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部区域 -->
    <view class="footer">
        <text class="copyright">&copy; 2025 快修侠维修 All rights reserved.</text>
      </view>
      
      <!-- 底部空白区域，用于避免内容被底部导航栏遮挡 -->
      <view class="bottom-space"></view>
    </scroll-view>

    <!-- 回到顶部按钮 -->
    <view class="scroll-top-btn" :class="{ 'visible': showScrollTopBtn }" @click="scrollToTop">
      <text class="scroll-top-icon">↑</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { useUserStore } from '@/store/user';
import { isWxMiniProgram } from '@/utils/wxLogin';
import { userApi } from '@/api/user';
import type { BannerInfo } from '@/api/user';
import { repairProjectApi } from '@/api/repair-project';
import type { RepairProjectInfo } from '@/api/repair-project';
import { onMounted, ref, computed } from 'vue';

// 获取用户Store
const userStore = useUserStore();

// 轮播图数据
const banners = ref<BannerInfo[]>([]);
const bannersLoading = ref(true);
const bannersError = ref(false);

// 维修项目数据
const repairProjects = ref<RepairProjectInfo[]>([]);
const projectsLoading = ref(true);
const projectsError = ref(false);

// WebP支持检测
const isWebpSupported = ref(false);

// 检测WebP支持
function checkWebpSupport() {
  try {
    // 检查是否在浏览器环境
    if (typeof document !== 'undefined') {
      // H5环境，使用canvas检测
      const canvas = document.createElement('canvas');
      canvas.width = 1;
      canvas.height = 1;
      const dataURL = canvas.toDataURL('image/webp');
      isWebpSupported.value = dataURL.indexOf('data:image/webp') === 0;
    } else {
      // 小程序环境，默认支持WebP
      isWebpSupported.value = true;
    }
  } catch (e) {
    // 检测失败，默认不支持
    isWebpSupported.value = false;
  }
}

// 功能特色图片路径（使用计算属性确保响应式更新）
const featureImages = computed(() => ({
  fastResponse: isWebpSupported.value ? '/static/images/features/fast-response.webp' : '/static/images/features/fast-response.png',
  qualityAssurance: isWebpSupported.value ? '/static/images/features/quality-assurance.webp' : '/static/images/features/quality-assurance.png',
  afterSales: isWebpSupported.value ? '/static/images/features/after-sales.webp' : '/static/images/features/after-sales.png'
}));

// 定义路由页面地址
const userPages = {
  login: '/pages/user/login',
  register: '/pages/user/register',
  home: '/pages/user/home',
  createOrder: '/pages/user/create-order',
  orderList: '/pages/user/order-list',
  bindPhone: '/pages/user/bind-phone',
};

// 添加滚动相关状态
const isScrolled = ref(false);
const showScrollTopBtn = ref(false);
const scrollTop = ref(0);

// 处理滚动事件
function handleScroll(e: any) {
  const scrollTop = e.detail.scrollTop;
  // 当滚动超过50px时，将isScrolled设为true，否则为false
  isScrolled.value = scrollTop > 50;
  // 当滚动超过300px时，显示回到顶部按钮
  showScrollTopBtn.value = scrollTop > 300;
}

// 滚动到顶部
function scrollToTop() {
  scrollTop.value = 0;
}

// 当滚动到顶部时的处理
function onScrollToUpper() {
  showScrollTopBtn.value = false;
}

// 需要登录的页面列表
const pagesNeedLogin = [
  '/pages/user/create-order',
  '/pages/user/order-list',
];

// 服务分类数据
const serviceCategories = ref([
  { name: '家电维修', icon: '🔌', bgColor: 'rgba(46, 78, 158, 0.08)' },
  { name: '管道疏通', icon: '🚿', bgColor: 'rgba(46, 78, 158, 0.08)' },
  { name: '电脑维修', icon: '💻', bgColor: 'rgba(46, 78, 158, 0.08)' },
  { name: '手机维修', icon: '📱', bgColor: 'rgba(46, 78, 158, 0.08)' },
  { name: '家具安装', icon: '🪑', bgColor: 'rgba(46, 78, 158, 0.08)' },
  { name: '灯具维修', icon: '💡', bgColor: 'rgba(46, 78, 158, 0.08)' },
  { name: '门锁维修', icon: '🔑', bgColor: 'rgba(46, 78, 158, 0.08)' },
  { name: '更多服务', icon: '➕', bgColor: 'rgba(46, 78, 158, 0.08)' }
]);

// 优质维修师傅数据
const topRepairers = ref([
  { 
    name: '张师傅', 
    avatar: '👨‍🔧', 
    skill: '家电维修专家', 
    rating: '4.9' 
  },
  { 
    name: '李师傅', 
    avatar: '👨‍🔧', 
    skill: '管道疏通专家', 
    rating: '4.8' 
  },
  { 
    name: '王师傅', 
    avatar: '👨‍🔧', 
    skill: '电器维修专家', 
    rating: '4.7' 
  }
]);

// tabBar页面列表
const tabBarPages = [
  '/pages/index/index',
  '/pages/user/order-list',
  '/pages/user/home'
];

function navigateTo(url: string) {
  // 检查是否是tabBar页面
  const isTabBarPage = tabBarPages.includes(url);

  // 需要检查是否需要登录和绑定手机号的页面
  if (pagesNeedLogin.includes(url)) {
    // 检查登录状态
    if (!userStore.isLoggedIn) {
      // 保存跳转目标，登录后可以直接跳转
      uni.setStorageSync('redirect_after_bind_phone', url);

      uni.showToast({
        title: '请先登录后使用此功能',
        icon: 'none',
        duration: 2000
      });
      setTimeout(() => {
        uni.navigateTo({
          url: userPages.login,
          fail(err) {
            console.error('跳转登录页面失败', err);
            // 如果navigateTo失败，尝试redirectTo
            uni.redirectTo({ url: userPages.login });
          }
        });
      }, 500);
      return;
    }

    // 检查是否是微信小程序环境，且用户未绑定手机号
    if (isWxMiniProgram() && userStore.isLoggedIn && (!userStore.userInfo?.phone || userStore.userInfo?.phone === '')) {
      console.log('微信用户未绑定手机号，跳转到绑定页面');

      // 保存用户想要访问的原始页面
      uni.setStorageSync('redirect_after_bind_phone', url);

      uni.showToast({
        title: '请先绑定手机号后使用此功能',
        icon: 'none',
        duration: 1500
      });

      setTimeout(() => {
        uni.navigateTo({
          url: userPages.bindPhone,
          fail(err) {
            console.error('跳转绑定手机页面失败', err);
            // 如果navigateTo失败，尝试redirectTo
            uni.redirectTo({
              url: userPages.bindPhone,
              fail(e) {
                console.error('重定向到绑定手机页面也失败', e);
                uni.showToast({
                  title: '页面跳转失败，请稍后再试',
                  icon: 'none'
                });
              }
            });
          }
        });
      }, 500);
      return;
    }
  }

  // 对用户登录页面使用特殊处理
  if (url === userPages.login) {
    console.log('准备跳转到用户登录页面');

    // 在小程序环境下使用reLaunch来确保稳定导航
    if (isWxMiniProgram()) {
      uni.showLoading({
        title: '加载中...',
        mask: true
      });

      setTimeout(() => {
        uni.reLaunch({
          url,
          success() {
            console.log('成功跳转到登录页面');
            uni.hideLoading();
          },
          fail(err) {
            console.error('使用reLaunch跳转到登录页面失败', err);
            uni.hideLoading();

            // 提示用户
            uni.showToast({
              title: '页面跳转失败，请重启应用',
              icon: 'none',
              duration: 2000
            });
          }
        });
      }, 200);
    } else {
      // 非小程序环境使用常规跳转
      uni.navigateTo({
        url,
        fail(err) {
          console.error('页面跳转失败', err);
          uni.redirectTo({ url });
        }
      });
    }
    return;
  }

  // 如果是tabBar页面，使用switchTab
  if (isTabBarPage) {
    uni.switchTab({
      url,
      fail(err) {
        console.error('切换tabBar页面失败', err);
        uni.showToast({
          title: '页面跳转失败，请稍后再试',
          icon: 'none'
        });
      }
    });
    return;
  }

  // 正常导航
  uni.navigateTo({
    url,
    fail(err) {
      console.error('页面跳转失败', err);

      // 如果navigateTo失败，尝试redirectTo
      uni.redirectTo({
        url,
        fail(e) {
          console.error('重定向也失败', e);
          uni.showToast({
            title: '页面跳转失败，请稍后再试',
            icon: 'none'
          });
        }
      });
    }
  });
}

function showCommingSoon() {
  uni.showToast({
    title: '功能即将上线',
    icon: 'none',
    duration: 2000
  });
}

// 跳转到维修项目详情页
function navigateToProjectDetail(projectId: number) {
  uni.navigateTo({
    url: `/pages/repair-project/detail?id=${projectId}`,
    fail(err) {
      console.error('跳转维修项目详情页失败', err);
      uni.showToast({
        title: '页面跳转失败，请稍后再试',
        icon: 'none'
      });
    }
  });
}

// 获取轮播图数据
async function loadBanners() {
  try {
    bannersLoading.value = true;
    bannersError.value = false;

    const response = await userApi.getBannerList();
    banners.value = response.data || [];

    console.log('轮播图加载成功，数量：', banners.value.length);
  } catch (error) {
    console.error('轮播图加载失败：', error);
    bannersError.value = true;

    // 显示错误提示
    uni.showToast({
      title: '轮播图加载失败',
      icon: 'none',
      duration: 2000
    });
  } finally {
    bannersLoading.value = false;
  }
}

// 获取维修项目数据
async function loadRepairProjects() {
  try {
    projectsLoading.value = true;
    projectsError.value = false;

    const response = await repairProjectApi.getEnabledProjects();
    repairProjects.value = response.data || [];

    console.log('维修项目加载成功，数量：', repairProjects.value.length);
  } catch (error) {
    console.error('维修项目加载失败：', error);
    projectsError.value = true;

    // 显示错误提示
    uni.showToast({
      title: '维修项目加载失败',
      icon: 'none',
      duration: 2000
    });
  } finally {
    projectsLoading.value = false;
  }
}

// 在页面加载时检查用户登录状态
onMounted(() => {
  // 检测WebP支持
  checkWebpSupport();

  console.log('首页加载完成，检查登录状态:');
  console.log('用户token:', uni.getStorageSync('token'));
  console.log('维修师token:', uni.getStorageSync('repairer_token'));
  console.log('管理员token:', uni.getStorageSync('admin_token'));
  console.log('用户store状态:', { token: userStore.token, isLoggedIn: userStore.isLoggedIn });

  // 加载轮播图数据
  loadBanners();

  // 加载维修项目数据
  loadRepairProjects();

  if (userStore.isLoggedIn && !userStore.userInfo) {
    console.log('用户已登录但无用户信息，获取用户信息');
    userStore.getUserInfo().catch(err => {
      console.error('获取用户信息失败', err);
    });
  }

  // 在H5环境下，输出一些环境调试信息，以便排查问题
  // #ifdef H5
  console.log('当前环境: H5');
  console.log('当前BASE_URL:', window.location.origin);
  console.log('API测试:');
  
  // 通过fetch API测试后端连接性
  fetch('/api/heartbeat')
    .then(res => {
      console.log('API心跳检测结果:', res.status, res.statusText);
      return res.text();
    })
    .then(text => {
      console.log('API心跳返回:', text);
    })
    .catch(err => {
      console.error('API心跳检测失败:', err);
    });
  // #endif
});
</script>

<style>
.container {
  min-height: 100vh;
  background-color: #f8f9fa;
  position: relative;
}

/* 内容滚动区域 */
.content-scroll {
  height: 100vh;
  padding-top: calc(var(--status-bar-height) + 90rpx); /* 为顶部导航预留空间 */
}

/* 品牌区域样式 */
.brand-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: rgba(46, 78, 158, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: 30rpx 24rpx;
  padding-top: calc(var(--status-bar-height) + 30rpx); /* 添加状态栏高度 */
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.brand-header.scrolled {
  background: rgba(46, 78, 158, 0.98);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.12);
  padding-top: calc(var(--status-bar-height) + 20rpx);
  padding-bottom: 20rpx;
}

.logo-container {
  display: flex;
  align-items: center;
  flex: 0 0 auto;
}

.logo-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  margin-right: 16rpx;
  position: relative;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.icon-outline {
  position: relative;
  z-index: 2;
}

.logo-text {
  flex: 1;
}

.title {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 4rpx;
  display: block;
  letter-spacing: 0.5px;
}

.subtitle {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  letter-spacing: 0.5px;
}

/* 通知图标样式 */
.notification-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.notification-icon .icon-notification {
  font-size: 36rpx;
  color: #ffffff;
}

/* 轮播图区域样式 */
.banner-container {
  margin: 70rpx 34rpx 10rpx 34rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  position: relative;
  transition: transform 0.3s ease;
}

.banner-container:active {
  transform: scale(0.99);
}

.banner-swiper {
  height: 384rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

.banner-item {
  height: 100%;
  display: flex;
  align-items: flex-start; /* 改为顶部对齐 */
  padding: 60rpx 40rpx 40rpx 40rpx; /* 增加顶部padding，让内容往上移动 */
  position: relative;
  overflow: hidden;
}

.banner-bg-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  background-color: #ffffff; /* 使用白色背景，更清晰 */
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.banner-overlay-1 {
  background: linear-gradient(135deg, rgba(46, 78, 158, 0.7) 0%, rgba(61, 106, 239, 0.7) 100%);
}

.banner-overlay-2 {
  background: linear-gradient(135deg, rgba(55, 62, 84, 0.7) 0%, rgba(78, 91, 130, 0.7) 100%);
}

.banner-overlay-3 {
  background: linear-gradient(135deg, rgba(43, 76, 111, 0.7) 0%, rgba(69, 132, 197, 0.7) 100%);
}

.banner-content {
  color: #ffffff;
  position: relative;
  z-index: 3;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border-radius: 16rpx;
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 70%;
  transition: transform 0.3s ease;
}

.banner-title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 12rpx;
  letter-spacing: 0.5px;
}

.banner-desc {
  font-size: 24rpx;
  opacity: 0.9;
  letter-spacing: 0.5px;
}

/* 区域标题样式 */
.section-title {
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  margin: 40rpx 0 20rpx;
}

.section-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 16rpx;
  position: relative;
  letter-spacing: 0.5px;
}

.section-text::after {
  content: '';
  position: absolute;
  bottom: -8rpx;
  left: 0;
  width: 40rpx;
  height: 4rpx;
  background: #2e4e9e;
  border-radius: 2rpx;
}

.section-line {
  flex: 1;
  height: 1rpx;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.08), transparent);
  margin-left: 16rpx;
}

/* 快速入口样式 */
.quick-access {
  margin-bottom: 24rpx;
  padding: 0 16rpx;
}

/* 维修项目样式 */
.services-section {
  margin-bottom: 24rpx;
  padding: 0 16rpx;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  margin-top: 24rpx;
}

.service-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 20rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.service-item:hover {
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.12);
  transform: translateY(-2rpx);
}

.service-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.service-icon-image {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 16rpx;
}

.service-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.service-desc {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  line-height: 1.4;
}

/* 维修项目新增样式 */
.service-item {
  position: relative;
  overflow: hidden;
}

.service-bg-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.service-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.service-content.has-bg {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  padding: 24rpx 16rpx;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.service-content.has-bg .service-name,
.service-content.has-bg .service-desc {
  color: #333;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 加载和错误状态样式 */
.projects-loading,
.projects-error,
.projects-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 20rpx;
  margin-top: 24rpx;
}

.loading-text,
.error-text,
.empty-text {
  font-size: 28rpx;
  color: #999;
}

.quick-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 8rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  padding: 12rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border: 1px solid rgba(230, 230, 230, 0.5);
}

.quick-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  position: relative;
  overflow: hidden;
}

.quick-item::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: all 0.3s ease;
}

.quick-item:active::before {
  width: 180rpx;
  height: 180rpx;
  opacity: 1;
}

.quick-icon {
  width: 88rpx;
  height: 88rpx;
  background: #ffffff;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-bottom: 12rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  border: 1px solid rgba(230, 230, 230, 0.8);
}

.repair-icon {
  background: #2e4e9e;
  color: #ffffff;
  border: none;
}

.quick-item:active .quick-icon {
  transform: translateY(4rpx);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.08);
}

.quick-text {
  font-size: 26rpx;
  color: #333;
  letter-spacing: 0.5px;
  font-weight: 500;
}

/* 服务分类样式 */
.service-category {
  margin-bottom: 24rpx;
  padding: 0 16rpx;
}

.category-grid {
  display: flex;
  flex-wrap: wrap;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  padding: 12rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
  margin: 0 8rpx;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border: 1px solid rgba(230, 230, 230, 0.5);
}

.category-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  position: relative;
  overflow: hidden;
}

.category-item::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: all 0.3s ease;
}

.category-item:active::before {
  width: 180rpx;
  height: 180rpx;
  opacity: 1;
}

.category-icon {
  width: 88rpx;
  height: 88rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.06);
  background: #ffffff;
  border: 1px solid rgba(230, 230, 230, 0.8);
  transition: all 0.3s ease;
}

.category-emoji {
  font-size: 40rpx;
}

.category-text {
  font-size: 26rpx;
  color: #333;
  letter-spacing: 0.5px;
  font-weight: 500;
}

/* 服务特色区域样式 */
.features-section {
  padding: 0 24rpx;
  margin-bottom: 24rpx;
}

.features-list {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 16rpx 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(230, 230, 230, 0.5);
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.04);
  transition: background-color 0.3s ease;
}

.feature-item:last-child {
  border-bottom: none;
}

.feature-item:active {
  background-color: rgba(0, 0, 0, 0.02);
}

.feature-icon {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
  font-size: 32rpx;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(230, 230, 230, 0.5);
  padding: 12rpx;
}

.feature-image {
  width: 40rpx;
  height: 40rpx;
}

.feature-content {
  flex: 1;
}

.feature-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 6rpx;
  display: block;
  letter-spacing: 0.5px;
}

.feature-desc {
  font-size: 24rpx;
  color: #666;
  letter-spacing: 0.5px;
}

/* 维修师傅展示区样式 */
.repairers-section {
  padding: 0 24rpx;
  margin-bottom: 32rpx;
}

.repairers-list {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 16rpx 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(230, 230, 230, 0.5);
}

.repairer-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.04);
  transition: background-color 0.3s ease;
}

.repairer-item:last-child {
  border-bottom: none;
}

.repairer-item:active {
  background-color: rgba(0, 0, 0, 0.02);
}

.repairer-avatar {
  width: 88rpx;
  height: 88rpx;
  background: #f7f9fa;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  font-size: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(230, 230, 230, 0.5);
}

.repairer-info {
  flex: 1;
}

.repairer-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  display: block;
  letter-spacing: 0.5px;
}

.repairer-skill {
  font-size: 24rpx;
  color: #666;
  margin: 6rpx 0;
  display: block;
  letter-spacing: 0.5px;
}

.repairer-rating {
  display: flex;
  align-items: center;
}

.rating-star {
  font-size: 22rpx;
  color: #ffce3d;
  margin-right: 2rpx;
}

.rating-score {
  font-size: 24rpx;
  color: #ff9500;
  margin-left: 8rpx;
  font-weight: 600;
}

/* 底部版权区域样式 */
.footer {
  text-align: center;
  padding: 40rpx 0;
}

.copyright {
  font-size: 24rpx;
  color: #888;
  letter-spacing: 0.5px;
}

/* 底部空白区域 */
.bottom-space {
  height: 120rpx;
}

/* 滚动到顶部按钮 */
.scroll-top-btn {
  position: fixed;
  right: 30rpx;
  bottom: 140rpx;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  z-index: 99;
  opacity: 0;
  transform: translateY(20rpx);
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border: 1px solid rgba(230, 230, 230, 0.5);
}

.scroll-top-btn.visible {
  opacity: 1;
  transform: translateY(0);
}

.scroll-top-icon {
  font-size: 36rpx;
  color: #2e4e9e;
}

/* 轮播图加载状态样式 */
.banner-loading,
.banner-error,
.banner-empty {
  height: 384rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
}

.loading-text,
.error-text,
.empty-text {
  font-size: 28rpx;
  color: #666;
}

.error-text {
  color: #ff6b6b;
}
</style>
