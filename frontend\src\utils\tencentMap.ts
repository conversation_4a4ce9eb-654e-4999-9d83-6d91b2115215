import QQMapWX from 'qqmap-wx-jssdk';
import { TENCENT_MAP_CONFIG, ADDRESS_CONFIG } from './config';
import { limitCoordinatePrecision } from './location';
import type { 
  LocationResult, 
  AddressData, 
  TencentMapGeocodeResult, 
  TencentMapSearchResult 
} from '@/types/location';

// 初始化腾讯地图SDK
const qqmapsdk = new QQMapWX({
  key: TENCENT_MAP_CONFIG.key
});

// API调用频率控制器
class APIRateLimiter {
  private lastCallTime = 0;
  private minInterval = ADDRESS_CONFIG.API_MIN_INTERVAL;
  
  async callAPI<T>(apiFunction: () => Promise<T>): Promise<T> {
    const now = Date.now();
    const timeSinceLastCall = now - this.lastCallTime;
    
    if (timeSinceLastCall < this.minInterval) {
      await new Promise(resolve => 
        setTimeout(resolve, this.minInterval - timeSinceLastCall)
      );
    }
    
    this.lastCallTime = Date.now();
    return await apiFunction();
  }
}

const rateLimiter = new APIRateLimiter();

// 地址缓存管理器
class AddressCache {
  private cache = new Map<string, { data: AddressData; timestamp: number }>();
  private readonly CACHE_EXPIRY = ADDRESS_CONFIG.CACHE_EXPIRY;
  
  set(key: string, data: AddressData) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
    
    // 限制缓存大小，删除最旧的条目
    if (this.cache.size > 100) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
  }
  
  get(key: string): AddressData | null {
    const cached = this.cache.get(key);
    if (cached && this.isValidCache(cached)) {
      return cached.data;
    }
    
    // 清理过期缓存
    if (cached) {
      this.cache.delete(key);
    }
    
    return null;
  }
  
  private isValidCache(cached: { data: AddressData; timestamp: number }): boolean {
    return (Date.now() - cached.timestamp) < this.CACHE_EXPIRY;
  }
  
  clear() {
    this.cache.clear();
  }
}

const addressCache = new AddressCache();

/**
 * 逆地址解析 - 根据坐标获取地址信息
 */
export function reverseGeocode(location: LocationResult): Promise<AddressData> {
  return new Promise((resolve, reject) => {
    // 限制坐标精度
    const lat = limitCoordinatePrecision(location.latitude);
    const lng = limitCoordinatePrecision(location.longitude);
    
    // 检查缓存
    const cacheKey = `${lat}_${lng}`;
    const cached = addressCache.get(cacheKey);
    if (cached) {
      resolve(cached);
      return;
    }
    
    // 使用频率控制器调用API
    rateLimiter.callAPI(() => {
      return new Promise<AddressData>((apiResolve, apiReject) => {
        qqmapsdk.reverseGeocoder({
          location: {
            latitude: lat,
            longitude: lng
          },
          get_poi: 0, // 暂时关闭POI信息获取，避免参数错误
          success: (res: TencentMapGeocodeResult) => {
            try {
              if (res.status !== 0) {
                apiReject(new Error(`腾讯地图API错误: ${res.message}`));
                return;
              }

              const result = res.result;
              const addressComponent = result.address_component;
              
              const addressData: AddressData = {
                province: addressComponent.province || '',
                city: addressComponent.city || '',
                district: addressComponent.district || '',
                street: addressComponent.street || '',
                streetNumber: addressComponent.street_number || '',
                formattedAddress: result.formatted_addresses.recommend || result.address,
                latitude: lat,
                longitude: lng,
                accuracy: location.accuracy
              };
              
              // 缓存结果
              addressCache.set(cacheKey, addressData);
              
              apiResolve(addressData);
            } catch (error) {
              console.error('地址解析结果处理失败:', error);
              apiReject(new Error('地址解析结果处理失败'));
            }
          },
          fail: (error: any) => {
            console.error('腾讯地图逆地址解析失败:', error);
            apiReject(new Error('地址解析失败，请检查网络连接'));
          }
        });
      });
    }).then(resolve).catch(reject);
  });
}

/**
 * 地址搜索 - 根据关键词搜索地址
 */
export function searchAddress(keyword: string, region?: string): Promise<AddressData[]> {
  return new Promise((resolve, reject) => {
    if (!keyword || keyword.trim().length < 2) {
      resolve([]);
      return;
    }

    rateLimiter.callAPI(() => {
      return new Promise<AddressData[]>((apiResolve, apiReject) => {
        qqmapsdk.search({
          keyword: keyword.trim(),
          region: region || '全国',
          page_size: 10,
          page_index: 1,
          success: (res: TencentMapSearchResult) => {
            try {
              if (res.status !== 0) {
                apiReject(new Error(`腾讯地图搜索API错误: ${res.message}`));
                return;
              }

              const addressList: AddressData[] = res.data.map(item => ({
                province: item.ad_info.province || '',
                city: item.ad_info.city || '',
                district: item.ad_info.district || '',
                street: '',
                streetNumber: '',
                formattedAddress: item.address || item.title,
                latitude: limitCoordinatePrecision(item.location.lat),
                longitude: limitCoordinatePrecision(item.location.lng),
                accuracy: 0
              }));

              apiResolve(addressList);
            } catch (error) {
              console.error('搜索结果处理失败:', error);
              apiReject(new Error('搜索结果处理失败'));
            }
          },
          fail: (error: any) => {
            console.error('腾讯地图地址搜索失败:', error);
            apiReject(new Error('地址搜索失败，请检查网络连接'));
          }
        });
      });
    }).then(resolve).catch(reject);
  });
}

/**
 * 正向地理编码 - 根据地址获取坐标信息
 */
export function geocodeAddress(address: string): Promise<AddressData | null> {
  return new Promise((resolve, reject) => {
    if (!address || address.trim().length < 2) {
      resolve(null);
      return;
    }

    // 检查缓存
    const cacheKey = `geocode_${address.trim()}`;
    const cached = addressCache.get(cacheKey);
    if (cached) {
      resolve(cached);
      return;
    }

    rateLimiter.callAPI(() => {
      return new Promise<AddressData | null>((apiResolve, apiReject) => {
        qqmapsdk.geocoder({
          address: address.trim(),
          success: (res: any) => {
            try {
              if (res.status !== 0) {
                console.warn(`腾讯地图地理编码失败: ${res.message}`);
                apiResolve(null);
                return;
              }

              const result = res.result;
              if (!result || !result.location) {
                console.warn('腾讯地图地理编码返回空结果');
                apiResolve(null);
                return;
              }

              const addressData: AddressData = {
                province: result.address_components?.province || '',
                city: result.address_components?.city || '',
                district: result.address_components?.district || '',
                street: result.address_components?.street || '',
                streetNumber: result.address_components?.street_number || '',
                formattedAddress: address,
                latitude: limitCoordinatePrecision(result.location.lat),
                longitude: limitCoordinatePrecision(result.location.lng),
                accuracy: 0
              };

              // 缓存结果
              addressCache.set(cacheKey, addressData);

              apiResolve(addressData);
            } catch (error) {
              console.error('腾讯地图地理编码结果处理失败:', error);
              apiResolve(null);
            }
          },
          fail: (error: any) => {
            console.error('腾讯地图地理编码失败:', error);
            apiResolve(null);
          }
        });
      });
    }).then(resolve).catch(reject);
  });
}

/**
 * 计算两点间距离
 */
export function calculateDistance(
  from: { latitude: number; longitude: number },
  to: { latitude: number; longitude: number }
): Promise<number> {
  return new Promise((resolve, reject) => {
    rateLimiter.callAPI(() => {
      return new Promise<number>((apiResolve, apiReject) => {
        qqmapsdk.calculateDistance({
          from: {
            latitude: limitCoordinatePrecision(from.latitude),
            longitude: limitCoordinatePrecision(from.longitude)
          },
          to: [{
            latitude: limitCoordinatePrecision(to.latitude),
            longitude: limitCoordinatePrecision(to.longitude)
          }],
          success: (res: any) => {
            if (res.status === 0) {
              apiResolve(res.result.elements[0].distance);
            } else {
              apiReject(new Error(`距离计算失败: ${res.message}`));
            }
          },
          fail: (error: any) => {
            console.error('距离计算失败:', error);
            apiReject(new Error('距离计算失败'));
          }
        });
      });
    }).then(resolve).catch(reject);
  });
}

/**
 * 清理地址缓存
 */
export function clearAddressCache(): void {
  addressCache.clear();
}

/**
 * 检查腾讯地图SDK是否可用
 */
export function checkTencentMapAvailable(): boolean {
  return !!TENCENT_MAP_CONFIG.key && TENCENT_MAP_CONFIG.key !== 'your_tencent_map_key_here';
}

/**
 * 格式化距离显示
 * @param distance 距离（米）
 * @returns 格式化的距离字符串
 */
export function formatDistance(distance: number): string {
  if (distance < 1000) {
    return `${Math.round(distance)}m`;
  } else {
    return `${(distance / 1000).toFixed(1)}km`;
  }
}

/**
 * 计算两点间直线距离（简化版，不调用API）
 * 使用Haversine公式计算球面距离
 */
export function calculateStraightDistance(
  from: { latitude: number; longitude: number },
  to: { latitude: number; longitude: number }
): number {
  const R = 6371000; // 地球半径（米）
  const lat1 = from.latitude * Math.PI / 180;
  const lat2 = to.latitude * Math.PI / 180;
  const deltaLat = (to.latitude - from.latitude) * Math.PI / 180;
  const deltaLng = (to.longitude - from.longitude) * Math.PI / 180;

  const a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
    Math.cos(lat1) * Math.cos(lat2) *
    Math.sin(deltaLng / 2) * Math.sin(deltaLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c; // 距离（米）
}
