// 距离计算组合式函数
import { ref, computed } from 'vue';
import { getCurrentLocation } from '@/utils/location';
import { calculateStraightDistance, formatDistance } from '@/utils/tencentMap';
import type { AddressInfo } from '@/utils/addressFormatter';

// 维修师位置缓存
const repairerLocation = ref<{ latitude: number; longitude: number } | null>(null);
const locationLoading = ref(false);
const locationError = ref<string>('');

/**
 * 距离计算组合式函数
 */
export function useDistance() {
  
  /**
   * 获取维修师当前位置
   */
  async function getRepairerLocation(): Promise<{ latitude: number; longitude: number } | null> {
    // 如果已有缓存位置，直接返回
    if (repairerLocation.value) {
      return repairerLocation.value;
    }

    if (locationLoading.value) {
      return null;
    }

    try {
      locationLoading.value = true;
      locationError.value = '';
      
      const location = await getCurrentLocation();
      repairerLocation.value = {
        latitude: location.latitude,
        longitude: location.longitude
      };
      
      console.log('获取维修师位置成功:', repairerLocation.value);
      return repairerLocation.value;
    } catch (error: any) {
      console.error('获取维修师位置失败:', error);
      locationError.value = error.message || '定位失败';
      return null;
    } finally {
      locationLoading.value = false;
    }
  }

  /**
   * 计算到订单地址的距离
   */
  async function calculateOrderDistance(addressInfo?: AddressInfo): Promise<string | null> {
    // 检查订单地址是否有坐标
    if (!addressInfo?.latitude || !addressInfo?.longitude) {
      return null;
    }

    // 获取维修师位置
    const repairerPos = await getRepairerLocation();
    if (!repairerPos) {
      return null;
    }

    try {
      // 计算直线距离
      const distance = calculateStraightDistance(
        repairerPos,
        {
          latitude: addressInfo.latitude,
          longitude: addressInfo.longitude
        }
      );

      return formatDistance(distance);
    } catch (error) {
      console.error('计算距离失败:', error);
      return null;
    }
  }

  /**
   * 清除位置缓存（用于刷新位置）
   */
  function clearLocationCache() {
    repairerLocation.value = null;
    locationError.value = '';
  }

  /**
   * 批量计算多个订单的距离
   */
  async function calculateMultipleDistances(orders: Array<{ id: number; addressInfo?: AddressInfo }>): Promise<Map<number, string>> {
    const distanceMap = new Map<number, string>();
    
    // 获取维修师位置
    const repairerPos = await getRepairerLocation();
    if (!repairerPos) {
      return distanceMap;
    }

    // 批量计算距离
    for (const order of orders) {
      if (order.addressInfo?.latitude && order.addressInfo?.longitude) {
        try {
          const distance = calculateStraightDistance(
            repairerPos,
            {
              latitude: order.addressInfo.latitude,
              longitude: order.addressInfo.longitude
            }
          );
          distanceMap.set(order.id, formatDistance(distance));
        } catch (error) {
          console.error(`计算订单${order.id}距离失败:`, error);
        }
      }
    }

    return distanceMap;
  }

  return {
    repairerLocation: computed(() => repairerLocation.value),
    locationLoading: computed(() => locationLoading.value),
    locationError: computed(() => locationError.value),
    getRepairerLocation,
    calculateOrderDistance,
    calculateMultipleDistances,
    clearLocationCache
  };
}
