package com.repair.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Profile;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 环境变量验证器
 * 在生产环境启动时检查所有必需的环境变量是否已设置
 */
@Component
@Slf4j
@Profile("prod")  // 仅在生产环境中激活
@Order(0)  // 确保在其他ApplicationRunner之前运行
public class EnvironmentVariableValidator implements ApplicationRunner {

    private final Environment environment;
    private final ApplicationContext applicationContext;

    // 定义必需的环境变量列表
    private static final List<String> REQUIRED_ENV_VARS = Arrays.asList(
            "SPRING_DATASOURCE_URL",
            "SPRING_DATASOURCE_USERNAME",
            "SPRING_DATASOURCE_PASSWORD",
            "SPRING_REDIS_HOST",
            "SPRING_REDIS_PASSWORD",
            "JWT_SECRET",
            "APP_MYSQL_CHECK_ON_STARTUP",
            "APP_MYSQL_FAIL_ON_ERROR"
    );

    @Autowired
    public EnvironmentVariableValidator(Environment environment, ApplicationContext applicationContext) {
        this.environment = environment;
        this.applicationContext = applicationContext;
    }

    @Override
    public void run(ApplicationArguments args) {
        log.info("正在验证生产环境必需的环境变量...");
        
        // 收集所有缺失的环境变量
        List<String> missingVars = REQUIRED_ENV_VARS.stream()
                .filter(var -> environment.getProperty(var) == null)
                .collect(Collectors.toList());
        
        // 如果有缺失的环境变量，输出错误信息并终止应用
        if (!missingVars.isEmpty()) {
            log.error("=================================================================");
            log.error("生产环境启动失败：以下必需的环境变量未设置：");
            missingVars.forEach(var -> log.error("- {}", var));
            log.error("=================================================================");
            log.error("请确保在.env文件中设置了这些环境变量，或者通过其他方式将它们传递给容器。");
            log.error("应用将终止启动以防止使用不正确的配置。");
            
            // 终止应用
            SpringApplication.exit(applicationContext, () -> 1);
            System.exit(1);
        } else {
            log.info("所有必需的环境变量已正确设置。");
            
            // 打印环境变量的实际值，帮助调试
            log.info("=================================================================");
            log.info("环境变量值：");
            REQUIRED_ENV_VARS.forEach(var -> {
                String value = environment.getProperty(var);
                // 对于敏感信息，不显示实际值
                if (var.contains("PASSWORD") || var.contains("SECRET")) {
                    log.info("{} = {}", var, "******");
                } else {
                    log.info("{} = {}", var, value);
                }
            });
            log.info("=================================================================");
        }
    }
} 