<template>
  <view class="container">
    <!-- 使用统一的返回按钮组件 -->
    <common-back-button></common-back-button>
    
    <!-- 页面标题 -->
    <view class="header">
      <text class="title">绑定手机号</text>
    </view>
    
    <!-- 表单内容 -->
    <view class="form-container">
      <view class="form-item">
        <view class="label">手机号</view>
        <input 
          type="number" 
          v-model="phone" 
          placeholder="请输入手机号" 
          maxlength="11"
          class="input"
        />
      </view>
      
      <view class="form-item">
        <view class="label">验证码</view>
        <view class="code-input-wrapper">
          <input 
            type="number" 
            v-model="code" 
            placeholder="请输入验证码" 
            maxlength="6"
            class="input"
          />
          <verify-code-button
            :phone="phone"
            user-type="user"
            :for-login="true"
            @send-success="handleSendSuccess"
            @send-fail="handleSendFail"
          />
        </view>
      </view>
      
      <button class="bind-btn" @click="bindPhone">绑定手机号</button>
    </view>
    
    <!-- 滑动验证组件 -->
    <slide-verify
      v-model:visible="showSliderCaptcha"
      @success="handleCaptchaSuccess"
      @fail="handleCaptchaFail"
      @cancel="handleCaptchaCancel"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed, onUnmounted } from 'vue';
import { useUserStore } from '@/store/user';
import { showToast, showLoading, hideLoading } from '@/utils/uniapi';
import type { UserInfo } from '@/api/user';
import { userApi } from '@/api/user';
import { navigationUtils } from '@/utils/navigation';
import CommonBackButton from '@/components/common/BackButton.vue';
import { isValidPhone, sendVerifyCode, useCountdown } from '@/utils/verifycode';
import SlideVerify from '@/components/common/SlideVerify.vue';
import { captchaService } from '@/utils/captchaService';
import VerifyCodeButton from '@/components/common/VerifyCodeButton.vue';

// 表单数据
const phone = ref('');
const code = ref('');
const userStore = useUserStore();

// 使用统一的倒计时工具
const countdownState = useCountdown(phone, 'user');

// 使用计算属性简化模板中的访问
const countdownValue = computed(() => countdownState.countdown.value);
const isSending = computed(() => countdownState.isSending.value);

// 滑动验证状态
const showSliderCaptcha = computed({
  get: () => captchaService.showCaptcha.value,
  set: (value) => captchaService.showCaptcha.value = value
});

// 组件卸载时清理倒计时
onUnmounted(() => {
  if (countdownState.cleanup) {
    countdownState.cleanup();
  }
});

// 监听手机号变化，更新倒计时状态
watch(phone, (newPhone) => {
  // 这里什么都不需要做，因为useCountdown内部会处理更新
  console.log('手机号变更为:', newPhone);
});

// 发送验证码
async function sendVerificationCode() {
  if (countdownValue.value > 0 || isSending.value) {
    return; // 防止重复点击
  }
  
  // 执行发送逻辑前再次验证手机号
  if (!isValidPhone(phone.value)) {
    showToast('请输入正确的手机号码');
    return;
  }
  
  // 使用验证服务发送验证码（包含滑动验证）
  await captchaService.sendVerifyCodeWithCaptcha(phone.value, 'user', true);
}

// 处理滑动验证成功
function handleCaptchaSuccess() {
  captchaService.handleCaptchaSuccess();
}

// 处理滑动验证失败
function handleCaptchaFail() {
  captchaService.handleCaptchaFail();
}

// 处理取消滑动验证
function handleCaptchaCancel() {
  captchaService.handleCaptchaCancel();
}

// 处理验证码发送成功
function handleSendSuccess() {
  console.log('验证码发送成功');
}

// 处理验证码发送失败
function handleSendFail() {
  console.log('验证码发送失败');
}

// 静默注册用户
async function silentRegister(wxLoginResult: any): Promise<UserInfo> {
  try {
    // 准备注册参数
    const registerParams = {
      username: wxLoginResult.userInfo?.nickName || `微信用户${Math.floor(Math.random() * 1000)}`,
      phone: phone.value,
      password: `wx${Date.now()}`, // 生成随机密码，用户不需要记住
      verifyCode: code.value,
      wxOpenId: wxLoginResult.openid
    };
    
    console.log('准备静默注册用户', registerParams);
    
    // 使用userApi进行注册
    const { data } = await userApi.register(registerParams);
    
    console.log('用户注册成功', data);
    
    return data as UserInfo;
  } catch (error) {
    console.error('静默注册失败', error);
    throw new Error('注册失败');
  }
}

// 处理绑定成功
function handleBindSuccess(responseData: any) {
  if (!responseData || !responseData.token) {
    hideLoading();
    showToast('服务器返回数据格式错误');
    return;
  }
  
  // 保存用户信息到本地存储和状态管理
  const token = responseData.token;
  const userInfo = responseData.userInfo || {};
  
  userStore.setToken(token);
  userStore.setUserInfo(userInfo);
  
  uni.setStorageSync('token', token);
  uni.setStorageSync('userInfo', userInfo);
  
  // 清理临时数据
  uni.removeStorageSync('wxLoginResult');
  
  hideLoading();
  showToast('绑定成功', 'success');
  
  // 检查是否有待跳转的页面
  const redirectUrl = uni.getStorageSync('redirect_after_bind_phone');
  
  // 跳转到正确的页面
  setTimeout(() => {
    // 移除存储的跳转页面
    uni.removeStorageSync('redirect_after_bind_phone');
    
    if (redirectUrl) {
      console.log('绑定成功后跳转到:', redirectUrl);
      navigationUtils.navigateTo(redirectUrl, {
        fail: (err) => {
          console.error('跳转失败:', err);
          // 如果跳转失败，默认跳转到用户首页
          navigationUtils.reLaunch('/pages/user/home');
        }
      });
    } else {
      // 没有特定跳转页面，默认跳转到用户首页
      navigationUtils.reLaunch('/pages/user/home');
    }
  }, 1000);
}

// 绑定手机号
async function bindPhone() {
  if (!isValidPhone(phone.value)) {
    showToast('请输入正确的手机号码');
    return;
  }
  
  if (!code.value || code.value.length !== 6) {
    showToast('请输入6位验证码');
    return;
  }
  
  try {
    showLoading('绑定中...');
    
    // 1. 获取缓存的微信登录结果
    const wxLoginResult = uni.getStorageSync('wxLoginResult');
    if (!wxLoginResult || !wxLoginResult.openid) {
      hideLoading();
      showToast('微信登录信息已失效，请重新登录');
      setTimeout(() => {
        try {
          uni.redirectTo({ 
            url: '/pages/user/login',
            fail: () => uni.reLaunch({ url: '/pages/index/index' })
          });
        } catch (e) {
          uni.reLaunch({ url: '/pages/index/index' });
        }
      }, 1500);
      return;
    }
    
    console.log('获取到wxLoginResult:', { 
      openid: wxLoginResult.openid,
      code: wxLoginResult.code,
      hasUserInfo: !!wxLoginResult.userInfo,
      hasSessionKey: !!wxLoginResult.session_key
    });
    
    // 2. 构造请求参数
    const bindParams = {
      code: wxLoginResult.code,
      phone: phone.value,
      phoneCode: code.value,
      encryptedData: wxLoginResult.encryptedData || '',
      iv: wxLoginResult.iv || '',
      openid: wxLoginResult.openid
    };
    
    console.log('准备绑定手机号请求参数:', { 
      ...bindParams, 
      encryptedData: wxLoginResult.encryptedData ? '已提供' : '未提供', 
      iv: wxLoginResult.iv ? '已提供' : '未提供'
    });
    
    // 3. 调用绑定手机号接口
    try {
      // 设置请求超时保护
      const requestTimeout = setTimeout(() => {
        hideLoading();
        showToast('请求超时，请检查网络后重试');
      }, 15000); // 15秒超时保护
      
      const { data } = await userApi.wxBindPhone(bindParams);
      
      // 清除超时保护
      clearTimeout(requestTimeout);
      
      console.log('绑定手机号成功，返回数据:', data);
      
      // 处理绑定成功
      handleBindSuccess(data);
    } catch (error: any) {
      console.error('绑定手机号失败:', error);
      
      // 特殊处理：如果是密码错误，尝试静默注册
      if (error.message?.includes('密码错误') || error.message?.includes('用户不存在')) {
        console.log('遇到用户验证问题，尝试静默注册');
        try {
          const userInfo = await silentRegister(wxLoginResult);
          await loginAfterRegister(userInfo);
          return;
        } catch (regError) {
          console.error('静默注册失败', regError);
          hideLoading();
          showToast('注册失败，请稍后重试');
        }
        return;
      }
      
      // 如果是因为手机号已存在导致的失败，尝试直接登录
      if (error.message?.includes('已被其他用户绑定') || error.message?.includes('已被注册')) {
        console.log('手机号已存在，尝试直接登录');
        try {
          await handleExistingPhoneLogin(wxLoginResult);
          return;
        } catch (loginError) {
          console.error('已有账号登录失败', loginError);
          hideLoading();
          showToast('登录失败，请手动登录');
        }
        return;
      } 
      
      // 显示错误信息
      hideLoading();
      showToast(error.message || '绑定失败，请重试');
    }
  } catch (error: any) {
    console.error('绑定过程中发生错误:', error);
    hideLoading();
    showToast('绑定失败，请重试');
    // 如果绑定过程彻底失败，返回首页
    setTimeout(() => {
      try {
        uni.reLaunch({ 
          url: '/pages/index/index',
          fail: (e) => {
            console.error('返回首页失败', e);
            uni.showToast({
              title: '返回首页失败，请手动操作',
              icon: 'none',
              duration: 2000
            });
          }
        });
      } catch (e) {
        console.error('跳转失败', e);
        uni.showToast({
          title: '跳转异常，请重启应用',
          icon: 'none',
          duration: 2000
        });
      }
    }, 2000);
  }
}

// 处理手机号已存在的情况，尝试通过微信登录直接关联
async function handleExistingPhoneLogin(wxLoginResult: any) {
  try {
    console.log('尝试静默登录并关联已有账号');
    
    // 1. 构建请求参数
    const loginParams: any = {
      code: wxLoginResult.code,
      openid: wxLoginResult.openid,
      sessionKey: wxLoginResult.session_key,
      phone: phone.value,
      phoneCode: code.value
    };
    
    console.log('静默登录请求参数:', loginParams);
    
    // 2. 调用微信登录接口
    try {
      const { data } = await userApi.wxLogin(loginParams);
      
      console.log('静默登录返回数据:', data);
      
      // 3. 如果返回了token，说明登录成功
      if (data && data.token) {
        userStore.setToken(data.token);
        userStore.setUserInfo(data.userInfo || {} as UserInfo);
        
        uni.setStorageSync('token', data.token);
        uni.setStorageSync('userInfo', data.userInfo || {});
        
        // 清理临时数据
        uni.removeStorageSync('wxLoginResult');
        
        hideLoading();
        showToast('登录成功', 'success');
        
        // 检查是否有待跳转的页面
        const redirectUrl = uni.getStorageSync('redirect_after_bind_phone');
        
        // 跳转到正确的页面
        setTimeout(() => {
          // 移除存储的跳转页面
          uni.removeStorageSync('redirect_after_bind_phone');
          
          if (redirectUrl) {
            console.log('登录成功后跳转到:', redirectUrl);
            navigationUtils.navigateTo(redirectUrl, {
              fail: (err) => {
                console.error('跳转失败:', err);
                // 如果跳转失败，默认跳转到用户首页
                navigationUtils.reLaunch('/pages/user/home');
              }
            });
          } else {
            // 没有特定跳转页面，默认跳转到用户首页
            navigationUtils.reLaunch('/pages/user/home');
          }
        }, 1000);
        return;
      }
      
      // 如果登录失败，尝试静默注册
      console.log('微信登录未返回token，尝试静默注册');
      const userInfo = await silentRegister(wxLoginResult);
      
      // 注册成功后再次尝试登录
      await loginAfterRegister(userInfo);
      return;
    } catch (error: any) {
      console.error('微信登录失败:', error);
      if (error.message?.includes('用户不存在') || error.message?.includes('密码错误')) {
        // 尝试静默注册
        console.log('静默登录失败，尝试静默注册');
        const userInfo = await silentRegister(wxLoginResult);
        
        // 注册成功后再次尝试登录
        await loginAfterRegister(userInfo);
        return;
      }
      
      throw error;
    }
  } catch (error: any) {
    console.error('关联已有账号失败:', error);
    hideLoading();
    showToast(error.message || '登录失败，请重试');
  }
}

// 注册成功后的登录流程
async function loginAfterRegister(userInfo: UserInfo) {
  try {
    // 使用注册成功后的用户信息进行登录
    const loginParams = {
      phone: userInfo.phone,
      // 使用临时生成的随机密码，不依赖UserInfo中的password属性
      password: `wx${Date.now()}`
    };
    
    console.log('使用注册信息登录:', { phone: loginParams.phone });
    
    // 尝试使用账号密码登录
    try {
      const result = await userApi.login(loginParams);
      
      if (result && result.data && result.data.token) {
        userStore.setToken(result.data.token);
        userStore.setUserInfo(result.data.userInfo);
        
        // 清理临时数据
        uni.removeStorageSync('wxLoginResult');
        
        hideLoading();
        showToast('登录成功', 'success');
        
        // 检查是否有待跳转的页面
        const redirectUrl = uni.getStorageSync('redirect_after_bind_phone');
        
        // 跳转到正确的页面
        setTimeout(() => {
          // 移除存储的跳转页面
          uni.removeStorageSync('redirect_after_bind_phone');
          
          if (redirectUrl) {
            console.log('登录成功后跳转到:', redirectUrl);
            navigationUtils.navigateTo(redirectUrl, {
              fail: (err) => {
                console.error('跳转失败:', err);
                // 如果跳转失败，默认跳转到用户首页
                navigationUtils.reLaunch('/pages/user/home');
              }
            });
          } else {
            // 没有特定跳转页面，默认跳转到用户首页
            navigationUtils.reLaunch('/pages/user/home');
          }
        }, 1000);
        return;
      }
    } catch (e) {
      console.error('尝试登录失败', e);
    }
    
    // 如果登录失败，回到微信登录流程
    hideLoading();
    showToast('账号创建成功，请重新登录');
    setTimeout(() => {
      uni.redirectTo({ url: '/pages/user/login' });
    }, 1500);
    
  } catch (error) {
    console.error('注册后登录失败:', error);
    hideLoading();
    showToast('登录失败，请手动登录');
    setTimeout(() => {
      uni.redirectTo({ url: '/pages/user/login' });
    }, 1500);
  }
}

// 页面加载时检查是否有微信登录信息
onMounted(() => {
  // 获取存储的微信登录结果
  const wxLoginResult = uni.getStorageSync('wxLoginResult');
  console.log('从存储中获取的微信登录结果:', wxLoginResult);
  
  // 验证登录结果的有效性
  if (!wxLoginResult || !wxLoginResult.openid) {
    console.warn('未找到有效的微信登录信息');
    
    // 检查用户是否已登录
    if (userStore.isLoggedIn) {
      const userInfo = userStore.userInfo;
      // 如果用户已登录但未绑定手机号，可以继续使用当前页面
      if (userInfo && (!userInfo.phone || userInfo.phone === '')) {
        console.log('用户已登录但未绑定手机号，可以继续使用当前页面');
        
        // 创建临时的wxLoginResult对象以便于后续使用
        uni.setStorageSync('wxLoginResult', {
          openid: userInfo.openid || '',
          code: '', // code已使用过，无法重新获取
          userInfo: userInfo,
          session_key: ''
        });
        
        return;
      }
    }
    
    console.error('微信登录信息无效或已过期，且用户未登录或已绑定手机号');
    
    uni.showToast({
      title: '登录信息已失效，请重新登录',
      icon: 'none',
      duration: 2000
    });
    
    // 延迟返回首页或登录页
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/user/login',
        fail: () => {
          uni.reLaunch({ url: '/pages/index/index' });
        }
      });
    }, 2000);
    return;
  }
});
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

.header {
  position: relative;
  height: 96rpx;
  margin-top: 160rpx; /* 为返回按钮留出空间 */
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #f0f0f0;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

.form-container {
  padding: 40rpx 30rpx;
  background-color: #fff;
  margin: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.form-item {
  margin-bottom: 30rpx;
}

.label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.input {
  height: 90rpx;
  border: 1rpx solid #e5e5e5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background-color: #f8f8f8;
}

.code-input-wrapper {
  display: flex;
  align-items: center;
}

.code-input-wrapper .input {
  flex: 1;
}

.send-code-btn {
  width: 200rpx;
  height: 90rpx;
  margin-left: 20rpx;
  font-size: 26rpx;
  background-color: #3c9cff;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
  transition: all 0.3s;
}

.send-code-btn.disabled {
  background-color: #bbb;
  opacity: 0.8;
  color: #f5f5f5;
}

.send-code-btn[disabled] {
  background-color: #bbb;
  opacity: 0.8;
  color: #f5f5f5;
}

.bind-btn {
  width: 100%;
  height: 90rpx;
  background-color: #3c9cff;
  color: #fff;
  font-size: 32rpx;
  border-radius: 45rpx;
  margin-top: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style> 