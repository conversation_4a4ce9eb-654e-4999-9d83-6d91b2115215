<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue';
import { useRepairerStore } from '@/store/repairer';
import { repairerApi } from '@/api/repairer';
import type { OrderInfo } from '@/api/repairer';
import { formatAddress } from '@/utils/addressFormatter';
import { quickNavigate } from '@/utils/mapNavigation';
import { useDistance } from '@/composables/useDistance';
import CommonBackButton from '@/components/common/BackButton.vue';

const repairerStore = useRepairerStore();
const orders = ref<OrderInfo[]>([]);
const loading = ref(false);
const refreshing = ref(false);
const hasMore = ref(true);
const pagination = reactive({
  page: 1,
  size: 10
});

// 距离数据
const orderDistances = ref<Map<number, string>>(new Map());

// 使用距离计算组合式函数
const { calculateMultipleDistances } = useDistance();

// 页面加载时检查登录状态并加载订单
onMounted(() => {
  // 验证登录状态
  if (!repairerStore.token) {
    uni.redirectTo({ url: './login' });
    return;
  }
  
  loadOrders();
});

// 加载订单列表
async function loadOrders(reset = false) {
  if (reset) {
    pagination.page = 1;
    orders.value = [];
    hasMore.value = true;
  }
  
  if (!hasMore.value && !reset) return;
  
  try {
    loading.value = true;
    
    const res = await repairerApi.getAcceptedOrders({
      page: pagination.page,
      size: pagination.size
    });
    
    if (res.data && res.data.records) {
      if (res.data.records.length < pagination.size) {
        hasMore.value = false;
      }
      
      if (reset) {
        orders.value = res.data.records;
      } else {
        orders.value = [...orders.value, ...res.data.records];
      }

      pagination.page++;

      // 计算距离
      try {
        const distances = await calculateMultipleDistances(orders.value);
        orderDistances.value = distances;
      } catch (distanceError) {
        console.warn('计算距离失败:', distanceError);
      }
    } else {
      hasMore.value = false;
    }
  } catch (error) {
    console.error('获取我的订单失败', error);
    uni.showToast({
      title: '获取订单失败',
      icon: 'none'
    });
  } finally {
    loading.value = false;
    if (refreshing.value) {
      uni.stopPullDownRefresh();
      refreshing.value = false;
    }
  }
}

// 完成订单
async function completeOrder(orderId: number) {
  uni.showModal({
    title: '确认完成',
    content: '确认已完成维修工作？',
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({ title: '处理中...' });
          // 调用完成订单接口
          await repairerApi.completeOrder(orderId);
          uni.hideLoading();
          
          uni.showToast({
            title: '订单已完成',
            icon: 'success'
          });
          
          // 延迟1.5秒后刷新列表
          setTimeout(() => {
            loadOrders(true);
          }, 1500);
        } catch (error) {
          uni.hideLoading();
          console.error('操作失败', error);
          uni.showToast({
            title: '操作失败',
            icon: 'none'
          });
        }
      }
    }
  });
}

// 取消接单
async function cancelOrder(orderId: number) {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消接单吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({ title: '处理中...' });
          await repairerApi.cancelOrder(orderId);
          uni.hideLoading();
          
          uni.showToast({
            title: '已取消接单',
            icon: 'success'
          });
          
          // 延迟1.5秒后刷新列表
          setTimeout(() => {
            loadOrders(true);
          }, 1500);
        } catch (error) {
          uni.hideLoading();
          console.error('取消失败', error);
          uni.showToast({
            title: '取消失败',
            icon: 'none'
          });
        }
      }
    }
  });
}

// 格式化订单状态
function formatStatus(status: number): string {
  const statusMap: Record<number, string> = {
    0: '待处理',
    1: '已接单',
    2: '处理中',
    3: '已完成',
    4: '已取消'
  };
  return statusMap[status] || '未知状态';
}

// 格式化时间
function formatDate(dateString?: string): string {
  if (!dateString) return '未设置';
  const date = new Date(dateString);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
}

// 下拉刷新
function onPullDownRefresh() {
  refreshing.value = true;
  loadOrders(true);
}

// 上拉加载更多
function onReachBottom() {
  if (!loading.value && hasMore.value) {
    loadOrders();
  }
}

// 获取显示操作按钮
function getActions(status: number) {
  if (status === 1) { // 已接单状态
    return [
      { text: '开始处理', type: 'primary', action: processOrder },
      { text: '取消', type: 'default', action: cancelOrder }
    ];
  } else if (status === 2) { // 处理中状态
    return [
      { text: '完成', type: 'primary', action: completeOrder }
    ];
  }
  return [];
}

// 开始处理订单
async function processOrder(orderId: number) {
  uni.showModal({
    title: '确认开始处理',
    content: '确认开始处理此订单？',
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({ title: '处理中...' });
          // 调用开始处理订单接口
          await repairerApi.processOrder(orderId);
          uni.hideLoading();
          
          uni.showToast({
            title: '已开始处理',
            icon: 'success'
          });
          
          // 延迟1.5秒后刷新列表
          setTimeout(() => {
            loadOrders(true);
          }, 1500);
        } catch (error) {
          uni.hideLoading();
          console.error('操作失败', error);
          uni.showToast({
            title: '操作失败',
            icon: 'none'
          });
        }
      }
    }
  });
}

// 处理地址点击事件
function handleAddressClick(order: OrderInfo) {
  console.log('点击地址导航:', {
    addressInfo: order.addressInfo,
    address: order.address
  });

  // 使用快速导航功能
  quickNavigate(order.addressInfo, order.address);
}

// 查看订单详情
function viewOrderDetail(orderId: number) {
  uni.navigateTo({
    url: `/pages/repairer/order-detail?id=${orderId}`
  });
}

// 暴露给页面的生命周期钩子
defineExpose({
  onPullDownRefresh,
  onReachBottom
});
</script>

<template>
  <view class="container">
    <!-- 使用统一的返回按钮组件 -->
    <common-back-button></common-back-button>
    
    <!-- 顶部标题栏 -->
    <view class="header">
      <text class="title">我的订单</text>
    </view>
    
    <!-- 订单列表 -->
    <view class="order-list">
      <view v-if="orders.length === 0 && !loading" class="empty-state">
        <text class="empty-text">暂无订单</text>
      </view>
      
      <view v-for="order in orders" :key="order.id" class="order-card">
        <view class="order-header">
          <text class="order-id">订单号: {{order.orderId}}</text>
          <text class="order-status" :class="{
            'status-pending': order.status === 0,
            'status-accepted': order.status === 1,
            'status-processing': order.status === 2,
            'status-completed': order.status === 3,
            'status-canceled': order.status === 4
          }">
            {{formatStatus(order.status)}}
          </text>
        </view>
        
        <view class="order-content">
          <view class="order-item">
            <text class="label">维修内容:</text>
            <text class="value">{{order.description}}</text>
          </view>
          
          <view class="order-item">
            <text class="label">维修地址:</text>
            <view class="address-container">
              <text
                class="value address-clickable"
                @click="handleAddressClick(order)"
              >
                {{formatAddress(order.addressInfo, order.address)}}
              </text>
              <text v-if="orderDistances.get(order.id)" class="distance-text">
                (约{{ orderDistances.get(order.id) }})
              </text>
            </view>
          </view>
          
          <view class="order-item">
            <text class="label">预约时间:</text>
            <text class="value">{{formatDate(order.appointmentTime)}}</text>
          </view>
          
          <view class="order-item">
            <text class="label">接单时间:</text>
            <text class="value">{{formatDate(order.updateTime)}}</text>
          </view>
          
          <view class="order-item" v-if="order.completeTime">
            <text class="label">完成时间:</text>
            <text class="value">{{formatDate(order.completeTime)}}</text>
          </view>
        </view>
        
        <view class="order-actions">
          <button class="action-button view-detail" @click="viewOrderDetail(order.id)">查看详情</button>
          <button v-if="order.status === 1" class="action-button process" @click="processOrder(order.id)">开始处理</button>
          <button v-if="order.status === 1 || order.status === 2" class="action-button cancel" @click="cancelOrder(order.id)">取消接单</button>
          <button v-if="order.status === 2" class="action-button complete" @click="completeOrder(order.id)">完成订单</button>
        </view>
      </view>
      
      <!-- 加载状态 -->
      <view v-if="loading" class="loading">加载中...</view>
      <view v-if="!hasMore && orders.length > 0" class="no-more">没有更多数据了</view>
    </view>
  </view>
</template>

<style>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

.header {
  height: 96rpx;
  margin-top: 160rpx; /* 为返回按钮留出空间 */
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #f0f0f0;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

.order-list {
  padding: 24rpx;
}

.empty-state {
  padding: 80rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
  margin-top: 20rpx;
}

.order-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16rpx;
  border-bottom: 1px solid #f0f0f0;
}

.order-id {
  font-size: 28rpx;
  color: #666;
}

.order-status {
  font-size: 28rpx;
  color: #ff6b00;
  font-weight: 500;
}

.status-pending {
  color: #ff6b00;
}

.status-accepted {
  color: #2196f3;
}

.status-processing {
  color: #ff6b00;
}

.status-completed {
  color: #52c41a;
}

.status-canceled {
  color: #f56c6c;
}

.order-content {
  padding: 16rpx 0;
}

.order-item {
  margin-bottom: 16rpx;
}

.order-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #666;
  margin-right: 16rpx;
}

.value {
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
}

.address-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.address-clickable {
  color: #2196f3 !important;
  text-decoration: underline;
  cursor: pointer;
  transition: all 0.3s ease;
}

.address-clickable:active {
  opacity: 0.7;
  transform: scale(0.98);
}

.distance-text {
  font-size: 24rpx;
  color: #666;
  font-style: italic;
}

.order-actions {
  padding-top: 16rpx;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 16rpx 20rpx 0;
}

.action-button {
  margin-bottom: 16rpx;
  height: 76rpx;
  font-size: 28rpx;
  line-height: 76rpx;
  text-align: center;
  border-radius: 38rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.view-detail {
  width: 100%;
  background-color: #2196f3;
  color: #ffffff;
  margin-bottom: 16rpx;
}

.view-detail:active {
  background-color: #1976d2;
}

.process {
  width: 48%;
  background-color: #2196f3;
  color: #ffffff;
}

.process:active {
  background-color: #1976d2;
}

.cancel {
  width: 48%;
  background-color: #f5f5f5;
  color: #666;
  border: 1px solid #e0e0e0;
}

.cancel:active {
  background-color: #e8e8e8;
}

.complete {
  width: 48%;
  background-color: #ff6b00;
  color: #ffffff;
}

.complete:active {
  background-color: #e66000;
}

.loading, .no-more {
  text-align: center;
  padding: 24rpx 0;
  color: #999;
  font-size: 28rpx;
}
</style> 