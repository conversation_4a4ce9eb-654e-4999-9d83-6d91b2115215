<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue';
import { useRepairerStore } from '@/store/repairer';
import { repairerApi } from '@/api/repairer';
import type { OrderInfo } from '@/api/repairer';
import { formatAddress } from '@/utils/addressFormatter';
import { quickNavigate } from '@/utils/mapNavigation';
import { useDistance } from '@/composables/useDistance';
import CommonBackButton from '@/components/common/BackButton.vue';

const repairerStore = useRepairerStore();
const orders = ref<OrderInfo[]>([]);
const loading = ref(false);
const refreshing = ref(false);
const hasMore = ref(true);
const pagination = reactive({
  page: 1,
  size: 10
});

// 距离数据
const orderDistances = ref<Map<number, string>>(new Map());

// 使用距离计算组合式函数
const { calculateMultipleDistances } = useDistance();

// 页面加载时检查登录状态并加载订单
onMounted(() => {
  // 验证登录状态
  if (!repairerStore.token) {
    uni.redirectTo({ url: './login' });
    return;
  }
  
  loadOrders();
});

// 加载订单列表
async function loadOrders(reset = false) {
  if (reset) {
    pagination.page = 1;
    orders.value = [];
    hasMore.value = true;
  }
  
  if (!hasMore.value && !reset) return;
  
  try {
    loading.value = true;
    
    const res = await repairerApi.getPendingOrders({
      page: pagination.page,
      size: pagination.size
    });
    
    if (res.data && res.data.records) {
      if (res.data.records.length < pagination.size) {
        hasMore.value = false;
      }
      
      if (reset) {
        orders.value = res.data.records;
      } else {
        orders.value = [...orders.value, ...res.data.records];
      }

      pagination.page++;

      // 计算距离
      try {
        const distances = await calculateMultipleDistances(orders.value);
        orderDistances.value = distances;
      } catch (distanceError) {
        console.warn('计算距离失败:', distanceError);
      }
    } else {
      hasMore.value = false;
    }
  } catch (error) {
    console.error('获取待接订单失败', error);
    uni.showToast({
      title: '获取订单失败',
      icon: 'none'
    });
  } finally {
    loading.value = false;
    if (refreshing.value) {
      uni.stopPullDownRefresh();
      refreshing.value = false;
    }
  }
}

// 接单
async function acceptOrder(orderId: number) {
  uni.showModal({
    title: '确认接单',
    content: '接单后需要按时完成维修，确认接单吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({ title: '处理中...' });
          const result = await repairerApi.acceptOrder(orderId);
          uni.hideLoading();
          
          if (result.code === 200) {
            // 先显示toast提示
            uni.showToast({
              title: '接单成功',
              icon: 'success',
              duration: 2000
            });
            
            // 延迟跳转，确保Toast显示完整
            setTimeout(() => {
              // 使用redirectTo替代navigateTo避免页面堆栈问题
              uni.redirectTo({
                url: './my-orders'
              });
            }, 1500);
          } else {
            uni.showToast({
              title: result.message || '接单失败',
              icon: 'none',
              duration: 2000
            });
          }
        } catch (error) {
          console.error('接单失败', error);
          uni.hideLoading();
          uni.showToast({
            title: '接单失败',
            icon: 'none',
            duration: 2000
          });
        }
      }
    }
  });
}

// 格式化订单状态
function formatStatus(status: number): string {
  const statusMap: Record<number, string> = {
    0: '待处理',
    1: '已接单',
    2: '已完成',
    3: '已取消'
  };
  return statusMap[status] || '未知状态';
}

// 格式化时间
function formatDate(dateString?: string): string {
  if (!dateString) return '未设置';
  const date = new Date(dateString);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
}

// 处理地址点击事件
function handleAddressClick(order: OrderInfo) {
  console.log('点击地址导航:', {
    addressInfo: order.addressInfo,
    address: order.address
  });

  // 使用快速导航功能
  quickNavigate(order.addressInfo, order.address);
}

// 下拉刷新
function onPullDownRefresh() {
  refreshing.value = true;
  loadOrders(true);
}

// 上拉加载更多
function onReachBottom() {
  if (!loading.value && hasMore.value) {
    loadOrders();
  }
}

// 暴露给页面的生命周期钩子
defineExpose({
  onPullDownRefresh,
  onReachBottom
});
</script>

<template>
  <view class="container">
    <!-- 使用统一的返回按钮组件 -->
    <common-back-button></common-back-button>
    
    <!-- 顶部标题栏 -->
    <view class="header">
      <text class="title">待处理订单</text>
    </view>
    
    <!-- 订单列表 -->
    <view class="order-list">
      <view v-if="orders.length === 0 && !loading" class="empty-state">
        <text class="empty-text">暂无待接订单</text>
      </view>
      
      <view v-for="order in orders" :key="order.id" class="order-card">
        <view class="order-header">
          <text class="order-id">订单号: {{order.orderId}}</text>
          <text class="order-status">{{formatStatus(order.status)}}</text>
        </view>
        
        <view class="order-content">
          <view class="order-item">
            <text class="label">维修内容:</text>
            <text class="value">{{order.description}}</text>
          </view>
          
          <view class="order-item">
            <text class="label">维修地址:</text>
            <view class="address-container">
              <text
                class="value address-clickable"
                @click="handleAddressClick(order)"
              >
                {{formatAddress(order.addressInfo, order.address)}}
              </text>
              <text v-if="orderDistances.get(order.id)" class="distance-text">
                (约{{ orderDistances.get(order.id) }})
              </text>
            </view>
          </view>
          
          <view class="order-item">
            <text class="label">预约时间:</text>
            <text class="value">{{formatDate(order.appointmentTime)}}</text>
          </view>
        </view>
        
        <view class="order-actions">
          <button class="accept-btn" @click="acceptOrder(order.id)">
            <text class="btn-icon">✓</text>
            <text class="btn-text">接单</text>
          </button>
        </view>
      </view>
      
      <!-- 加载状态 -->
      <view v-if="loading" class="loading">加载中...</view>
      <view v-if="!hasMore && orders.length > 0" class="no-more">没有更多数据了</view>
    </view>
  </view>
</template>

<style>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

.header {
  height: 96rpx;
  margin-top: 160rpx; /* 为返回按钮留出空间 */
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #f0f0f0;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

.order-list {
  padding: 24rpx;
}

.empty-state {
  padding: 80rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
  margin-top: 20rpx;
}

.order-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16rpx;
  border-bottom: 1px solid #f0f0f0;
}

.order-id {
  font-size: 28rpx;
  color: #666;
}

.order-status {
  font-size: 28rpx;
  color: #ff6b00;
  font-weight: 500;
}

.order-content {
  padding: 16rpx 0;
}

.order-item {
  margin-bottom: 16rpx;
}

.order-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #666;
  margin-right: 16rpx;
}

.value {
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
}

.address-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.address-clickable {
  color: #2196f3 !important;
  text-decoration: underline;
  cursor: pointer;
  transition: all 0.3s ease;
}

.address-clickable:active {
  opacity: 0.7;
  transform: scale(0.98);
}

.distance-text {
  font-size: 24rpx;
  color: #666;
  font-style: italic;
}

.order-actions {
  padding-top: 16rpx;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: center;
  padding: 16rpx 20rpx 0;
}

.accept-btn {
  width: 80%;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: 500;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  background: linear-gradient(135deg, #ff8800 0%, #ff6600 100%);
  color: #ffffff;
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 0, 0.3);
  transition: all 0.3s;
  border: none;
}

.accept-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 0, 0.2);
  background: linear-gradient(135deg, #ff7700 0%, #ff5500 100%);
}

.btn-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

.btn-text {
  letter-spacing: 2rpx;
}

.loading, .no-more {
  text-align: center;
  padding: 24rpx 0;
  color: #999;
  font-size: 28rpx;
}
</style> 