import { defineStore } from 'pinia';
import type { AdminInfo } from '@/api/admin';
import { adminApi } from '@/api/admin';

interface AdminState {
  token: string;
  adminInfo: AdminInfo | null;
}

export const useAdminStore = defineStore('admin', {
  state: (): AdminState => ({
    token: uni.getStorageSync('admin_token') || '',
    adminInfo: uni.getStorageSync('admin_info') || null
  }),

  getters: {
    isLoggedIn(): boolean {
      return !!this.token;
    }
  },

  actions: {
    // 设置Token
    setToken(token: string) {
      this.token = token;
      uni.setStorageSync('admin_token', token);
    },

    // 设置管理员信息
    setAdminInfo(adminInfo: AdminInfo) {
      this.adminInfo = adminInfo;
      uni.setStorageSync('admin_info', adminInfo);
    },

    // 清除管理员信息
    clearAdminInfo() {
      this.token = '';
      this.adminInfo = null;
      uni.removeStorageSync('admin_token');
      uni.removeStorageSync('admin_info');
    },

    // 退出登录
    logout() {
      this.clearAdminInfo();
      // 跳转到首页
      uni.switchTab({
        url: '/pages/index/index',
        fail: (err) => {
          console.error('switchTab失败:', err);
          // 如果switchTab失败，尝试使用reLaunch
          uni.reLaunch({
            url: '/pages/index/index'
          });
        }
      });
    },

    // 获取管理员信息
    async getAdminInfo() {
      try {
        const { data } = await adminApi.getAdminInfo();
        this.setAdminInfo(data);
        return data;
      } catch (error) {
        console.error('获取管理员信息失败', error);
        throw error;
      }
    }
  }
}); 