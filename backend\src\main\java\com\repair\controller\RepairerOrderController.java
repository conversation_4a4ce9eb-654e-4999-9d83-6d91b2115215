package com.repair.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.repair.entity.RepairOrder;
import com.repair.service.RepairerOrderService;
import com.repair.utils.JwtUtil;
import com.repair.common.Result;
import com.repair.vo.OrderVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 维修人员订单控制器
 */
@RestController
@RequestMapping("/api/repairer/order")
@Validated
public class RepairerOrderController {

    private final RepairerOrderService repairerOrderService;
    private final JwtUtil jwtUtil;

    public RepairerOrderController(RepairerOrderService repairerOrderService, JwtUtil jwtUtil) {
        this.repairerOrderService = repairerOrderService;
        this.jwtUtil = jwtUtil;
    }

    /**
     * 获取待处理订单列表
     */
    @GetMapping("/pending")
    public Result<Page<OrderVO>> getPendingOrders(
            @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size) {
        Page<RepairOrder> pageParam = new Page<>(page, size);
        Page<OrderVO> orders = repairerOrderService.getPendingOrders(pageParam);
        return Result.success(orders);
    }

    /**
     * 获取已接单列表
     */
    @GetMapping("/accepted")
    public Result<Page<OrderVO>> getAcceptedOrders(
            @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            HttpServletRequest request) {
        Long repairerId = jwtUtil.getUserIdFromRequest(request);
        Page<RepairOrder> pageParam = new Page<>(page, size);
        Page<OrderVO> orders = repairerOrderService.getAcceptedOrders(repairerId, pageParam);
        return Result.success(orders);
    }

    /**
     * 接单
     */
    @PostMapping("/accept/{orderId}")
    public Result<OrderVO> acceptOrder(@PathVariable("orderId") Long orderId, HttpServletRequest request) {
        Long repairerId = jwtUtil.getUserIdFromRequest(request);
        OrderVO order = repairerOrderService.acceptOrder(repairerId, orderId);
        return Result.success(order);
    }

    /**
     * 开始处理订单
     */
    @PostMapping("/process/{orderId}")
    public Result<OrderVO> processOrder(@PathVariable("orderId") Long orderId, HttpServletRequest request) {
        Long repairerId = jwtUtil.getUserIdFromRequest(request);
        OrderVO order = repairerOrderService.processOrder(repairerId, orderId);
        return Result.success(order);
    }

    /**
     * 完成订单
     */
    @PostMapping("/complete/{orderId}")
    public Result<OrderVO> completeOrder(@PathVariable("orderId") Long orderId, HttpServletRequest request) {
        Long repairerId = jwtUtil.getUserIdFromRequest(request);
        OrderVO order = repairerOrderService.completeOrder(repairerId, orderId);
        return Result.success(order);
    }

    /**
     * 转单
     */
    @PostMapping("/transfer/{orderId}")
    public Result<OrderVO> transferOrder(
            @PathVariable("orderId") Long orderId,
            @RequestParam("reason") @NotNull String reason,
            HttpServletRequest request) {
        Long repairerId = jwtUtil.getUserIdFromRequest(request);
        OrderVO order = repairerOrderService.transferOrder(repairerId, orderId, reason);
        return Result.success(order);
    }

    /**
     * 获取订单详情
     */
    @GetMapping("/{orderId}")
    public Result<OrderVO> getOrderDetail(@PathVariable("orderId") Long orderId, HttpServletRequest request) {
        Long repairerId = jwtUtil.getUserIdFromRequest(request);
        OrderVO orderDetail = repairerOrderService.getOrderDetail(repairerId, orderId);
        return Result.success(orderDetail);
    }
} 