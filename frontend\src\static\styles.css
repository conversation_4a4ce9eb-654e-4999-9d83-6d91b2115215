/* 基础样式 */
page {
  background-color: #f5f5f5;
}
.container {
  width: 100%;
  min-height: 100vh;
}

/* 文本样式 */
.text-primary {
  color: #007aff;
}
.text-success {
  color: #4cd964;
}
.text-warning {
  color: #f0ad4e;
}
.text-error {
  color: #dd524d;
}
.text-gray-400 {
  color: #9ca3af;
}
.text-gray-500 {
  color: #6b7280;
}
.text-gray-600 {
  color: #4b5563;
}
.text-gray-700 {
  color: #374151;
}
.text-gray-800 {
  color: #1f2937;
}
.text-gray-900 {
  color: #111827;
}
.text-white {
  color: #ffffff;
}
.text-black {
  color: #000000;
}

/* 字体大小 */
.text-xs {
  font-size: 24rpx;
}
.text-sm {
  font-size: 28rpx;
}
.text-base {
  font-size: 32rpx;
}
.text-lg {
  font-size: 36rpx;
}
.text-xl {
  font-size: 40rpx;
}
.text-2xl {
  font-size: 48rpx;
}

/* 字体粗细 */
.font-light {
  font-weight: 300;
}
.font-normal {
  font-weight: 400;
}
.font-medium {
  font-weight: 500;
}
.font-bold {
  font-weight: 700;
}

/* 布局样式 */
.flex {
  display: flex;
}
.flex-row {
  flex-direction: row;
}
.flex-col {
  flex-direction: column;
}
.justify-center {
  justify-content: center;
}
.items-center {
  align-items: center;
}
.justify-between {
  justify-content: space-between;
}
.justify-start {
  justify-content: flex-start;
}
.justify-end {
  justify-content: flex-end;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.flex-1 {
  flex: 1;
}
.flex-wrap {
  flex-wrap: wrap;
}

/* 边距 */
.m-1 {
  margin: 8rpx;
}
.m-2 {
  margin: 16rpx;
}
.m-3 {
  margin: 24rpx;
}
.m-4 {
  margin: 32rpx;
}
.mx-1 {
  margin-left: 8rpx;
  margin-right: 8rpx;
}
.mx-2 {
  margin-left: 16rpx;
  margin-right: 16rpx;
}
.mx-3 {
  margin-left: 24rpx;
  margin-right: 24rpx;
}
.mx-4 {
  margin-left: 32rpx;
  margin-right: 32rpx;
}
.my-1 {
  margin-top: 8rpx;
  margin-bottom: 8rpx;
}
.my-2 {
  margin-top: 16rpx;
  margin-bottom: 16rpx;
}
.my-3 {
  margin-top: 24rpx;
  margin-bottom: 24rpx;
}
.my-4 {
  margin-top: 32rpx;
  margin-bottom: 32rpx;
}
.mt-1 {
  margin-top: 8rpx;
}
.mt-2 {
  margin-top: 16rpx;
}
.mt-3 {
  margin-top: 24rpx;
}
.mt-4 {
  margin-top: 32rpx;
}
.mb-1 {
  margin-bottom: 8rpx;
}
.mb-2 {
  margin-bottom: 16rpx;
}
.mb-3 {
  margin-bottom: 24rpx;
}
.mb-4 {
  margin-bottom: 32rpx;
}
.ml-1 {
  margin-left: 8rpx;
}
.ml-2 {
  margin-left: 16rpx;
}
.ml-3 {
  margin-left: 24rpx;
}
.ml-4 {
  margin-left: 32rpx;
}
.mr-1 {
  margin-right: 8rpx;
}
.mr-2 {
  margin-right: 16rpx;
}
.mr-3 {
  margin-right: 24rpx;
}
.mr-4 {
  margin-right: 32rpx;
}

/* 内边距 */
.p-1 {
  padding: 8rpx;
}
.p-2 {
  padding: 16rpx;
}
.p-3 {
  padding: 24rpx;
}
.p-4 {
  padding: 32rpx;
}
.px-1 {
  padding-left: 8rpx;
  padding-right: 8rpx;
}
.px-2 {
  padding-left: 16rpx;
  padding-right: 16rpx;
}
.px-3 {
  padding-left: 24rpx;
  padding-right: 24rpx;
}
.px-4 {
  padding-left: 32rpx;
  padding-right: 32rpx;
}
.py-1 {
  padding-top: 8rpx;
  padding-bottom: 8rpx;
}
.py-2 {
  padding-top: 16rpx;
  padding-bottom: 16rpx;
}
.py-3 {
  padding-top: 24rpx;
  padding-bottom: 24rpx;
}
.py-4 {
  padding-top: 32rpx;
  padding-bottom: 32rpx;
}

/* 圆角 */
.rounded-sm {
  border-radius: 4rpx;
}
.rounded {
  border-radius: 8rpx;
}
.rounded-md {
  border-radius: 12rpx;
}
.rounded-lg {
  border-radius: 16rpx;
}
.rounded-xl {
  border-radius: 24rpx;
}
.rounded-full {
  border-radius: 9999rpx;
}

/* 背景颜色 */
.bg-white {
  background-color: #ffffff;
}
.bg-gray-100 {
  background-color: #f3f4f6;
}
.bg-gray-200 {
  background-color: #e5e7eb;
}
.bg-primary {
  background-color: #007aff;
}
.bg-success {
  background-color: #4cd964;
}
.bg-warning {
  background-color: #f0ad4e;
}
.bg-error {
  background-color: #dd524d;
}

/* 边框 */
.border {
  border-width: 1rpx;
  border-style: solid;
}
.border-t {
  border-top-width: 1rpx;
  border-top-style: solid;
}
.border-b {
  border-bottom-width: 1rpx;
  border-bottom-style: solid;
}
.border-l {
  border-left-width: 1rpx;
  border-left-style: solid;
}
.border-r {
  border-right-width: 1rpx;
  border-right-style: solid;
}
.border-gray-200 {
  border-color: #e5e7eb;
}
.border-gray-300 {
  border-color: #d1d5db;
}

/* 阴影 */
.shadow-sm {
  box-shadow: 0 1rpx 2rpx 0 rgba(0, 0, 0, 0.05);
}
.shadow {
  box-shadow: 0 1rpx 3rpx 0 rgba(0, 0, 0, 0.1), 0 1rpx 2rpx 0 rgba(0, 0, 0, 0.06);
}
.shadow-md {
  box-shadow: 0 4rpx 6rpx -1rpx rgba(0, 0, 0, 0.1), 0 2rpx 4rpx -1rpx rgba(0, 0, 0, 0.06);
}
.shadow-lg {
  box-shadow: 0 10rpx 15rpx -3rpx rgba(0, 0, 0, 0.1), 0 4rpx 6rpx -2rpx rgba(0, 0, 0, 0.05);
}

/* 宽度和高度 */
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
.w-1-2 {
  width: 50%;
}
.w-1-3 {
  width: 33.333333%;
}
.w-2-3 {
  width: 66.666667%;
}
.w-1-4 {
  width: 25%;
}
.w-3-4 {
  width: 75%;
}

/* 全局样式 */

/* 状态栏适配 */
:root {
  --status-bar-height: 44px; /* 默认值，在App.vue中会被动态计算覆盖 */
}

/* 安全区域样式 */
.safe-area-top {
  padding-top: var(--status-bar-height);
}

.safe-area-inset-top {
  margin-top: var(--status-bar-height);
} 