package com.repair.utils;

import com.repair.exception.BusinessException;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.crypto.SecretKey;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.Base64;
import java.util.Date;
import java.util.Objects;

@Component
public class JwtUtil {
    private static String secret;
    private static Long expiration;
    private static final Logger log = LoggerFactory.getLogger(JwtUtil.class);

    @Value("${jwt.secret}")
    public void setSecret(String secret) {
        JwtUtil.secret = secret;
    }

    @Value("${jwt.expiration}")
    public void setExpiration(Long expiration) {
        JwtUtil.expiration = expiration;
    }

    /**
     * 获取签名密钥
     */
    private static Key getSigningKey() {
        byte[] keyBytes = secret.getBytes(StandardCharsets.UTF_8);
        return Keys.hmacShaKeyFor(keyBytes);
    }

    /**
     * 生成token
     */
    public static String generateToken(Long userId) {
        throw new UnsupportedOperationException("请使用createToken方法，确保包含用户角色信息");
    }

    /**
     * 创建包含多个信息的token
     */
    public static String createToken(Long userId, String username, String role) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration);

        return Jwts.builder()
                .setSubject(userId.toString())
                .claim("username", username)
                .claim("role", role)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(getSigningKey())
                .compact();
    }

    /**
     * 从token中获取用户ID
     */
    public static Long getUserIdFromToken(String token) {
        Claims claims = Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(token)
                .getBody();

        return Long.parseLong(claims.getSubject());
    }

    /**
     * 从token中获取用户角色
     */
    public static String getRoleFromToken(String token) {
        Claims claims = Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(token)
                .getBody();

        return claims.get("role", String.class);
    }

    /**
     * 从token中获取用户名
     */
    public static String getUsernameFromToken(String token) {
        Claims claims = Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(token)
                .getBody();

        return claims.get("username", String.class);
    }

    /**
     * 从请求头中获取用户ID
     * @param request HTTP请求
     * @return 用户ID
     */
    public static Long getUserIdFromRequest(HttpServletRequest request) {
        // 获取请求头中的令牌
        String token = request.getHeader("Authorization");
        if (!StringUtils.hasText(token)) {
            throw new BusinessException("未找到有效的认证信息");
        }
        
        // 处理Bearer前缀
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        
        // 清除可能存在的空格
        token = token.trim();
        
        if (token.isEmpty()) {
            throw new BusinessException("令牌内容为空");
        }
        
        try {
            return getUserIdFromToken(token);
        } catch (Exception e) {
            log.error("解析令牌失败", e);
            throw new BusinessException("无效的认证信息: " + e.getMessage());
        }
    }

    /**
     * 从请求头中获取用户角色
     * @param request HTTP请求
     * @return 用户角色
     */
    public static String getRoleFromRequest(HttpServletRequest request) {
        // 获取请求头中的令牌
        String token = request.getHeader("Authorization");
        if (!StringUtils.hasText(token)) {
            throw new BusinessException("未找到有效的认证信息");
        }
        
        // 处理Bearer前缀
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        
        // 清除可能存在的空格
        token = token.trim();
        
        if (token.isEmpty()) {
            throw new BusinessException("令牌内容为空");
        }
        
        try {
            return getRoleFromToken(token);
        } catch (Exception e) {
            log.error("解析令牌角色失败", e);
            throw new BusinessException("无效的认证信息: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前请求用户ID
     * @return 当前用户ID
     */
    public static Long getCurrentUserId() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            throw new BusinessException("获取请求上下文失败");
        }
        
        HttpServletRequest request = attributes.getRequest();
        return getUserIdFromRequest(request);
    }

    /**
     * 验证token是否有效
     */
    public static boolean validateToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            log.warn("令牌为空");
            return false;
        }
        
        try {
            Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(token);
            return true;
        } catch (Exception e) {
            log.warn("令牌验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 生成随机 HMAC-SHA 密钥
     */
    public static void generateRandomKey() {
        SecretKey randomKey = Keys.secretKeyFor(SignatureAlgorithm.HS256);
        String base64Key = Base64.getEncoder().encodeToString(randomKey.getEncoded());
        System.out.println("随机 HMAC-SHA 密钥 (Base64): " + base64Key);
    }

    public static void main(String[] args) {
        // 生成随机密钥用于JWT secret
        generateRandomKey();
    }
} 