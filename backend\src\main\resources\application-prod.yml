# 生产环境特有配置
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 在docker-compose环境中，使用服务名作为主机名
    url: ${SPRING_DATASOURCE_URL}
    username: ${SPRING_DATASOURCE_USERNAME}
    password: ${SPRING_DATASOURCE_PASSWORD}
    hikari:
      # 连接池配置
      maximum-pool-size: 10
      minimum-idle: 5
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-timeout: 30000
  
  # Redis生产环境配置
  redis:
    host: ${SPRING_REDIS_HOST}
    port: ${SPRING_REDIS_PORT}
    database: ${SPRING_REDIS_DATABASE}
    password: ${SPRING_REDIS_PASSWORD}

# MyBatis-Plus生产环境特有配置
mybatis-plus:
  configuration:
    # 生产环境关闭SQL日志
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl

# JWT生产环境配置
jwt:
  # 在生产环境中，使用环境变量来设置密钥
  secret: ${JWT_SECRET}

# 微信小程序生产环境配置
wechat:
  miniapp:
    appid: ${WECHAT_MINIAPP_APPID}
    secret: ${WECHAT_MINIAPP_SECRET}

# 自定义配置
app:
  mysql:
    check-on-startup: ${APP_MYSQL_CHECK_ON_STARTUP}
    fail-on-error: ${APP_MYSQL_FAIL_ON_ERROR}
  # 阿里云SMS生产环境配置
  aliyun-sms:
    enabled: ${APP_ALIYUN_SMS_ENABLED:false}
    access-key-id: ${ALIYUN_ACCESS_KEY_ID}
    access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET}
    sign-name: ${ALIYUN_SIGN_NAME}
    template-code: ${ALIYUN_TEMPLATE_CODE}
    region-id: cn-chengdu
    endpoint: dysmsapi.aliyuncs.com

# 日志生产环境配置
logging:
  file:
    path: /app/logs
  level:
    root: INFO
    com.repair: INFO
    com.repair.mapper: WARN
    org.springframework: WARN 