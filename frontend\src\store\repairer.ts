import { defineStore } from 'pinia';
import type { RepairerInfo } from '@/api/repairer';
import { repairerApi } from '@/api/repairer';

interface RepairerState {
  token: string;
  repairerInfo: RepairerInfo | null;
  processingOrderCount: number;
  pendingOrderCount: number;
}

export const useRepairerStore = defineStore('repairer', {
  state: (): RepairerState => ({
    token: uni.getStorageSync('repairer_token') || '',
    repairerInfo: uni.getStorageSync('repairer_info') || null,
    processingOrderCount: 0,
    pendingOrderCount: 0
  }),

  getters: {
    isLoggedIn(): boolean {
      return !!this.token;
    }
  },

  actions: {
    // 设置Token
    setToken(token: string) {
      this.token = token;
      uni.setStorageSync('repairer_token', token);
    },

    // 设置维修师傅信息
    setRepairerInfo(repairerInfo: RepairerInfo) {
      this.repairerInfo = repairerInfo;
      uni.setStorageSync('repairer_info', repairerInfo);
    },

    // 清除维修师傅信息
    clearRepairerInfo() {
      this.token = '';
      this.repairerInfo = null;
      uni.removeStorageSync('repairer_token');
      uni.removeStorageSync('repairer_info');
    },

    // 退出登录
    logout() {
      this.clearRepairerInfo();
      // 跳转到首页
      uni.switchTab({
        url: '/pages/index/index',
        fail: (err) => {
          console.error('switchTab失败:', err);
          // 如果switchTab失败，尝试使用reLaunch
          uni.reLaunch({
            url: '/pages/index/index'
          });
        }
      });
    },
    
    // 获取维修师信息
    async getRepairerInfo() {
      try {
        const { data } = await repairerApi.getRepairerInfo();
        this.setRepairerInfo(data);
        return data;
      } catch (error) {
        console.error('获取维修师信息失败', error);
        throw error;
      }
    },
    
    // 获取待处理订单数量
    async getProcessingOrderCount() {
      try {
        const { data } = await repairerApi.getProcessingOrderCount();
        this.processingOrderCount = data;
        return data;
      } catch (error) {
        console.error('获取待处理订单数量失败', error);
        this.processingOrderCount = 0;
        return 0;
      }
    },
    
    // 获取待接单订单数量
    async getPendingOrderCount() {
      try {
        const { data } = await repairerApi.getPendingOrderCount();
        this.pendingOrderCount = data;
        return data;
      } catch (error) {
        console.error('获取待接单订单数量失败', error);
        this.pendingOrderCount = 0;
        return 0;
      }
    }
  }
}); 