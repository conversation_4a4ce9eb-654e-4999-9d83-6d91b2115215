package com.repair.vo;

import lombok.Data;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 订单列表VO
 */
@Data
public class OrderVO {
    
    /**
     * 订单ID
     */
    private Long id;
    
    /**
     * 订单编号
     */
    private String orderId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 维修师ID
     */
    private Long repairerId;
    
    /**
     * 用户名称
     */
    private String username;
    
    /**
     * 用户手机号
     */
    private String userPhone;
    
    /**
     * 维修师名称
     */
    private String repairerName;
    
    /**
     * 维修师手机号
     */
    private String repairerPhone;
    
    /**
     * 联系人姓名
     */
    private String contactName;
    
    /**
     * 联系电话
     */
    private String contactPhone;
    
    /**
     * 结构化地址信息
     */
    private AddressVO addressInfo;
    
    /**
     * 问题描述
     */
    private String description;
    
    /**
     * 订单状态：0-待处理，1-已接单，2-处理中，3-已完成，4-已取消
     */
    private Integer status;
    
    /**
     * 订单状态描述
     */
    private String statusDesc;
    
    /**
     * 是否紧急：0-普通，1-紧急
     */
    private Integer urgent;
    
    /**
     * 评分：1-5星
     */
    private Integer rating;
    
    /**
     * 评价内容
     */
    private String comment;
    
    /**
     * 预约维修时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime appointmentTime;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    /**
     * 完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completeTime;
} 