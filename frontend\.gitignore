# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 依赖相关
node_modules/
.pnpm-store/
.pnpm-debug.log
/npm-debug.log*
/yarn-debug.log*
/yarn-error.log*
/pnpm-debug.log*
/.pnpm-debug.log*
package-lock.json
yarn.lock
pnpm-lock.yaml

# 编译和构建输出
dist
dist-ssr
unpackage
.uni-app
*.local

# 编辑器目录和文件
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.sublime-*
.DS_Store
.directory
*.tsbuildinfo
*.code-workspace

# 本地环境文件
.env
.env.local
.env.*.local

# 不忽略这两个环境配置文件
!.env.production
!.env.development

# 测试覆盖率
/coverage

# 缓存文件
.history
.temp
.cache
.eslintcache
.stylelintcache
.nuxt

# 测试相关
/test/unit/coverage
/test/e2e/reports
/tests/e2e/videos/
/tests/e2e/screenshots/

# Vitest
vitest.config.ts.timestamp-*

# 预编译工具输出目录
/.sass-cache/
/.tsc-cache/

# TypeScript 编译输出
**/*.js.map
**/*.d.ts.map

# Vue开发时的热更新文件
*.hot-update.js
*.hot-update.json

# uni-app特有
.hbuilderx/
hybrid/
/android/
/ios/

# 微信小程序
/wxcomponents/
miniprogram_dev/
miniprogram_npm/

# 临时文件
tmp/
temp/
.tmp/

# 备份文件
*.bak
*.backup
*.orig 