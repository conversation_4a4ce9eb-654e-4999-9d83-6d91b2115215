<template>
  <view class="verification-container">
    <template v-if="type === 'puzzle'">
      <PuzzleVerify 
        @success="onVerifySuccess" 
        @error="onVerifyError" 
      />
    </template>
    <template v-else-if="type === 'slide'">
      <SliderCaptcha 
        @success="onVerifySuccess" 
        @error="onVerifyError" 
      />
    </template>
  </view>
</template>

<script>
import PuzzleVerify from '@/components/common/PuzzleVerify.vue'
import SliderCaptcha from '@/components/common/SliderCaptcha.vue'

export default {
  components: {
    PuzzleVerify,
    SliderCaptcha
  },
  data() {
    return {
      type: 'puzzle', // 默认使用拼图验证
    }
  },
  onLoad(options) {
    // 获取页面参数
    if (options.type) {
      this.type = options.type
    }
  },
  methods: {
    onVerifySuccess(result) {
      // 验证成功，返回结果给调用页面
      const eventChannel = this.getOpenerEventChannel()
      eventChannel.emit('verificationSuccess', {
        result,
        type: this.type
      })
      uni.navigateBack()
    },
    onVerifyError(error) {
      // 验证失败
      uni.showToast({
        title: '验证失败，请重试',
        icon: 'none'
      })
    }
  }
}
</script>

<style lang="scss">
.verification-container {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
}
</style> 