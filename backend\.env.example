# 应用端口配置
BACKEND_PORT=18080
MYSQL_PORT=3306
REDIS_PORT=6379
NGINX_HTTP_PORT=80
NGINX_HTTPS_PORT=443 

# 数据库配置
MYSQL_ROOT_PASSWORD=root_password
MYSQL_DATABASE=repair_system
MYSQL_USER=repair_user
MYSQL_PASSWORD=repair_password

# Redis配置
REDIS_PASSWORD=redis_password

# Spring Boot应用配置
SPRING_PROFILES_ACTIVE=prod
SPRING_DATASOURCE_URL=**********************************************************************************************************
SPRING_DATASOURCE_USERNAME=repair_user
SPRING_DATASOURCE_PASSWORD=repair_password
SPRING_REDIS_HOST=redis
SPRING_REDIS_PORT=6379
SPRING_REDIS_DATABASE=0
SPRING_REDIS_PASSWORD=redis_password

# MySQL连接检查配置
APP_MYSQL_CHECK_ON_STARTUP=true
APP_MYSQL_FAIL_ON_ERROR=true

# JWT配置
JWT_SECRET=your_secure_jwt_secret_key_replace_this_with_a_strong_random_value
JWT_EXPIRATION=86400000

# 微信小程序配置
WECHAT_MINIAPP_APPID=your_appid
WECHAT_MINIAPP_SECRET=your_secret

# 阿里云SMS配置
APP_ALIYUN_SMS_ENABLED=false
ALIYUN_ACCESS_KEY_ID=your_aliyun_access_key_id
ALIYUN_ACCESS_KEY_SECRET=your_aliyun_access_key_secret
ALIYUN_SIGN_NAME=your_aliyun_sign_name
ALIYUN_TEMPLATE_CODE=SMS_319371136