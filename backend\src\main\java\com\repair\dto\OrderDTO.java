package com.repair.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.time.LocalDateTime;

/**
 * 订单数据传输对象
 */
@Data
@Accessors(chain = true)
public class OrderDTO {
    /**
     * 联系人姓名
     */
    @NotBlank(message = "联系人不能为空")
    @Length(max = 50, message = "联系人姓名不能超过50个字符")
    private String contactName;
    
    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不能为空")
    @Pattern(regexp = "^1\\d{10}$", message = "请输入正确的手机号")
    private String contactPhone;
    
    /**
     * 结构化地址信息
     */
    @NotNull(message = "地址信息不能为空")
    @Valid
    private AddressDTO addressInfo;
    
    /**
     * 维修描述
     */
    @NotBlank(message = "维修描述不能为空")
    @Length(min = 5, max = 500, message = "描述长度应在5-500字符之间")
    private String description;
    
    /**
     * 是否紧急 (0-否, 1-是)
     */
    private Integer urgent;
    
    /**
     * 预约时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime appointmentTime;
} 