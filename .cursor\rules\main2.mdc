---
description: 
globs: 
alwaysApply: false
---
项目名称: uni-app维修管理系统

# 代码风格与结构
- 使用简洁、可维护且技术上准确的TypeScript代码。
- 避免使用类，优先使用函数式编程风格。
- 模块化开发，遵循DRY原则，避免代码重复。
- 使用具有描述性的变量命名，如isLoading, hasError。
- 每个文件仅包含相关内容，如组件、子组件、工具函数、静态资源和类型。

# 命名规范
- 目录使用小写短横线命名，如：components/order-form。
- 函数使用命名导出。

# TypeScript使用规范
- 全部代码使用TypeScript编写。
- 优先使用interface代替type，便于扩展和合并。
- 避免使用enum，推荐使用Map实现更好的类型安全性。
- 使用TypeScript接口定义组件Props。

# 语法与格式化
- 使用function关键字定义纯函数以利用提升特性。
- 使用Vue Composition API与<script setup>风格。

# UI与样式
- 使用Headless UI、Element Plus和Tailwind CSS进行组件开发与样式管理。
- 使用Tailwind CSS实现响应式设计，采用移动优先策略。

# 性能优化
- 使用VueUse提供的工具函数优化响应性与性能。
- 对异步组件使用Suspense与fallback UI。
- 非关键组件使用动态导入进行代码分割。
- 图片使用WebP格式，包含尺寸信息，并实现懒加载。
- 在Vite构建阶段进行代码分割以优化打包大小。

# 角色与功能定义
## 用户
- 登录后可提交维修订单。
- 提交内容包含：联系人、联系方式、地址、维修内容。

## 维修师傅
- 登录后可查看订单信息。
- 可执行以下操作：接单、转单、关闭订单、完成订单。

## 管理员
- 登录后可查看与管理所有用户与维修师傅信息。

# 关键约定
- 优化Web Vitals (LCP, CLS, FID)，使用Lighthouse工具检测。
- 遵循uni-app官方推荐的最佳实践。

# 开发流程
- 项目设计时先编写一份TODO.md
- 开发时每完成一个功能都需要及时更新TODO.md
- 开发时如果要执行命令要使用windwos下的命令
- 开发时可以调用所有可用的MCP工具

