import { defineConfig, loadEnv } from "vite";
import uni from "@dcloudio/vite-plugin-uni";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // 加载env文件
  const env = loadEnv(mode, process.cwd());
  
  console.log('当前构建模式:', mode);
  console.log('API基础URL:', env.VITE_API_BASE_URL);
  
  return {
    plugins: [uni()],
    server: {
      proxy: {
        '/api': {
          target: env.VITE_API_BASE_URL,
          changeOrigin: true,
          secure: false, // 如果是https接口，需要配置这个参数
          ws: true, // 是否启用websocket
        }
      },
      cors: true // 启用CORS支持
    },
    // 生产环境配置
    build: {
      // 构建优化选项
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: false, // 生产环境下是否移除console
          drop_debugger: true // 生产环境下移除debugger
        }
      },
      // 输出目录
      outDir: 'dist',
      // 分割代码，防止单文件过大
      chunkSizeWarningLimit: 1000,
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['vue', 'pinia'], // 第三方库单独打包
          }
        }
      }
    }
  }
});
