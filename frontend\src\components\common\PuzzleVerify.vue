<!-- 拼图滑块验证组件 - 无Canvas实现 -->
<template>
  <view class="slide_model" v-if="slidebel">
    <view class="slide_wrapper">
      <!-- 顶部标题栏 -->
      <view class="puzzle_header">
        <text class="puzzle_title">{{ title }}</text>
        <view @click="cancel" class="puzzle_close_btn">
          <text class="iconfont icon-close"></text>
        </view>
      </view>
      
      <!-- 拼图图片部分 -->
      <view class="puzzle_container" id="puzzle_container">
        <!-- 背景 -->
        <view class="puzzle_background" :style="{ backgroundColor: bgColor }">
          <!-- 随机形状元素 -->
          <view 
            v-for="(shape, index) in randomShapes" 
            :key="index" 
            class="random_shape"
            :style="{
              left: shape.x + 'px',
              top: shape.y + 'px',
              width: shape.size + 'px',
              height: shape.size + 'px',
              borderRadius: shape.type === 0 ? '50%' : '0',
              transform: shape.type === 2 ? 'rotate(45deg)' : '',
              backgroundColor: shape.color
            }"
          ></view>
        </view>
        
        <!-- 被抠方块 -->
        <view 
          class="puzzle_hole" 
          :style="{ 
            left: targetPosition.x + 'px', 
            top: targetPosition.y + 'px',
            backgroundColor: 'rgba(0, 0, 0, 0.4)'
          }"
        ></view>
        
        <!-- 可移动方块 -->
        <view 
          class="puzzle_block"
          :style="{ 
            left: (slidePosition > containerWidth - 50 ? containerWidth - 50 : slidePosition) + 'px', 
            top: targetPosition.y + 'px',
            backgroundColor: blockColor
          }"
        ></view>
      </view>
      
      <!-- 滑块 -->
      <view class="slider_container">
        <view 
          class="slider_track"
          :style="{ 
            width: (slidePosition > containerWidth - 50 ? containerWidth - 50 : slidePosition) + 'px',
            backgroundColor: slideStatus === 2 ? '#52CCBA' : (slideStatus === 3 ? '#F57A7A' : '')
          }"
        ></view>
        <view 
          class="slider_button" 
          :class="{ 
            'button-success': slideStatus === 2,
            'button-fail': slideStatus === 3,
            'button-moving': slideStatus === 1
          }"
          :style="{ 
            left: (slidePosition > containerWidth - 50 ? containerWidth - 50 : slidePosition) + 'px'
          }"
          @touchstart="slideStart"
          @touchmove="slideMove"
          @touchend="slideEnd"
          @mousedown="mouseDown"
        >
          <view v-if="slideStatus < 2" class="slider-icon">
            <text class="iconfont icon-right-arrow"></text>
          </view>
          <view v-if="slideStatus === 2" class="slider-icon">
            <text class="iconfont icon-check"></text>
          </view>
          <view v-if="slideStatus === 3" class="slider-icon">
            <text class="iconfont icon-close"></text>
          </view>
        </view>
        <view v-if="slideStatus === 0 || slideStatus === 1" class="slider-tip">
          拖动左边滑块完成上方拼图
        </view>
      </view>
      
      <!-- 底部按钮 -->
      <view class="footer_buttons">
        <view @click="cancel" class="footer-icon footer-cancel">
          <text class="iconfont icon-close"></text>
          <text class="button-text">关闭</text>
        </view>
        <view class="spacer"></view>
        <view @click="reset" class="footer-icon">
          <text class="iconfont icon-refresh"></text>
          <text class="button-text">刷新</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, defineComponent, nextTick } from 'vue';

// 确保组件有一个明确的名称
defineComponent({
  name: 'PuzzleVerify'
});

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '安全验证'
  }
});

// 定义组件事件
const emit = defineEmits(['success', 'fail', 'cancel', 'update:visible']);

// 内部状态
const slidebel = ref(false);
const containerWidth = ref(300);
const targetPosition = ref({ x: 0, y: 0 });
const slidePosition = ref(0);
const slideStatus = ref(0); // 0 停止操作 1 触发长按 2 正确 3 错误
const bgColor = ref('#E8F0FE');
const blockColor = ref('#1991FA');
const randomShapes = ref<any[]>([]);
const colorPalette = ['#1991FA', '#52CCBA', '#F57A7A', '#FFB800', '#A0D911'];

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    showVerify();
  } else {
    slidebel.value = false;
  }
}, { immediate: true });

// 组件挂载时初始化
onMounted(() => {
  if (props.visible) {
    showVerify();
  }
});

// 显示验证
function showVerify() {
  slidebel.value = true;
  
  // 等待DOM渲染完成后初始化
  nextTick(() => {
    initContainer();
  });
}

// 初始化容器
function initContainer() {
  try {
    // 获取容器宽度
    const query = uni.createSelectorQuery();
    query.select('#puzzle_container').boundingClientRect(rect => {
      if (rect) {
        containerWidth.value = rect.width;
        // 初始化拼图
        reset();
      } else {
        console.error('无法获取容器尺寸');
        // 使用默认宽度
        reset();
      }
    }).exec();
  } catch (error) {
    console.error('初始化容器失败:', error);
    // 使用默认宽度
    reset();
  }
}

// 重置拼图
function reset() {
  try {
    // 重置状态
    slideStatus.value = 0;
    slidePosition.value = 0;
    
    // 随机设置目标位置
    const maxX = containerWidth.value - 100; // 留出右侧空间
    const maxY = 100; // 限制上下范围在合理区域
    
    targetPosition.value = {
      x: Math.round(Math.random() * maxX + 50), // 确保不要太靠左
      y: Math.round(Math.random() * maxY + 10)  // 确保在合理范围内
    };
    
    // 随机选择背景色
    const bgIndex = Math.floor(Math.random() * colorPalette.length);
    bgColor.value = colorPalette[bgIndex];
    
    // 随机选择方块色
    let blockIndex;
    do {
      blockIndex = Math.floor(Math.random() * colorPalette.length);
    } while (blockIndex === bgIndex); // 确保方块颜色与背景不同
    
    blockColor.value = colorPalette[blockIndex];
    
    // 生成随机形状
    generateRandomShapes();
  } catch (error) {
    console.error('重置拼图失败:', error);
  }
}

// 生成随机形状
function generateRandomShapes() {
  randomShapes.value = [];
  
  try {
    // 生成10-15个随机形状
    const shapeCount = Math.floor(Math.random() * 6) + 10;
    
    for (let i = 0; i < shapeCount; i++) {
      const x = Math.random() * containerWidth.value;
      const y = Math.random() * 150;
      const size = Math.random() * 30 + 10;
      const type = Math.floor(Math.random() * 3); // 0: 圆形, 1: 方形, 2: 菱形
      const colorIndex = Math.floor(Math.random() * colorPalette.length);
      
      randomShapes.value.push({
        x,
        y,
        size,
        type,
        color: colorPalette[colorIndex]
      });
    }
  } catch (error) {
    console.error('生成随机形状失败:', error);
  }
}

// 触摸开始
function slideStart() {
  slideStatus.value = 1;
}

// 触摸移动
function slideMove(e: TouchEvent) {
  if (e.touches && e.touches[0]) {
    try {
      // 获取触摸点的clientX，减去60px的偏移(滑块容器左边距)
      const clientX = e.touches[0].clientX - 60;
      // 限制滑块在有效范围内
      slidePosition.value = clientX < 1 ? 0 : clientX;
    } catch (error) {
      console.error('处理触摸移动事件失败:', error);
    }
  }
}

// 触摸结束，验证滑块位置
function slideEnd() {
  if (slidePosition.value < 1) {
    slideStatus.value = 0;
    return false;
  }
  
  try {
    let finalPosition;
    const maxX = containerWidth.value - 60;
    
    if (slidePosition.value > maxX) {
      finalPosition = maxX;
    } else {
      finalPosition = slidePosition.value;
    }
    
    // 判断滑块位置是否正确(允许15px误差)
    if (Math.abs(targetPosition.value.x - finalPosition) <= 15) {
      // 验证成功
      slideStatus.value = 2;
      slidePosition.value = targetPosition.value.x;
      
      // 延迟关闭验证窗口
      setTimeout(() => {
        slidebel.value = false;
        emit('update:visible', false);
      }, 500);
      
      // 显示成功提示
      uni.showToast({
        icon: 'success',
        title: '验证成功',
      });
      
      // 触发成功事件
      emit('success');
    } else {
      // 验证失败
      slideStatus.value = 3;
      
      // 触发失败事件
      emit('fail');
    }
    
    // 延迟重置滑块状态
    setTimeout(() => {
      slideStatus.value = 0;
      slidePosition.value = 0;
    }, 500);
  } catch (error) {
    console.error('处理触摸结束事件失败:', error);
    slideStatus.value = 0;
    slidePosition.value = 0;
  }
  
  return true;
}

// 鼠标按下(PC端)
function mouseDown(e: MouseEvent) {
  try {
    // 设置状态为拖动中
    slideStatus.value = 1;
    
    // 记录起始X坐标
    const startX = e.clientX;
    const offsetLeft = 60; // 滑块容器左边距
    
    // 添加鼠标移动和抬起事件处理
    const handleMouseMove = (moveEvent: MouseEvent) => {
      try {
        // 计算移动距离
        const newX = moveEvent.clientX - startX;
        
        // 更新滑块位置，保证在有效范围内
        slidePosition.value = Math.max(0, Math.min(newX, containerWidth.value - 50));
      } catch (error) {
        console.error('处理鼠标移动事件失败:', error);
      }
    };
    
    const handleMouseUp = (upEvent: MouseEvent) => {
      try {
        // 移除事件监听
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        
        // 调用触摸结束验证方法
        if (slidePosition.value < 1) {
          slideStatus.value = 0;
          return;
        }
        
        let finalPosition;
        const maxX = containerWidth.value - 60;
        
        if (slidePosition.value > maxX) {
          finalPosition = maxX;
        } else {
          finalPosition = slidePosition.value;
        }
        
        // 判断滑块位置是否正确(允许15px误差)
        if (Math.abs(targetPosition.value.x - finalPosition) <= 15) {
          // 验证成功
          slideStatus.value = 2;
          slidePosition.value = targetPosition.value.x;
          
          // 延迟关闭验证窗口
          setTimeout(() => {
            slidebel.value = false;
            emit('update:visible', false);
          }, 500);
          
          // 显示成功提示
          uni.showToast({
            icon: 'success',
            title: '验证成功',
          });
          
          // 触发成功事件
          emit('success');
        } else {
          // 验证失败
          slideStatus.value = 3;
          
          // 触发失败事件
          emit('fail');
        }
        
        // 延迟重置滑块状态
        setTimeout(() => {
          slideStatus.value = 0;
          slidePosition.value = 0;
        }, 500);
      } catch (error) {
        console.error('处理鼠标抬起事件失败:', error);
        slideStatus.value = 0;
        slidePosition.value = 0;
      }
    };
    
    // 添加鼠标事件监听
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  } catch (error) {
    console.error('处理鼠标按下事件失败:', error);
    slideStatus.value = 0;
  }
}

// 取消验证
function cancel() {
  slidebel.value = false;
  emit('update:visible', false);
  emit('cancel');
}

// 导出组件方法
defineExpose({
  showVerify
});
</script>

<style>
/* 拼图滑动验证样式 */
.slide_model {
  width: 100%;
  height: 100vh;
  z-index: 999;
  position: fixed;
  left: 0;
  top: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
}

.slide_wrapper {
  width: 90%;
  max-width: 400px;
  z-index: 1;
  position: relative;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

/* 顶部标题栏 */
.puzzle_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #f4f4f4;
}

.puzzle_title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.puzzle_close_btn {
  padding: 10rpx;
  color: #666;
}

/* 拼图容器 */
.puzzle_container {
  width: 90%;
  height: 150px;
  position: relative;
  margin: 25rpx auto 0;
  border-radius: 4px;
  overflow: hidden;
}

/* 拼图背景 */
.puzzle_background {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 4px;
  overflow: hidden;
}

/* 随机形状 */
.random_shape {
  position: absolute;
  z-index: 1;
}

/* 被抠方块 */
.puzzle_hole {
  width: 50px;
  height: 50px;
  position: absolute;
  z-index: 2;
  box-shadow: 0 0 5px 2px rgba(255, 255, 255, 0.5) inset;
  border-radius: 4px;
}

/* 可移动方块 */
.puzzle_block {
  width: 50px;
  height: 50px;
  position: absolute;
  left: 0;
  z-index: 3;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5);
  border-radius: 4px;
}

.puzzle_block::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  box-shadow: 0 0 8px 5px rgba(255, 255, 255, 0.4) inset;
  border-radius: 4px;
}

/* 滑块容器 */
.slider_container {
  width: 90%;
  height: 45px;
  background: #f5f5f5;
  text-align: center;
  line-height: 80rpx;
  margin: 20rpx auto;
  position: relative;
  font-size: 26rpx;
  border-radius: 22.5px;
  overflow: hidden;
}

/* 滑条上滑块经过的部分 */
.slider_track {
  position: absolute;
  left: 0;
  top: 0;
  height: 45px;
  background-color: #1991FA;
  width: 0;
  border-radius: 22.5px 0 0 22.5px;
  transition: background-color 0.3s;
}

/* 滑块 */
.slider_button {
  width: 45px;
  height: 45px;
  background-color: #fff;
  font-size: 36rpx;
  font-weight: 700;
  position: absolute;
  left: 0;
  top: 0;
  border: 1px solid #ddd;
  color: #fff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s;
  z-index: 5;
}

.slider_button.button-moving {
  background-color: #1991FA;
}

.slider_button.button-success {
  background-color: #52CCBA;
}

.slider_button.button-fail {
  background-color: #F57A7A;
}

.slider-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.slider-tip {
  position: absolute;
  width: 100%;
  text-align: center;
  color: #999;
  font-size: 26rpx;
  line-height: 45px;
  z-index: 1;
}

/* 底部按钮位置 */
.footer_buttons {
  width: 100%;
  border-top: 1px solid #f4f4f4;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40rpx;
  box-sizing: border-box;
}

.footer-icon {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 32rpx;
  white-space: nowrap;
}

.footer-cancel {
  color: #F57A7A;
}

.button-text {
  margin-left: 6rpx;
  font-size: 26rpx;
}

.spacer {
  flex: 1;
}

/* 图标字体 */
@font-face {
  font-family: "iconfont";
  src: url('data:application/x-font-woff2;charset=utf-8;base64,d09GMgABAAAAAAQYAAsAAAAACJwAAAPKAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHFQGYACDMgqEGINfATYCJAMUCwwABCAFhGcHSRvYB8gekqSrQAEFFMQYUA0Pz2+s+/7MziQK16q4So1SJBHaKJVKpEImFZrX9Nvj4Rvst71z2fxRJanqrOqM56cSYjJFOhMqIRIkEpohk6aZAEw+iM/kVa4IhHNVp6ioWIbfRQcGKKA9MERWgALygRzyGr0OdAPXAzQA9iLVkO4epyOQpzOmAA23C3SCvBhdBYQ5Fp2CfEhtlXXOLOKJhrTpJJ0DPPpfj/+YIYckzYxF3TkwqUPpz9Bncca+b9TbD5Dd+UDbjIw5QCGuy3rO47LQHHLpJRhbDlCXJKmaz9A93DP8M+HfXCZZf3mkJEgRZLRrfU16xTzzK42A56IoktlofhTQ0MmAZyDQ00nh2Uoe9Vf+FJw8fojjqcTjc0ymlX5C3TaHw+rwJ9Ssnc/iYd/lSvO6PX6sKJyXq8MR8HjCbrff6Qy53RG3O2xxxH0+Xwj9nRdGg2i6UfMHi5Fn/EmLGcXJDQbRfgpLKcjxdYqzGUXR1+8L+X0B6YJGx1MnQ6nB4LzBWbJCwqGgJyC5TSG/L9iXY/4w0V8GkF4+2RcM+UPNNEkdVdvjDMEXL2l8qFYoGDKZ/AaDo7m9PSUkzWbrM5lCJpMf5RtLYTD5TFXk8Rc8nW2cNuX8ueZpqx2qxJPYPvH5q6bLXdV38cVz9R//Sn8TbJrGCWzIEpMI+w7jxevz5kY1dNj6G3Kvf47lNFnbWk22Jhe1Vl9x6dRTiDYYVN0eQ3kH3yJrOL51WIu2QnL8JEEGfUWXN4uBWwbdxXPfntRWlunCdZWjv6xCsKcAEWQJ9FiMJ4s6w+f+XUZ9p39Hta9Jx57o7kq3lBfjYoHAPzH4/8j/dJb1QQGc1vO/L8R/C5/eYOC7UzKKpATw4CLHN9LgbK1kqY26aN7pf9VeSDMIEjkDCpJGP2RJP7SQc9BgBJehSbIKeUZm7X2GhCKKDGb1CRBa7UHS4hVkrfZDC/kBDTr9giat4UGedTjbewaTCQWyB0wNTGSEFGZjI1WojZH7K3qQM4FjfVjwAltE+1TGsT7KJCZ80nSIERglVmAJJmEPZsayAkvIGJixO+LKEK+YJDBii2pWrRj1dWCqAiMiCFEwZoNGUgqyYdT9SmvkzAQcpp8n8AJWUeytZDD3jJKRMOL1nA4iBYwSZVdhEUYC98Ayw2LC+WEChgzLLYWLgRyI2iSBHTFLqazsreJ+9w5okB/ZIkWOIo3I6xmqEVzN+tB40cDOBLtIMRIqqzJDd7xOUL4CAAAA') format('woff2');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 24px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-right-arrow:before {
  content: "\e600";
}

.icon-check:before {
  content: "\e645";
}

.icon-close:before {
  content: "\e646";
}

.icon-refresh:before {
  content: "\e647";
}
</style> 